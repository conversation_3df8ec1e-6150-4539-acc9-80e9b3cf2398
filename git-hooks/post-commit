#!/bin/sh

## Copied from https://gist.github.com/koppen/c445fc3e4d39146611b1, modified a little bit

red='\033[0;31m'
green='\033[0;32m'
yellow='\033[0;33m'
NC='\033[0m'

# Check if rubocop is installed for the current project
# rubocop -v >/dev/null 2>&1 || { echo >&2 "${red}[Ruby Style][Fatal]: Add rubocop to your Gemfile"; exit 1; }

TOPLEVEL="$(git rev-parse --show-toplevel)"
FILES="$(git diff head~1 --name-only --diff-filter=AMC | grep "\.rb$" | xargs -I '{}' echo ${TOPLEVEL}/{} | tr '\n' ' ')"

echo "${green}[Ruby Style][Info]: Running Rubocop Auto-correct${NC}"

if [ -n "$FILES" ]
then
	if [ ! -f './web/.rubocop.yml' ]; then
	  echo "${yellow}[Ruby Style][Warning]: No .rubocop.yml config file.${NC}"
	fi

	cd web
	bundle exec rubocop -a ${FILES}
else
	echo "${green}[Ruby Style][Info]: No files to check${NC}"
fi

exit 0

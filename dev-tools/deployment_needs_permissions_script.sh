#!/bin/bash

from=$1
to=$2

if [[ "$from" == "" ]] ; then
  echo usage: $0 [from_version] [to_version]
  exit 0
fi

if [[ "$to" == "" ]] ; then
  echo must specify a both a from and to version
  exit 1
fi

cd $(dirname $0)/..

fail() {
  echo $1 1>&2
  exit 1
}

determine_git() {
  if [[ $1 =~ ^[0-9]+\.[0-9]+$ ]] ; then
    git branch --remote | grep "^ *origin/release/$1\$" 2>&1 1>/dev/null
    if [[ "$?" -ne "0" ]] ; then
      echo "Could not find branch $1" 1>&2
      return 1
    fi
    echo "origin/release/$1"
    return 0
  elif [[ $1 =~ ^[0-9]+\.[0-9]+\.[.0-9A-Za-z\-]+$ ]] ; then
    git tag | grep "^ *v$1\$" 2>&1 1>/dev/null
    if [[ "$?" -ne "0" ]] ; then
      echo "Could not find tag $1" 1>&2
      return 1
    fi

    echo "v$1"
    return 0
  else
    echo "Didn't understand $1; please specify a release number or tag" 1>&2
    return 1
  fi
}

echo -n "Updating repo with \"git fetch\"..."
git fetch 1>&2 2>/dev/null
if [[ "$?" -ne "0" ]] ; then
  echo "failed; please update repo manually and then re-run script"
  exit 1
fi
echo "done!"

git_from=$(determine_git $from) || exit 1
git_to=$(determine_git $to) || exit 1

difflines=`git diff ${git_from} ${git_to} web/app/services/role_service/permissions.rb | wc -l | sed -e 's/ *//'`

if [[ "$difflines" == "0" ]] ; then
  echo permissions.rb did not change between $git_from and $git_to
else
 echo permissions.rb DID CHANGE between $git_from and $git_to\; please run script
fi


#!/bin/bash

usage() {
  echo "usage: $0 [source] [destination]" 1>&2
  echo "Sources may be a release like \"1.52\", a project like \"proj-role-permission-r4\", or can just be \"master\"" 1>&2
}

fail() {
  echo $1 1>&2
  usage
  exit 1
}

determine_branch() {
  if [[ $1 =~ [0-9]+\.[0-9]+ ]] ; then
    echo "release/$1"
    return 0
  elif [[ $1 =~ proj[a-zA-Z0-9\-]+ ]] ; then
    echo "$1/base"
    return 0
  elif [[ "$1" == "master" ]] ; then
    echo $1
    return 0
  else
    return 1
  fi
}

if [[ "$1" == "" ]] ; then
  usage
  exit 0
fi

branch1=$(determine_branch $1) || fail "error: unrecognized branch $1"
branch2=$(determine_branch $2) || fail "error: unrecognized branch $2"

branch1_alt=$(echo $branch1 | sed -e 's/\//-/')
branch2_alt=$(echo $branch2 | sed -e 's/\//-/')

set -e

bold=$(tput bold)
normal=$(tput sgr0)

echo "${bold}=== Cloning ujet-server repo ===${normal}"
TMPDIR=`mktemp -d`
cd $TMPDIR
<NAME_EMAIL>:UJET/ujet-server.git
cd ujet-server

echo "${bold}=== Creating merge branch ===${normal}"
MERGE_BRANCH="merge/${branch1_alt}-into-${branch2_alt}"
git checkout $branch1
git checkout $branch2
git checkout -b $MERGE_BRANCH
set +e
git merge --no-ff --no-commit ${branch1}
set -e
git reset -- web/VERSION
git checkout --ours web/VERSION

set +e
git status | grep "both modified" 2>&1 1>/dev/null
if [[ $? == 0 ]] ; then
  set -e
  mkdir -p $TMPDIR/bin
  cat > $TMPDIR/bin/continue-merge <<EOF
touch ${TMPDIR}/continue
EOF
  chmod +x $TMPDIR/bin/continue-merge
  export PATH="$TMPDIR/bin:${PATH}"

  echo
  echo $bold
  echo "Dropping into subshell to allow you to resolve merge conflicts."
  echo $normal
  echo "When finished, run ${bold}continue-merge${normal} and then exit the shell to continue the merge."
  echo
  echo
  $SHELL

  if [[ ! -f "${TMPDIR}/continue" ]] ; then
    echo "${bold}you didn't run \"continue-merge\"; aborting merge${normal}"
    exit 2
  fi
fi
set -e

echo "${bold}=== Finishing merge and pushing ===${normal}"
git commit -m "Merge ${branch1} into ${branch2} ($(date +"%Y%m%d"))"
git push -u origin $MERGE_BRANCH
open https://github.com/UJET/ujet-server/compare/$branch2...$MERGE_BRANCH

echo "${bold}=== Cleaning up ===${normal}"
rm -rf ${TMPDIR}
echo
echo "${bold}Done!${normal}"

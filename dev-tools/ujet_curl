#!/usr/bin/env python3

import getopt
import requests
import urllib
import urllib3
import sys
import os
import json
import re
import jwt
import time
import uuid

# Causes requests not to spew SSL warnings about self-signed certificates
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def usage():
  print(f"usage: {sys.argv[0]} [-u USERNAME] [-p PASSWORD] [-X METHOD] [-f] url", file=sys.stderr)
  print(f" -t: type, one of [admin, end_user] (assumed to be \"admin\" by default)", file=sys.stderr)
  print(f" -u: user for which to log in (<EMAIL> if missing), used for admin APIs", file=sys.stderr)
  print(f" -p: password (\"123456AB!@\" if missing), used for admin APIs", file=sys.stderr)
  print(f" -s: secret (\"secret\" if missing), used for end user APIs", file=sys.stderr)
  print(f" -X: method (assumes GET if missing)", file=sys.stderr)
  print(f" -d: content to post (assumes Content-Type of application/json)", file=sys.stderr)
  print(f" -f: format and sort output (assumes JSON)", file=sys.stderr)
  print(f" -b: output as binary data (e.g. for piping to another tool)", file=sys.stderr)

  sys.exit(1)

def read_json_file(path):
  with open(path, 'r') as j:
    contents = json.loads(j.read())
    return contents

def do_request(session, method, path, content=None):
  if method == 'GET':
    return session.get(path, verify=False)
  elif method == 'PUT':
    return session.put(path, verify=False, json=content)
  elif method == 'POST':
    return session.post(path, verify=False, json=content)
  elif method == 'DELETE':
    return session.delete(path, verify=False)

def print_output(res, binary, format_output):
  if binary:
    sys.stdout.buffer.write(res.content)
    sys.stdout.flush()
  else:
    output = res.text
    if format_output:
      try:
        output = json.dumps(res.json(), sort_keys=True, indent=4, separators=(',', ': '))
      except:
        pass
    print(output)


def main():
  username = "<EMAIL>"
  password = "123456Ab!@"
  secret = "secret"
  method = "GET"
  format_output = False
  content = None
  binary = False
  curl_type = 'admin'

  try:
    opts, args = getopt.getopt(sys.argv[1:], 't:u:p:s:X:d:fb')
  except getopt.GetoptError as err:
    print(f"error: {err}", file=sys.stderr)
    usage()
  for o, a in opts:
    if o == '-t':
      curl_type = a
    if o == '-u':
      username = a
    elif o == '-p':
      password = a
    elif o == '-s':
      secret = a
    elif o == '-X':
      method = a.upper()
    elif o == '-d':
      try:
        content = read_json_file(a)
      except json.decoder.JSONDecodeError as err:
        print(f"error: malformed JSON. {err}", file=sys.stderr)
        usage()
    elif o == '-f':
      format_output = True
    elif o == '-b':
      binary = True

  # Type must be 'admin' or 'end_user'
  if not (curl_type == 'admin' or curl_type == 'end_user'):
    print(f"error: type specified in -t flag was invalid", file=sys.stderr)
    usage()

  # Cannot have both binary and format
  if format_output and binary:
    print(f"error: -f and -b flags are mutually exclusive", file=sys.stderr)
    usage()

  # Validate method
  if method not in ["GET", "POST", "PUT", "DELETE"]:
    print(f"error: unrecognized HTTP method {method}", file=sys.stderr)
    usage()

  if len(args) < 1:
    usage()

  path = args[0]
  base_path = urllib.parse.urljoin(path, "/")

  if curl_type == 'admin':
    # Get X-XSRF-TOKEN
    session = requests.session()
    res = session.get(f"{base_path}v1/auth/status", verify=False)
    session.headers.update({ 'X-XSRF-TOKEN': urllib.parse.unquote(res.headers['XSRF-TOKEN']) })

    # Sign in
    res = session.post(f"{base_path}v1/auth/sign_in", verify=False, json={"user":{"username":username, "password":password}, "hash_key":None})
    session.headers.update({ 'X-XSRF-TOKEN': urllib.parse.unquote(res.headers['XSRF-TOKEN']) })

    res = do_request(session, method, path, content)
    print_output(res, binary, format_output)
  elif curl_type == 'end_user':
    token = jwt.encode({"name": "ujet_curl", "iss": "ujet", "iat": time.time()}, secret, algorithm="HS256")

    # Assume company_id is subdomain
    # TODO: fix this at some point
    m = re.match(r"^.*://(\w*)\..*", base_path)
    if m:
      company_id = m[1]
    else:
      print(f"error: unable to determine company ID from path", file=sys.stderr)
      exit(1)

    # Sign in
    session = requests.session()
    res = session.post(f"{base_path}api/v2/auth/token", verify=False, json=
      {
        "end_user": {
          "token": token,
          "company_id": company_id,
          "device": {
            "device_user_agent": "ujet_curl",
            "device_uuid": str(uuid.uuid4())
          }
        }
      }
    )
    auth_token = res.json()["auth_token"]
    session.headers.update({ 'Authorization': f"Bearer {auth_token}" })

    res = do_request(session, method, path, content)
    print_output(res, binary, format_output)

if __name__ == "__main__":
  main()

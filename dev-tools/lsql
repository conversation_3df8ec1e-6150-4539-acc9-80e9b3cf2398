#!/bin/bash

ENV_FILE="$(dirname $0)/../web/.env"

if [[ ! -r "$ENV_FILE" ]] ; then
  echo "Could not read env file \"$ENV_FILE\"" 1>&2
  exit 1
fi

MYSQL_PWD="$(grep -e "^MYSQL_PASSWORD" $ENV_FILE | sed -e 's/^MYSQL_PASSWORD=//' | sed -e "s/'//g" | sed -e 's/"//g')"

if [[ "$MYSQL_PWD" == "" ]] ; then
  echo "Couldn't find variable MYSQL_PASSWORD in env file \"$ENV_FILE\"; maybe you haven't set it?" 1>&2
  exit 2
fi

export MYSQL_PWD
exec mysql -u root $@

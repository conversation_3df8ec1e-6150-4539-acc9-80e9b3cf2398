# stackhawk configuration for applicationName
app:
  # An applicationId obtained from the StackHawk platform.
  applicationId: 05b59197-e9b1-4754-b502-3a4cc9814331
  # The environment for the applicationId defined in the StackHawk platform.
  env: Pre-Production
  # The url of your application to scan
  host: https://zdco.ujetqa.co
  autoPolicy: true
  autoInputVectors: true
  authentication:
    loggedInIndicator: ".*2[0-9][0-9].*OK.*|.*3[0-9][0-9].*"
    loggedOutIndicator: ".*4[0-9][0-9].*Unauthorized.*"
    external:
      type: TOKEN
      value: ${AUTH_TOKEN}
    tokenAuthorization:
      type: HEADER
      value: Authorization
      tokenType: Basic
    testPath:
      path: /v1/dashboard/queue_report_groups
      success: ".*200.*"
      requestMethod: GET
  
hawk:
  spider:
    ajax: true
    maxDurationMinutes: 10

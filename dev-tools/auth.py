#!/usr/bin/python3

import os
import sys
import json
import logging
import requests

from urllib.parse import unquote

# EXAMPLE BELOW
# This hawk.rc file contains the stackhawk API key
#. ~/.hawk/hawk.rc
#docker pull stackhawk/hawkscan:latest
# this hosts file is to set the hostname in the docker container
#cp hosts.orig hosts
#sed -ie "s?@@CHANGEME@@?$(ifconfig | grep 192\.168\.86 | awk '{print $2}')?g" hosts
#export AUTH_TOKEN=$(./auth.py) && docker run -e AUTH_TOKEN=${AUTH_TOKEN}  --network host -v $(pwd)/hosts:/etc/hosts -e API_KEY=$HAWK_API_KEY --rm -v $(pwd):/hawk:rw -it stackhawk/hawkscan:latest

BASE_URL=sys.argv[1]

def signIn(s, x):
    data = { 'user': {
        'username': sys.argv[2],
        'password': sys.argv[3]
        }
    }

    endpoint = BASE_URL + "/v1/auth/sign_in"
    H = {
        'X-XSRF-TOKEN': x,
        'Content-Type': 'application/json'
        }
    s.headers=H
    data = json.dumps(data)
    r = s.request(method='POST',
                  url=endpoint,
                  headers=H,
                  data=data
                  )

    a = r.headers['set-cookie'].split(';')
    for i in a:
        i = i.strip()
        t = i[:18]
        if t == "Secure, auth_token":
            auth_token = i[19:]    
    return auth_token


def setXsrf(headers):
    xsrf = False
    for h in headers:
        header = headers[h]
        if h.lower() != 'set-cookie':
            continue
        cookieArr = header.split(';')
        for c in cookieArr:
            cookie = c.split('=')
            if cookie[0] != 'XSRF-TOKEN':
                continue
            xsrf = unquote(cookie[1])
    if xsrf:
        return xsrf


def doAuth():
    requests.packages.urllib3.disable_warnings()
    endpoint = BASE_URL + "/v1/auth/status"
    s = requests.Session()
    r = s.get(endpoint, verify=False)
    x = setXsrf(r.headers)
    si = signIn(s, x)
    print(si.strip("\n"))

if __name__ == '__main__':
    doAuth()


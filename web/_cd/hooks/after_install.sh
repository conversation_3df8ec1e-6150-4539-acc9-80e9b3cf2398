#!/bin/bash -l

set -xe

DEPLOY_DIR="/srv/ujet/was"
ONDECK_DIR="${DEPLOY_DIR}/ondeck"

export DEPLOY_NG=1
export RUN_MIGRATIONS=0
export ASG_DEPLOY=1


# Import all environment specific variables from SSM is not possible because export generates wrong format of the .env file
#for var in $(chamber export --format=dotenv prj01_ujet)
#do
#  export $var
#done

# Chamber service name is equal to the deployment group except the diff between dash and underscore.
service_name=$(echo $DEPLOYMENT_GROUP_NAME | tr - _)
export CRM_CLI_ENABLED=$(chamber read ${service_name} crm_cli_enabled -q)
CONTRIBSYS_KEY=$(chamber read ${service_name} contribsys_key -q)

cd $ONDECK_DIR

rvm use ujet_server

bundle config enterprise.contribsys.com $CONTRIBSYS_KEY
bundle install --path $DEPLOY_DIR/shared/bundle --without development test --deployment

bundle exec cap local deploy $DRY_RUN
bundle exec cap local deploy:project_branch $DRY_RUN

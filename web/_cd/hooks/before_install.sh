#!/bin/bash

set -xe

UJET_DIR="/srv/ujet"
DEPLOY_DIR="${UJET_DIR}/was"
ONDECK_DIR="${DEPLOY_DIR}/ondeck"

wait_file() {
  local file="$1"; shift
  local wait_seconds="${1:-10}"; shift # 10 seconds as default timeout

  until test $((wait_seconds--)) -eq 0 -o -f "$file" ; do sleep 1; done

  ((++wait_seconds))
}

# include hidden files when using star in bash commands
shopt -s dotglob

wait_file "${UJET_DIR}/.initialized" 60 || {
  echo "Server is not initialized after 60 seconds. Exiting"
  exit 1
}
mkdir -p $ONDECK_DIR
rm -rf $ONDECK_DIR/*

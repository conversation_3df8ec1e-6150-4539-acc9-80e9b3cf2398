#!/bin/bash -l

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"
source "$SCRIPT_DIR/nvm"

REQ_VER=$1

case $REQ_VER in
  "10")
    NODE_VER=10.15.1
    NPM_VER=6.13.4
    LTS_VER="lts/dubnium"
    echo "Node version $REQ_VER was requested. We will translate this to: ($NODE_VER/$NPM_VER)"
    ;;
  "14")
    # use generic v14 here instead of pinning to specific version, because Dockerfile's FROM node:14 will not be pinned like EC2 is
    NODE_VER=14
    NPM_VER=6.14
    LTS_VER="lts/fermium"
    echo "Node version $REQ_VER was requested. We will translate this to: ($NODE_VER/$NPM_VER)"
    ;;
  "18")
    NODE_VER=18
    NPM_VER=10.8.2
    LTS_VER="lts/hydrogen"
    echo "Node version $REQ_VER was requested. We will translate this to: ($NODE_VER/$NPM_VER)"
    ;;
  *)
    NODE_VER=10.15.1
    NPM_VER=6.13.4
    LTS_VER="lts/dubnium"
    echo "Running npm script with defaults: ($NODE_VER/$NPM_VER)"
    ;;
esac

function force_reinstall_node {
  # ensure we have *a* version of node installed
  nvm install $LTS_VER
  nvm uninstall v$NODE_VER || true # this is expected to fail if it's the same version we just installed OR the version's not installed at all yet
  nvm install v$NODE_VER
  nvm alias default v$NODE_VER
  nvm exec $NODE_VER npm install -g npm@$NPM_VER
  nvm exec $NODE_VER npm install -g gulp
}

# Ensure we can use the expected NODE_VER
nvm use v$NODE_VER || force_reinstall_node
# Force reinstall node if npm is missing
which npm || force_reinstall_node

# Just in case we have npm but it's not the correct version
if [ "$(npm --version)" != "$NPM_VER" ]; then
  nvm exec $NODE_VER npm install -g npm@$NPM_VER
fi

nvm exec $NODE_VER npm ci

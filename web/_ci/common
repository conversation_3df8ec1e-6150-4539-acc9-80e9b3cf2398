#!/bin/bash -l

set -o errexit

ruby_version=$(cat .ruby-version || cat ../.ruby-version)
ruby_version=${ruby_version%\\n}

gems_version=$(cat .gems-version || cat ../.gems-version)
gems_version=${gems_version%\\n}

ruby_gemset=$(cat .ruby-gemset || cat ../.ruby-gemset)
ruby_gemset=${ruby_gemset%\\n}

bundler_version=$(cat .bundler-version || cat ../.bundler-version)
bundler_version=${bundler_version%\\n}

artifact_dir=jenkins/artifacts

function get_md5 {
  # MacOS has a different md5sum binary
  if [ "$(uname -s)" = "Darwin" ]; then
    (md5 ${1} | awk '{print $4}') || echo
  else
    (md5sum ${1} | awk '{print $1}') || echo
  fi
}


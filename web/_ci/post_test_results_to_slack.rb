# frozen_string_literal: true

RAILS_TEST_RESULTS_PATH = 'tmp/spec_summary.log'
CRM_SERVER_TEST_RESULTS_PATH = 'crm-server/results/crm-server.json'
CRM_ADAPTOR_TEST_RESULTS_PATH = 'crm_adaptor/results/crm_adaptor.json'

require 'net/https'
require "json"

def parse_json_results(path)
  return nil unless File.file?(path)

  file = File.open(path)
  JSON.load(file)
end

def parse_rspec_parallel_summary(path)
  return nil unless File.file?(path)

  summaries = []

  found_summary = false
  file = File.open(path)
  file.each_line do |line|
    if found_summary
      match = /^(?<examples>\d+) examples, (?<failures>\d+) failures(, (?<pending>\d+) pending)?$/.match(line)
      summaries.last.merge!({
        tests: match[:examples].to_i,
        failures: match[:failures].to_i,
        pending: match[:pending]&.to_i || 0
      })
    end

    found_summary = line.start_with? 'Finished in '

    if found_summary
      match = /^Finished in ((?<minutes>[0-9]+) minutes )?(?<seconds>[0-9\.]+) seconds?/.match(line)
      summaries << { duration: (match[:minutes]&.to_i || 0) * 60 + match[:seconds].to_i }
    end
  end

  summaries.each_with_object({ tests: 0, failures: 0, pending: 0, duration: 0 }) do |summary, overall|
    overall[:tests] += summary[:tests]
    overall[:failures] += summary[:failures]
    overall[:pending] += summary[:pending]
    overall[:duration] = [overall[:duration], summary[:duration]].max
  end
end

test_results = {
  rails: parse_rspec_parallel_summary(RAILS_TEST_RESULTS_PATH),
  crm_server: parse_json_results(CRM_SERVER_TEST_RESULTS_PATH),
  crm_adaptor: parse_json_results(CRM_ADAPTOR_TEST_RESULTS_PATH)
}

header_blocks = [
  {
    "type": "section",
    "text": {
      "type": "plain_text",
      "text": "ujet-server - #{ENV['CHANGE_BRANCH'] || ENV['BRANCH_NAME']}"
    },
    "accessory": {
      "type": "button",
      "text": {
        "type": "plain_text",
        "text": "Jenkins Job",
        "emoji": true
      },
      "url": ENV['BUILD_URL']
    }
  }
]

results_blocks = []
if !test_results[:rails] && !test_results[:crm_server] && !test_results[:crm_adaptor]
  results_blocks << {
    "type": "section",
    "text": {
      "type": "mrkdwn",
      "text": "@here build failed before tests started"
    }
  }
else
  test_summary = {}

  if test_results[:rails]
    rails_results = test_results[:rails]
    test_summary[:rails] = {
      tests: rails_results[:tests],
      failures: rails_results[:failures],
      pending: rails_results[:pending],
      duration: rails_results[:duration]
    }
  end
  
  if test_results[:crm_server]
    crm_server_results = test_results[:crm_server]
    test_summary[:crm_server] = {
      tests: crm_server_results['stats']['tests'],
      failures: crm_server_results['stats']['failures'],
      skipped: crm_server_results['stats']['skipped'],
      duration: crm_server_results['stats']['duration'].fdiv(1000)
    }
  end
  
  if test_results[:crm_adaptor]
    crm_adaptor_results = test_results[:crm_adaptor]
    test_summary[:crm_adaptor] = {
      tests: crm_adaptor_results['stats']['tests'],
      failures: crm_adaptor_results['stats']['failures'],
      skipped: crm_adaptor_results['stats']['skipped'],
      duration: crm_adaptor_results['stats']['duration'].fdiv(1000)
    }
  end
  
  def trim(num)
    i, f = num.to_i, num.to_f
    i == f ? i : f
  end
  
  results_blocks = %i[rails crm_server crm_adaptor].each_with_object([]) do |test_project, accum|
    if test_summary[test_project]
      test_project_summary = test_summary[test_project]
      passed_total = test_project_summary[:tests] - test_project_summary[:failures]
      pass_rate = (passed_total.to_f/test_project_summary[:tests] * 100).round(2)
      pass_rate_string = "#{passed_total}/#{test_project_summary[:tests]} - #{trim(pass_rate)}%"
      additional_details_string = %i[skipped pending].each_with_object([]) do |key, details|
        unless test_project_summary[key].nil? || test_project_summary[key] == 0
          details << " - (#{test_project_summary[key]} #{key})"
        end
      end.join
      duration_display_value = test_project_summary[:duration] > 2 ? test_project_summary[:duration].round : test_project_summary[:duration].round(2)
  
      accum.concat [
        {
          "type": "header",
          "text": {
            "type": "plain_text",
            "text": test_project,
            "emoji": true
          }
        },
        {
          "type": "section",
          "fields": [
            {
              "type": "mrkdwn",
              "text": "*Pass Rate*\n#{pass_rate_string + additional_details_string}"
            },
            {
              "type": "mrkdwn",
              "text": "*Duration*\n#{duration_display_value} s"
            }
          ]
        }
      ]
  
      if test_project_summary[:failures] > 0
        accum << {
          "type": "section",
          "text": {
            "type": "mrkdown",
            "text": "<!here> Tests have failed"
          }
        }
      end
    else
      accum.concat [
        {
          "type": "header",
          "text": {
            "type": "plain_text",
            "text": test_project,
            "emoji": true
          }
        },
        {
          "type": "section",
          "text": {
            "type": "mrkdwn",
            "text": "Test results not found"
          }
        }
      ]
    end
  end
end

uri = URI(ENV['SLACK_WEBHOOK_URL'])
https = Net::HTTP.new(uri.host, uri.port)
https.use_ssl = true
req = Net::HTTP::Post.new(uri.path, 'Content-Type' => 'application/json')
post_body = { blocks: header_blocks + results_blocks }
req.body = post_body.to_json
https.request(req).body

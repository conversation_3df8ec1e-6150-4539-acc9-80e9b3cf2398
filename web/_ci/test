#!/bin/bash -l

set -o errexit

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"

export FIREBASE_ADMIN_PRIVATE_KEY=$(mktemp)
function cleanup {
  rm -f $FIREBASE_ADMIN_PRIVATE_KEY
  rm -f .env
}

trap 'cleanup' EXIT TERM

set +e
source "${HOME}/.rvm/scripts/rvm"
rvm use ${ruby_version}@${ruby_gemset}
set -e

bundle check

[ -z "${BUILD_TAG}" ] && {
  echo "BUILD_TAG must be set."
  exit 1
}

# This number should match the one set in setup
export PARALLEL_TEST_PROCESSORS=${PARALLEL_TEST_PROCESSORS:-12}

BUILD_LABEL=${BUILD_TAG/\//_}
BUILD_LABEL=${BUILD_LABEL//\%2F/_}

db_instance_id=$(docker ps | grep "mysql-${BUILD_LABEL}" | awk '{print $1}')
[ -n "${db_instance_id}" ]
mysql_port=$(docker port "${db_instance_id}" 3306 | cut -d: -f2)

redis_instance_id=$(docker ps | grep "redis-${BUILD_LABEL}" | awk '{print $1}')
[ -n "${redis_instance_id}" ]
redis_port=$(docker port "${redis_instance_id}" 6379 | cut -d: -f2)

export REDIS_URL=redis://127.0.0.1:${redis_port}/15
export SIDEKIQ_REDIS_URL=redis://127.0.0.1:${redis_port}/8
export CRM_REDIS_URL=redis://127.0.0.1:${redis_port}/1
export MYSQL_PORT=${mysql_port}
export AWS_S3_BUCKET=ujet-qa

chamber export ci_ujet_firebase > $FIREBASE_ADMIN_PRIVATE_KEY
chamber export ci_ujet -f dotenv > .env

UJET_LOG_LEVEL=fatal RAILS_ENV=test PARALLEL_TEST_PROCESSORS=$PARALLEL_TEST_PROCESSORS bundle exec rake parallel:spec

mysqladmin -h 127.0.0.1 -P ${mysql_port} -u root shutdown

echo "Setup .npmrc"
NPMRC_FILE=~/.npmrc
if [ -n "$GITHUB_READ_PACKAGES_TOKEN" ]; then
  if ! grep -q _authToken $NPMRC_FILE 2>/dev/null; then
    echo "... Adding _authToken to $NPMRC_FILE"
    echo "//npm.pkg.github.com/:_authToken=$GITHUB_READ_PACKAGES_TOKEN" >> $NPMRC_FILE
  fi
fi

# CRM Server
echo "Entering crm-server directory"
pushd crm-server
echo "Running npm script"
$SCRIPT_DIR/npm 10
nvm exec 10 npm test
nvm exec 10 npm run lint
$SCRIPT_DIR/npm 14
nvm exec 14 npm test
nvm exec 14 npm run lint
popd

# CRM Adaptor
pushd crm_adaptor
$SCRIPT_DIR/npm 10
nvm exec 10 npm test
nvm exec 10 npm run coverage
nvm exec 10 npm run lint
$SCRIPT_DIR/npm 14
nvm exec 14 npm test
nvm exec 14 npm run coverage
nvm exec 14 npm run lint
popd

#!/bin/bash -l

set -o errexit

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"

export DOCKER_USERNAME=$(/usr/local/bin/chamber read -q ci_ujet docker_username)
export DOCKER_PASSWORD=$(/usr/local/bin/chamber read -q ci_ujet docker_password)

echo "$DOCKER_PASSWORD" | docker login --username "$DOCKER_USERNAME" --password-stdin
docker pull mysql:5.7
docker pull redis:3.2

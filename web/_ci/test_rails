#!/bin/bash -l

set -o errexit

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"

export FIREBASE_ADMIN_PRIVATE_KEY=$(mktemp)
function cleanup {
  rm -f $FIREBASE_ADMIN_PRIVATE_KEY
}

trap 'cleanup' EXIT TERM

set +e
source "${HOME}/.rvm/scripts/rvm"
rvm use ${ruby_version}@${ruby_gemset}
set -e

bundle check

[ -z "${BUILD_TAG}" ] && {
  echo "BUILD_TAG must be set."
  exit 1
}

# This number should match the one set in setup
export PARALLEL_TEST_PROCESSORS=${PARALLEL_TEST_PROCESSORS:-12}

BUILD_LABEL=${BUILD_TAG/\//_}
BUILD_LABEL=${BUILD_LABEL//\%2F/_}

db_instance_id=$(docker ps | grep "mysql-${BUILD_LABEL}" | awk '{print $1}')
[ -n "${db_instance_id}" ]
mysql_port=$(docker port "${db_instance_id}" 3306 | cut -d: -f2)

redis_instance_id=$(docker ps | grep "redis-${BUILD_LABEL}" | awk '{print $1}')
[ -n "${redis_instance_id}" ]
redis_port=$(docker port "${redis_instance_id}" 6379 | cut -d: -f2)

export REDIS_URL=redis://127.0.0.1:${redis_port}/15
export SIDEKIQ_REDIS_URL=redis://127.0.0.1:${redis_port}/8
export CRM_REDIS_URL=redis://127.0.0.1:${redis_port}/1
export MYSQL_PORT=${mysql_port}
export AWS_S3_BUCKET=ujet-qa

chamber export ci_ujet_firebase > $FIREBASE_ADMIN_PRIVATE_KEY

# get changed lines, excluding dump timestamp and mysql info
added_lines=$( \
  git diff --color=always \
  | perl -wlne 'print $1 if /^\e\[32m\+\e\[m\e\[32m(.*)\e\[m$/' \
  | grep -v 'Dump completed on' \
  | grep -v 'Database:' \
  | grep -v 'Dumping routines for database' \
  | grep -v 'Table name:' \
  | grep -v 'MySQL dump' \
  | grep -v 'Server version' \
) || true

removed_lines=$( \
  git diff --color=always \
  | perl -wlne 'print $1 if /^\e\[31m-(.*)\e\[m$/' \
  | grep -v 'Dump completed on' \
  | grep -v 'Database:' \
  | grep -v 'Dumping routines for database' \
  | grep -v 'Table name:' \
  | grep -v 'MySQL dump' \
  | grep -v 'Server version' \
) || true

# ensure there are no changed lines
if [ -n "$added_lines$removed_lines" ]; then
  echo "[FATAL] ensure schema and annotations are up to date!"
  echo "see UJET-24820 for more details: https://ujetcs.atlassian.net/browse/UJET-24820"
  echo "running 'ujet.sh clean_dump' script should resolve this error"
  exit 1
fi

# Do static analysis
echo "STARTING STATIC ANALYSIS"
bundle exec brakeman --no-progress --no-pager -q
echo "FINISHED STATIC ANALYSIS"

# run valkey cluster test with real instance
echo "STARTING CLUSTER TEST"
REDIS_URL="redis://127.0.0.1:${redis_port}/13" \
STATS_REDIS_URL="redis://127.0.0.1:${redis_port}/13" \
REDIS_CLUSTER_NODES="redis://127.0.0.1:7000" \
REDIS_DATASTORE_CLUSTER=true \
RAILS_ENV=test \
bundle exec rails audit_cluster_keys
echo "FINISHED CLUSTER TEST"

SPEC_DIRS=(`find . -type d -name spec`)

set +e
focused_specs=`find ${SPEC_DIRS[@]} -iname "*_spec.rb" | xargs grep -E 'focus: *true'`
set -e

if [[ -n "$focused_specs" ]]; then
  echo "[FATAL] some spec tests contain the 'focus: true' tag"
  echo "Ensure all uses of 'focus: true' are removed from spec tests before merging"
  echo "Files with violations:"
  echo "$focused_specs"
  exit 1
fi

UJET_LOG_LEVEL=fatal RAILS_ENV=test PARALLEL_TEST_PROCESSORS=$PARALLEL_TEST_PROCESSORS bundle exec rake ujet_parallel:spec

mysqladmin -h 127.0.0.1 -P ${mysql_port} -u root shutdown

#!/bin/bash -l

set -o errexit

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"

set +e
source "${HOME}/.rvm/scripts/rvm"
rvm use ${ruby_version}@${ruby_gemset} --create
set -e

bundle check

[ -z "${BUILD_TAG}" ] && {
  echo "BUILD_TAG must be set."
  exit 1
}

export PARALLEL_TEST_PROCESSORS=${PARALLEL_TEST_PROCESSORS:-12}

BUILD_LABEL=${BUILD_TAG/\//_}
BUILD_LABEL=${BUILD_LABEL//\%2F/_}

db_instance_id=$(docker run -d -e MYSQL_ALLOW_EMPTY_PASSWORD=true --name "mysql-${BUILD_LABEL}" --tmpfs /var/lib/mysql -P mysql:5.7)
[ -n "${db_instance_id}" ]
mysql_port=$(docker port "${db_instance_id}" 3306 | cut -d: -f2)
echo "Found mysql_port=${mysql_port}"

redis_instance_id=$(docker run -d --name "redis-${BUILD_LABEL}" -P redis:3.2)
[ -n "${redis_instance_id}" ]
redis_port=$(docker port "${redis_instance_id}" 6379 | cut -d: -f2)
echo "Found redis_port=${redis_port}"

nodes=()
for i in $(seq 0 5); do
  nodes+=("valkey-${i}-${BUILD_LABEL}")
done
node_names="127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 127.0.0.1:7004 127.0.0.1:7005"
for i in ${!nodes[@]}; do
  if [ "${i}" = "5" ]; then
    creator="yes"
  else
    creator="no"
  fi
  port="700${i}"
  valkey_instance_id=$(docker run -d --name "${nodes[$i]}" --net=host -e "VALKEY_PORT_NUMBER=${port}" -e "ALLOW_EMPTY_PASSWORD=yes" -e "VALKEY_AOF_ENABLED=no" -e "VALKEY_CLUSTER_ANNOUNCE_IP=127.0.0.1" -e "VALKEY_NODES=${node_names}" -e "VALKEY_CLUSTER_DYNAMIC_IPS=no" -e "VALKEY_CLUSTER_REPLICAS=1" -e "VALKEY_CLUSTER_CREATOR=${creator}" bitnami/valkey-cluster:8.0.1)
  [ -n "${valkey_instance_id}" ]
done

export REDIS_URL=redis://127.0.0.1:${redis_port}
export MYSQL_PORT=${mysql_port}

tries=0
set +e
while (! (echo "select 1 from dual;" | mysql -h 127.0.0.1 -P ${mysql_port} -u root > /dev/null 2>&1) ); do
  echo "Waiting 2 secs to connect to mysql"
  sleep 2
  tries=$(( tries + 1 ))
  [ $tries -gt 10 ] && exit 3
done
set -e

echo "Create .env"
chamber export ci_ujet -f dotenv > .env

RAILS_ENV=test PARALLEL_TEST_PROCESSORS=$PARALLEL_TEST_PROCESSORS bundle exec rake parallel:create
echo "alter database ujet_test character set utf8 collate utf8_general_ci;" | mysql -h 127.0.0.1 -P ${mysql_port} -u root
for i in $(seq 2 $PARALLEL_TEST_PROCESSORS); do
  echo "alter database ujet_test${i} character set utf8 collate utf8_general_ci;" | mysql -h 127.0.0.1 -P ${mysql_port} -u root
done
RAILS_ENV=test PARALLEL_TEST_PROCESSORS=$PARALLEL_TEST_PROCESSORS bundle exec rake parallel:migrate

# ensure data_migrations table exists
RAILS_ENV=test bundle exec rake data:version
# update structure.sql
RAILS_ENV=test bundle exec rake db:schema:dump
# update model annotations
RAILS_ENV=test bundle exec rake annotate_models

echo "Setup .npmrc"
NPMRC_FILE=~/.npmrc
if [ -n "$GITHUB_READ_PACKAGES_TOKEN" ]; then
  if ! grep -q _authToken $NPMRC_FILE 2>/dev/null; then
    echo "... Adding _authToken to $NPMRC_FILE"
    echo "//npm.pkg.github.com/:_authToken=$GITHUB_READ_PACKAGES_TOKEN" >> $NPMRC_FILE
  fi
fi

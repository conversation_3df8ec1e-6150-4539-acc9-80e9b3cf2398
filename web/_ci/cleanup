#!/bin/bash -l

set -o errexit
 
[ -z "${BUILD_TAG}" ] && {
  echo "BUILD_TAG must be set."
  exit 1
}

BUILD_LABEL=${BUILD_TAG/\//_}
BUILD_LABEL=${BUILD_LABEL//\%2F/_}
 
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common"

ACCOUNT_ALIAS=$(aws iam list-account-aliases | jq -r '.AccountAliases[0]' || echo)
BUCKET_NAME="ujet-artifacts--${ACCOUNT_ALIAS}--uswe2"
GEMFILE_LOCK_MD5=$(get_md5 Gemfile.lock)
BUNDLE_FILENAME="bundle-${GEMFILE_LOCK_MD5}.tar.xz"
BUNDLE_S3_KEY="bundles/${BUNDLE_FILENAME}"
artifact_dir=${artifact_dir:-.}
mkdir -p ${artifact_dir}
LOCAL_BUNDLE="${artifact_dir}/${BUNDLE_FILENAME}"
 
set +e
source "${HOME}/.rvm/scripts/rvm"
rvm use ${ruby_version}@${ruby_gemset}

eval "S3_MD5=$( (aws s3api head-object --bucket ${BUCKET_NAME} --key ${BUNDLE_S3_KEY} 2>/dev/null || echo "{\"ETag\": \"\\\"\\\"\"}") | jq -r '.ETag' || echo )"

if [ ! -f "${LOCAL_BUNDLE}" ]; then
  if [ -n "${S3_MD5}" ]; then
    echo "--> ${BUNDLE_FILENAME} exists on S3 with md5: ${S3_MD5}, we won't upload ours."
  else
    echo "--> ${BUNDLE_FILENAME} does not exist on S3, we will upload ours."
    rm -f ${artifact_dir}/bundle*
    cp Gemfile Gemfile.lock .ruby-version .ruby-gemset .gems-version ${artifact_dir}/
    tar -cJf ${LOCAL_BUNDLE} .bundle
    if [ $? -ne 0 ]; then
      echo "---> tar failed, will not upload ${LOCAL_BUNDLE}"
    else
      ETAG=$(openssl md5 -binary ${LOCAL_BUNDLE} | base64)
      echo "---> Uploading ${LOCAL_BUNDLE} to S3 with checksum ${ETAG}"
      aws s3api put-object --bucket ${BUCKET_NAME} \
        --key ${BUNDLE_S3_KEY} \
        --body ${LOCAL_BUNDLE} \
        --metadata md5chksum=${ETAG} \
        --content-md5 ${ETAG}
    fi
  fi
else
  echo "--> No local ${BUNDLE_FILENAME} at ${LOCAL_BUNDLE} to upload"
fi

function stop_docker {
  docker stop $(docker ps -qf "name=${1}") 2>/dev/null || echo "--> Can't stop ${1} because it doesn't appear to be running."
}

function rm_docker {
  docker rm $(docker ps -qaf "name=${1}") 2>/dev/null || echo "--> Can't remove ${1} because I can't find it."
}
 
stop_docker "mysql-${BUILD_LABEL}"
rm_docker "mysql-${BUILD_LABEL}"
stop_docker "redis-${BUILD_LABEL}"
rm_docker "redis-${BUILD_LABEL}"
rm .env
 
bundle check
exit 0

#!/bin/bash -l

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"

set +e
if (! test -e ~/.rvmrc || ! grep -q rvm_silence_path_mismatch_check_flag ~/.rvmrc); then
  echo 'rvm_silence_path_mismatch_check_flag=1' >> ~/.rvmrc
fi
if (! test -e ~/.rvmrc || ! grep -q rvm_auto_reload_flag ~/.rvmrc); then
  echo 'rvm_auto_reload_flag=1' >> ~/.rvmrc
fi
 
if [[ ! -s "${HOME}/.rvm/scripts/rvm" ]]; then
  curl -sSL https://get.rvm.io | bash -s head --path "${HOME}/.rvm"
fi
source "${HOME}/.rvm/scripts/rvm"

rvm list strings | grep "^${ruby_version}" || {
  rvm autolibs read-fail
  rvm install "${ruby_version}"
}

rvm use "${ruby_version}"
GEMS_VERSION=$(gem --version | grep "${gems_version}" || echo)
if [ -z "$GEMS_VERSION" ]; then
  rvm rubygems --force "${gems_version}"
else
  echo "Found rubygems $GEMS_VERSION"
fi

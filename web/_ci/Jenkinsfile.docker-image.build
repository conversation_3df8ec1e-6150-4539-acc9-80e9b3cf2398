import groovy.json.JsonOutput;

def docker_image

def AWS_ACCOUNT_ID = ************
def AWS_REGION = 'us-west-2'

def microserviceList = [
  crmServer: 'web/crm-server/Dockerfile',
  crmCli: 'web/crm-cli/Dockerfile'
]
def ecrRepos = [
  crmServer: 'crm-server',
  crmCli: 'crm-cli'
]

/** Which branches should we upload the docker image to ECR? */
def uploadImageOnBranches(branchName) {
  def gitBranch = branchName.replaceAll("origin/", "")
  return gitBranch == "master" || gitBranch ==~ /proj.*\/base/
}

def dockerImages = [:]

pipeline {
  agent any

  stages {
    stage('Checkout') {
      steps {
        checkout scm
      }
    }

    stage('Build Image') {
      steps {
        script {
          sh ecrLogin()
          withCredentials([string(credentialsId: 'github_read_packages_token', variable: 'GITHUB_READ_PACKAGES_TOKEN')]) {
            microserviceList.each { microserviceName, dockerfile ->
              dockerImages[microserviceName] = docker.build(ecrRepos[microserviceName], " -f ${dockerfile} --build-arg github_read_packages_token=${GITHUB_READ_PACKAGES_TOKEN} ./web")
            }
          }
        }
      }
    }

    stage('Push Image') {
      steps {
        echo 'Pushing docker image to the remote repository'
        script {
          sh ecrLogin()

          metadata_build_job  = "${env.JOB_NAME}"
          metadata_invoked_by = "${env.BUILD_USER_ID}"
          metadata_branch     = "${env.GIT_BRANCH}"
          metadata_builder    = sh(script: 'whoami', returnStdout: true).trim()
          metadata_build_id   = "${env.BUILD_ID}"
          metadata_gch        = sh(
                                script: "echo $GIT_BRANCH | sed -e \'s/origin\\///g\' | sed -e \'s/\\//-/g\'",
                                returnStdout: true).trim() + "-" + sh(
                                script: 'git --no-pager rev-parse --short HEAD',
                                returnStdout: true).trim()

          writeFile(file: 'metadata.json',
                    text: JsonOutput.prettyPrint(JsonOutput.toJson([
                            metadata_build_job: metadata_build_job,
                            metadata_invoked_by: metadata_invoked_by,
                            metadata_branch: metadata_branch,
                            metadata_builder: metadata_builder,
                            metadata_build_id: metadata_build_id,
                            metadata_gch: metadata_gch])))
          if (uploadImageOnBranches(env.GIT_BRANCH)) {
            dockerImages.each { microserviceName, dockerfile ->
              docker.withRegistry("https://${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ecrRepos[microserviceName]}") {
                dockerImages[microserviceName].push(metadata_build_id)
                dockerImages[microserviceName].push(metadata_gch)
                dockerImages[microserviceName].push("latest")
              }
            }
          }
        }
      }
    }

    stage('Update k8s-env repo') {
      steps {
        script {
          sshagent(['github-key']) {
            // If we are updating the master image, then update k8s-env with the new master image tag.
            dockerImages.each { microserviceName, dockerfile ->
              if (uploadImageOnBranches(env.GIT_BRANCH)) {
                println("Running script...")
                sh "web/_ci/update-k8s-env https://${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${ecrRepos[microserviceName]} ${GIT_BRANCH} ${ecrRepos[microserviceName]}"
              } else {
                println("We do not build docker images for this branch. Skipping...")
              }
            }
          }
        }
      }
    }
  }

  post {
    always {
      archiveArtifacts artifacts: 'metadata.json', fingerprint: true
      deleteDir()
    }
    success { echo 'Build succeeded!'; }
    failure { echo 'Build failed!'; }
  }
}

// vim: syntax=groovy :

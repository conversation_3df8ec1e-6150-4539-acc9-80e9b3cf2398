#!/bin/bash -l

set -o errexit

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/common"

export FIREBASE_ADMIN_PRIVATE_KEY=$(mktemp)
function cleanup {
  rm -f $FIREBASE_ADMIN_PRIVATE_KEY
}

trap 'cleanup' EXIT TERM

[ -z "${BUILD_TAG}" ] && {
  echo "BUILD_TAG must be set."
  exit 1
}

# This number should match the one set in setup
export PARALLEL_TEST_PROCESSORS=${PARALLEL_TEST_PROCESSORS:-12}

BUILD_LABEL=${BUILD_TAG/\//_}
BUILD_LABEL=${BUILD_LABEL//\%2F/_}

redis_instance_id=$(docker ps | grep "redis-${BUILD_LABEL}" | awk '{print $1}')
[ -n "${redis_instance_id}" ]
redis_port=$(docker port "${redis_instance_id}" 6379 | cut -d: -f2)

export CRM_REDIS_URL=redis://127.0.0.1:${redis_port}/1
export AWS_S3_BUCKET=ujet-qa

chamber export ci_ujet_firebase > $FIREBASE_ADMIN_PRIVATE_KEY

# CRM Server
echo "Entering crm-server directory"
pushd crm-server
echo "Running npm script"
$SCRIPT_DIR/npm 14
nvm exec 14 npm run coverage:unit
nvm exec 14 npm run lint
popd


# CRM Adaptor
echo "Entering crm_adaptor directory"
pushd crm_adaptor
$SCRIPT_DIR/npm 14
nvm exec 14 npm run coverage
nvm exec 14 npm run lint
popd


#!/bin/bash -l

ECR_REPO=$(echo $1 | sed -e 's/https:\/\///g')
BRANCH=$(echo $2 | sed -e 's/origin\///g')
MICROSERVICE_NAME=$3

echo "Current Branch is: $BRANCH"

BRANCH_PATH=${BRANCH%/base}

UJET_BRANCH_NAME="$(echo $BRANCH | sed -e 's/\//-/g')-$(git --no-pager rev-parse --short HEAD)"

<NAME_EMAIL>:UJET/k8s-env.git
cd k8s-env

mkdir -p config/images/$MICROSERVICE_NAME/$BRANCH_PATH
echo "# NOTE: This file is automatically generated by <PERSON>. DO NOT MODIFY or else
# your changes will be overridden.

bases:
- ../../../ujet-services/$MICROSERVICE_NAME

images:
- name: $MICROSERVICE_NAME
  newName: $ECR_REPO
  newTag: $UJET_BRANCH_NAME" > config/images/$MICROSERVICE_NAME/$BRANCH_PATH/kustomization.yaml

git add config/images
git commit -m "Jenkins automatically update master tag to $BRANCH"

cat config/images/$MICROSERVICE_NAME/$BRANCH_PATH/kustomization.yaml

# Feels like a race condition could happen with multiple pipelines committing
# to git at the same time. Adding this pull request to hopefully avoid the
# chances of a collision.
git pull
git push

cd ..
rm -rf k8s-env

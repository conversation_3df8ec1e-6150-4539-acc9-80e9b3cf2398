pipeline {
    agent { label 'swarm' }
    options {
        timestamps()
        parallelsAlwaysFailFast()
    }
    triggers {
        githubPush()
    }

    stages {
        stage('init') {
            steps {
                script {
                    // Hack to allow new builds to cancel old builds
                    // https://issues.jenkins-ci.org/browse/JENKINS-43353
                    def buildNumber = BUILD_NUMBER as int
                    if (buildNumber > 1) {
                        milestone(buildNumber - 1)
                    }
                    milestone(buildNumber)
                }
            }
        }

        stage('checkout') {
            steps {
                checkout scm
            }
        }

        stage('prep') {
            steps {
                dir('web') {
                    sh '''
                        _ci/prep
                    '''
                }
            }
        }

        stage('ruby') {
            steps {
                dir('web') {
                    sh '''
                        _ci/ruby
                    '''
                }
            }
        }

        stage('bundle') {
            steps {
                dir('web') {
                    withCredentials([
                        string(credentialsId: 'Sidekiq-ent', variable: 'SIDEKIQ_REPO_KEY'),
                        string(credentialsId: 'ujet-automation-github-token', variable: 'GITHUB_TOKEN')
                    ]) {
                        sh '''
                            _ci/bundle
                        '''
                    }
                }
            }
        }

        stage('setup') {
            steps {
                dir('web') {
                    withCredentials([string(credentialsId: 'github_read_packages_token', variable: 'GITHUB_READ_PACKAGES_TOKEN')]) {
                        wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'XTerm']) {
                            sh '''
                                _ci/setup
                            '''
                        }
                    }
                }
            }
            post {
                failure {
                    dir('web') {
                        sh '''
                            _ci/cleanup
                        '''
                    }
                }
            }
        }

        stage('test') {
            parallel {
                stage('rails') {
                    steps {
                        dir('web') {
                            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'XTerm']) {
                                sh '''
                                    TEST_COVERAGE_ENABLED=true _ci/test_rails
                                '''
                            }
                        }
                    }
                }
                stage('crm') {
                    steps {
                        dir('web') {
                            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'XTerm']) {
                                sh '''
                                    TEST_COVERAGE_ENABLED=true _ci/test_crm
                                '''
                            }
                        }
                    }
                }
                stage('chatbot') {
                    steps {
                        dir('web') {
                            wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'XTerm']) {
                                sh '''
                                    TEST_COVERAGE_ENABLED=true _ci/test_chatbot
                                '''
                            }
                        }
                    }
                }
            }
            post {
                failure {
                    dir('web') {
                        sh '''
                            _ci/cleanup
                        '''
                    }
                }
            }
        }

        stage('cleanup') {
            steps {
                dir('web') {
                    sh '''
                        _ci/cleanup
                    '''
                }
            }
        }
    }

    post {
        always {
            script {
                if (env.BRANCH_NAME == 'master' && currentBuild.result != 'NOT_BUILT') {
                    withCredentials([string(credentialsId: '833d88e0-5802-4ce1-b3f5-0d5beb0ecc81', variable: 'SLACK_WEBHOOK_URL')]) {
                        dir('web') {
                            sh '''
                                _ci/post_test_results_to_slack
                            '''
                        }
                    }
                }
            }
        }
        success {
            echo 'Build succeeded!'
        }
        failure {
            echo 'Build failed!'
        }
    }
}

// vim: syntax=groovy :

pipeline {
    agent { label 'swarm' }
    options {
        timestamps()
    }

    stages {
        stage('checkout') {
            steps {
                checkout scm
            }
        }

        stage('prep') {
            steps {
                dir('web') {
                    sh '''
                        _ci/prep
                    '''
                }
            }
        }

        stage('ruby') {
            steps {
                dir('web') {
                    sh '''
                        _ci/ruby
                    '''
                }
            }
        }

        stage('bundle') {
            steps {
                dir('web') {
                    withCredentials([
                        string(credentialsId: 'Sidekiq-ent', variable: 'SIDEKIQ_REPO_KEY'),
                        string(credentialsId: 'ujet-automation-github-token', variable: 'GITHUB_TOKEN')
                    ]) {
                        sh '''
                            _ci/bundle
                        '''
                    }
                }
            }
        }

        stage('setup') {
            steps {
                dir('web') {
                    sh '''
                        _ci/setup
                    '''
                }
            }
            post {
                failure {
                    dir('web') {
                        sh '''
                            _ci/cleanup
                        '''
                    }
                }
            }
        }

        stage('test') {
            steps {
                dir('web') {
                    wrap([$class: 'AnsiColorBuildWrapper', 'colorMapName': 'XTerm']) {
                        sh '''
                            _ci/test
                        '''
                    }
                }
            }
            post {
                failure {
                    dir('web') {
                        sh '''
                            _ci/cleanup
                        '''
                    }
                }
            }
        }

        stage('cleanup') {
            steps {
                dir('web') {
                    sh '''
                        _ci/cleanup
                    '''
                }
            }
        }
    }

    post {
        success {
            echo 'Build succeeded!'
        }
        failure {
            echo 'Build failed!'
        }
    }
}

// vim: syntax=groovy :

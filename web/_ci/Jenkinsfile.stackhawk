pipeline {
    agent any
    stages {
	stage ("Checkout code") {
	    steps {
		checkout scm
	    }
	}

	stage ("Pull HawkScan Image") {
	    steps {
		sh 'docker pull stackhawk/hawkscan'
	    }
	}

	stage ("Run HawkScan Test") {
	    environment {
		HAWK_API_KEY = credentials('HAWK_API_KEY')
		USERNAME = credentials('QCA01_USERNAME')
		PASSWORD = credentials('QCA01_PASSWORD')
	    }
	    steps {
		dir("dev-tools") {
		    script {
			AUTH_TOKEN = sh returnStdout: true, script: '#!/bin/bash -e\n ./auth.py https://zdco.ujetqa.co ${USERNAME} ${PASSWORD} | tr -d \'\n\''
		    }
		    sh "#!/bin/bash -e\n docker run -v ${WORKSPACE}/dev-tools:/hawk:rw -t -e API_KEY=${HAWK_API_KEY} -e AUTH_TOKEN=${AUTH_TOKEN} -e NO_COLOR=true stackhawk/hawkscan"
		}
	    }
	}
    }
}

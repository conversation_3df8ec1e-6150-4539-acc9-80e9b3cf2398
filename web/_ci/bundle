#!/bin/bash -l

set -o errexit

readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "${SCRIPT_DIR}/common"

ACCOUNT_ALIAS=$(aws iam list-account-aliases | jq -r '.AccountAliases[0]' || echo)
BUCKET_NAME="ujet-artifacts--${ACCOUNT_ALIAS}--uswe2"
GEMFILE_LOCK_MD5=$(get_md5 Gemfile.lock)
GEMFILE_CONFIG_MD5=$(get_md5 .bundle/config-Darwin)
BUNDLE_FILENAME="bundle-${GEMFILE_LOCK_MD5}-${GEMFILE_CONFIG_MD5}.tar.xz"
BUNDLE_S3_KEY="bundles/${BUNDLE_FILENAME}"
artifact_dir=${artifact_dir:-.}
mkdir -p ${artifact_dir}
LOCAL_BUNDLE="${artifact_dir}/${BUNDLE_FILENAME}"

set +e
source "${HOME}/.rvm/scripts/rvm"
rvm use ${ruby_version}@${ruby_gemset}

gem install bundler -v ${bundler_version}

if (! bundle config enterprise.contribsys.com | grep -q "^Set for the current user" && \
    test -n "$SIDEKIQ_REPO_KEY"); then
  bundle config enterprise.contribsys.com $SIDEKIQ_REPO_KEY
fi

bundle config github.com x-access-token:$GITHUB_TOKEN

LOCAL_MD5=$(get_md5 ${LOCAL_BUNDLE})
eval "S3_MD5=$(aws s3api head-object --bucket ${BUCKET_NAME} --key ${BUNDLE_S3_KEY} | jq -r '.ETag' || echo)"

# Check for bundle in S3 but use local bundle if md5 matches
if [ -n "${S3_MD5}" ]; then
  echo "--> Found ${BUNDLE_FILENAME} in S3 with md5: ${S3_MD5}"
  if [ -f "${LOCAL_BUNDLE}" ]; then
    echo "---> Found local ${BUNDLE_FILENAME} with md5: ${LOCAL_MD5}"
    if [ "${LOCAL_MD5}" != ${S3_MD5} ]; then
      echo "----> The MD5 of the local bundle doesn't match S3. Overwriting with bundle from S3"
      eval "aws s3 cp s3://${BUCKET_NAME}/${BUNDLE_S3_KEY} ${LOCAL_BUNDLE}"
    fi
  else
    echo "---> Getting bundle from s3"
    eval "aws s3 cp s3://${BUCKET_NAME}/${BUNDLE_S3_KEY} ${LOCAL_BUNDLE}"
  fi
else
  echo "--> Did not find ${BUNDLE_FILENAME} in S3"
  # There shouldn't be a local bundle if there is none in s3 so delete with prejudice
  rm -f ${LOCAL_BUNDLE}
fi

bundle config --local frozen true
if [ ! -f "${LOCAL_BUNDLE}" ]; then
  bundle check --path "${HOME}/.caches/bundler_${ruby_version}" || {
    echo "--> Running bundle install in ${HOME}/.caches/bundler_${ruby_version}"
    mkdir -p "${HOME}/.caches/bundler_${ruby_version}"
    bundle install --path "${HOME}/.caches/bundler_${ruby_version}"
  }

  bundle check --path ".bundle/cache" || {
    echo "--> Running bundle install in .bundle/cache"
    rm -rf .bundle/cache
    cp -r "${HOME}/.caches/bundler_${ruby_version}" .bundle/cache
    bundle install --path .bundle/cache
    bundle clean
  }
else
  echo "--> Unpacking ${LOCAL_BUNDLE}"
  tar Jxf ${LOCAL_BUNDLE}
fi

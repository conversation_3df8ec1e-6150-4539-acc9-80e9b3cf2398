class V1::ChatPriorityDetailsController < V1::PriorityDetailsController
  before_action :authorize_chat

  private

  def authorize_chat
    authorize Chat
  end

  def sortable_columns
    %w(chat_id) + super
  end

  def load_priority_details
    Chat::PriorityDetail.includes(chat: [:agent, menu_path: { items: { menu: :settings } }], transfer: [{to_menus: :settings}])
  end

  def representer_class
    ChatPriorityDetailRepresenter
  end
end

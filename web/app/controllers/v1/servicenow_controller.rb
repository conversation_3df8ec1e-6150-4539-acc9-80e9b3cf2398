class V1::ServicenowController < ApplicationController
  skip_before_action :authenticate, only: [:oauth_callback]

  def oauth_start
    if current_company.crm_subdomain.blank?
      raise ServiceException, "Please set ServiceNow Domain."
    end

    if current_company.oauth_client_id.blank? || current_company.oauth_client_secret.blank?
      raise ServiceException, "Please set and save the OAuth client credentials first."
    end

    state = AuthToken.issue_token(agent_id: current_user.id)
    redirect_uri = "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/servicenow/oauth_callback"
    tenant = current_company.crm_subdomain
    client_id = current_company.oauth_client_id
    start_url = "https://#{tenant}.service-now.com/oauth_auth.do?response_type=code&redirect_uri=#{redirect_uri}&client_id=#{client_id}&state=#{state}"

    render json: { redirect_url: start_url }
  end

  def oauth_callback
    url = "https://#{URLHelper.host(request, remove_api: true)}"
    url += ":#{Rails.configuration.env[:client_app_port]}" if Rails.configuration.env[:client_app_port]
    url += "/oauth/servicenow/popup"

    if params[:code]
      payload, header = AuthToken.decode(params[:state])
      agent_id = payload["agent_id"]
      data = {
          userId: agent_id,
          serviceNowTenant: current_company.crm_subdomain,
          grantType: 'authorization_code',
          redirectUri: "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/servicenow/oauth_callback",
          clientId: current_company.oauth_client_id,
          clientSecret: current_company.oauth_client_secret,
          code: params[:code],
          state: params[:state],
      }

      # CRM Server will take care of token acquire and store to redis.
      res = ::CRM::CrmServer.oauth_request(url:'/oauth_callback', method: :post, params: data, user_id: agent_id)
      if res && res.dig('error').present?
        url += "?error=#{res.dig('error')}"
      end
    end

    redirect_to url, allow_other_host: true
  end

  private

  def should_verify_authorized
    false
  end
end

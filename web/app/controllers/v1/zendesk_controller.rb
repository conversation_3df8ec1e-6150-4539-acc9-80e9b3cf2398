class V1::ZendeskController < ApplicationController
  skip_before_action :authenticate, only: [:oauth_callback]

  before_action :require_agent, only: [:oauth_start]

  def oauth_start
    if current_company.zendesk_subdomain.nil?
      raise ServiceException, "Please set Zendesk subdomain."
    end

    if current_company.oauth_client_id.nil? or current_company.oauth_client_secret.nil?
      raise ServiceException, "Please set the OAuth client credentials first."
    end

    client = CRM::Zendesk.oauth_client(current_company)
    state = AuthToken.issue_token(agent_id: @agent.id)
    redirect_uri = "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/zendesk/oauth_callback"
    auth_url = client.auth_code.authorize_url(redirect_uri: redirect_uri, scope: 'read write', state:state)

    render json: { redirect_url: auth_url }
  end

  def oauth_callback
    url = "https://" + URLHelper.host(request, remove_api: true)
    url += ":#{Rails.configuration.env[:client_app_port]}" if Rails.configuration.env[:client_app_port]
    url += "/oauth/zendesk/popup"

    if params[:code]
      payload, header = AuthToken.decode(params[:state])
      agent_id = payload["agent_id"]
      agent = User.with_agent_permission.find(agent_id)

      company = Company.current
      client = CRM::Zendesk.oauth_client(company)
      redirect_uri = "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/zendesk/oauth_callback"
      token = client.auth_code.get_token(params[:code], redirect_uri: redirect_uri)
      agent.update_oauth_tokens(
        access_token: token.token,
        instance_url: "https://#{company.zendesk_subdomain}.zendesk.com/api/v2"
      )
    elsif params[:error]
      url += "?error=#{params[:error_description]}"
    end

    redirect_to url, allow_other_host: true
  end

  private

  def should_verify_authorized
    false
  end

end

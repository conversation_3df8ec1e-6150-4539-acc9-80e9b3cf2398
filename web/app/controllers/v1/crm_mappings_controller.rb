class V1::CrmMappingsController < ApplicationController
  before_action :find_crm_mapping, only: [:show, :update, :destroy]
  before_action :authorize_crm_mapping

  def index
    @crm_mappings = CrmMapping.all

    render json: CrmMappingRepresenter.for_collection.prepare(@crm_mappings)
  end

  def show
    render json: CrmMappingRepresenter.new(@crm_mapping)
  end

  def create
    @crm_mapping = CrmMapping.new(permitted_params)

    if @crm_mapping.save
      render json: CrmMappingRepresenter.new(@crm_mapping)
    else
      render json: {errors: @crm_mapping.errors}, status: :bad_request
    end
  end

  def update
    if @crm_mapping.update(permitted_params)
      render json: CrmMappingRepresenter.new(@crm_mapping)
    else
      render json: {errors: @crm_mapping.errors}, status: :bad_request
    end
  end

  def destroy
    if @crm_mapping.destroy
      head :ok
    else
      render json: {errors: @crm_mapping.errors}, status: :bad_request
    end
  end

  # bulk operations
  def bulk_create
    build_mappings
    if @crm_mappings.nil?
      render json: {errors: "Bad CRM mapping format"}, status: :bad_request
      return
    end

    exception = transact_on_crm_mappings(method: 'save!')
    if exception
      render json: {errors: exception.message}, status: :bad_request
      return
    end

    render_all_mappings
  end

  def bulk_update
    raw_data = permitted_bulk_params["crm_mappings"]
    if !raw_data.respond_to?(:each)
      render json: {errors: "Bad CRM mapping format"}, status: :bad_request
      return
    end

    CrmMapping.transaction do
      begin
        raw_data.each do |attrs|
          mapping = CrmMapping.find(attrs["id"])
          mapping.update!(attrs)
        end
      rescue ActiveRecord::StatementInvalid, ActiveRecord::RecordInvalid => exception
        render json: {errors: exception.message}, status: :bad_request
        return
      end
    end

    render_all_mappings
  end

  def bulk_destroy
    find_mappings
    if @crm_mappings.nil?
      render json: {errors: "Bad CRM mapping format"}, status: :bad_request
      return
    end

    if @crm_mappings.empty?
      render json: {errors: "CRM mapping not found"}, status: :not_found
      return
    end

    exception = transact_on_crm_mappings(method: 'destroy')
    if exception
      render json: {errors: exception.message}, status: :bad_request
      return
    end

    render_all_mappings
  end


  private

  def find_crm_mapping
    @crm_mapping = CrmMapping.find(params[:id])
  end

  def authorize_crm_mapping
    authorize @crm_mapping || CrmMapping
  end

  def permitted_params
    params.permit(:name, :crm, keys: permit_recursive_params(params[:keys]))
  end

  def permit_recursive_params(params)
    params.map do |key, value|
      if value.is_a?(Array)
        { key => [ permit_recursive_params(value.first) ] }
      elsif value.is_a?(Hash) || value.is_a?(ActionController::Parameters)
        { key => permit_recursive_params(value) }
      else
        key
      end
    end
  end


  # bulk operations

  def permitted_bulk_params
    params.permit(crm_mappings: [:id, :name, :crm, keys: [:object_name, :object_api, :case_reference_field_api, :object_lookup_field] ])
  end

  def build_mappings
    raw_data = permitted_bulk_params[:crm_mappings]
    return nil unless raw_data.respond_to?(:each)

    @crm_mappings = []
    raw_data.each do |attrs|
      Rails.logger.info("CRM Mapping attributes: #{attrs}")
      @crm_mappings.push(CrmMapping.new(attrs))
    end
    @crm_mappings
  end

  def transact_on_crm_mappings(method:)
    CrmMapping.transaction do
      begin
        @crm_mappings.each do |m|
          m.public_send(method)
        end
      rescue ActiveRecord::StatementInvalid, ActiveRecord::RecordInvalid => exception
        return exception
      end
    end
    nil
  end

  def find_mappings
    raw_data = permitted_bulk_params[:crm_mappings]
    return nil unless raw_data.respond_to?(:each)

    @crm_mappings = []
    raw_data.each do |attrs|
      mapping = CrmMapping.find_by(id: attrs["id"])
      @crm_mappings.push(mapping) if mapping
    end
    @crm_mappings
  end

  def all_mappings
    build_mappings if @crm_mappings.nil?
    @crm_mappings
  end

  def render_all_mappings
    render json: CrmMappingRepresenter.for_collection.prepare(all_mappings)
  end

end
# frozen_string_literal: true

module V1
  class FilesController < ApplicationController
    before_action :skip_authorization
    skip_before_action :authenticate

    before_action :validate_payload, only: [:put, :private]

    def put
      key = decrypted_payload[:key]

      storage = CloudStorage::Factory.create_private
      object = storage.put(
        key:,
        body: params[:file].tempfile,
        content_type: decrypted_payload[:content_type],
        cache_control: decrypted_payload[:cache_control]
      )

      # emulate response format of s3/gcs presigned post
      response = {
        Location: storage.location(key:),
        Bucket: CloudStorage.private_bucket_name,
        Key: key,
        ETag: object.etag
      }

      render xml: response.to_xml(root: 'PostResponse'), status: :created
    rescue StandardError => e
      # Catch any exceptions and return a server error
      logger.error "File upload failed: #{e.message}"
      render json: { error: 'File upload failed' }, status: :internal_server_error
    end

    # GET /v1/files/public/:filename
    def public
      key = params[:filename]

      storage = CloudStorage::Factory.create_public
      return not_found unless storage.exists?(key:)

      send_data storage.get(key:),
                filename: storage.filename(key:),
                type: storage.content_type(key:),
                disposition: :inline
    rescue StandardError => e
      handle_storage_error(e)
    end

    # GET /v1/files/private/:filename?secret=
    # filename is same as the key and is a path.
    # However filename is ignored and decrypted_payload is used instead.
    def private
      storage = CloudStorage::Factory.create_private
      return not_found unless storage.exists?(key: decrypted_payload[:key])

      send_data storage.get(key: decrypted_payload[:key]),
                filename: storage.filename(key: decrypted_payload[:key]),
                type: storage.content_type(key: decrypted_payload[:key]),
                disposition: :inline
    rescue StandardError => e
      handle_storage_error(e)
    end

    private

    def validate_payload
      return not_found if decrypted_payload.blank?
      return not_found if decrypted_payload[:expires_at].blank?
      return not_found if Time.at(decrypted_payload[:expires_at]) < Time.now
    end

    def decrypted_payload
      @decrypted_payload ||= Ujet.encryption_service.decrypt(Base64.urlsafe_decode64(params[:secret]))
    rescue StandardError => e
      logger.error "Decryption failed: #{e.class} - #{e.message}"
      nil
    end

    def handle_storage_error(exception)
      logger.error "Cloud Storage error: #{exception.class} - #{exception.message}"
      not_found
    end

    def not_found
      head :not_found
    end
  end
end

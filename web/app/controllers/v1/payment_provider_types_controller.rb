# frozen_string_literal: true

class V1::PaymentProviderTypesController < ApplicationController
  before_action :authorize_payment_provider_type

  def currencies
    render json: CurrencyRepresenter.for_collection.prepare(Currency.supported(payment_provider_type: type))
  end

  private

  def authorize_payment_provider_type
    authorize :payment_provider_type
  end

  def type
    params.require(:type)
  end
end

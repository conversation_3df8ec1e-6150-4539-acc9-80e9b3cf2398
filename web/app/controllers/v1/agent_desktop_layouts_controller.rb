# frozen_string_literal: true

module V1
  class AgentDesktopLayoutsController < ApplicationController
    before_action :authorize_agent_desktop_layouts
    before_action :require_agent_desktop_layout, only: [:show, :update, :destroy, :duplicate]

    def index
      agent_desktop_layout_params ||= shared_params
      layouts = AgentDesktopLayouts::AgentDesktopLayoutService.fetch_layouts_with_usage(agent_desktop_layout_params)
      render json: agent_desktop_layout_list_representer(layouts)
    end

    def show
      render_agent_desktop_layout
    end

    def create
      layout = AgentDesktopLayouts::AgentDesktopLayoutService.create(permitted_params)
      render json: AgentDesktopLayouts::AgentDesktopLayoutRepresenter.new(layout)
    end

    def destroy
      @agent_desktop_layout.destroy!
      head :ok
    end

    def update
      AgentDesktopLayouts::AgentDesktopLayoutService.update(@agent_desktop_layout, permitted_params)
      render json: AgentDesktopLayouts::AgentDesktopLayoutRepresenter.new(@agent_desktop_layout)
    end

    def duplicate
      duplicated_layout = AgentDesktopLayouts::AgentDesktopLayoutService.duplicate(@agent_desktop_layout)
      render json: AgentDesktopLayouts::AgentDesktopLayoutRepresenter.new(duplicated_layout)
    end

    private

    def render_agent_desktop_layout
      if @agent_desktop_layout
        render json: AgentDesktopLayouts::AgentDesktopLayoutRepresenter.new(@agent_desktop_layout)
      else
        render json: nil
      end
    end

    def require_agent_desktop_layout
      @agent_desktop_layout = AgentDesktopLayouts::AgentDesktopLayout.includes(:agent_desktop_layout_widgets).find(params[:id])
      @agent_desktop_layout.update_in_use
    end

    def shared_params
      shared_params ||= params.permit(
        :parent_id,
        :name,
        :channel_type,
        :directory
      )
      shared_params.merge!(parent_id: nil) if shared_params.blank? || shared_params[:parent_id] == 'null'
      shared_params
    end

    def agent_desktop_layout_list_representer(collection)
      AgentDesktopLayouts::AgentDesktopLayoutRepresenter.for_collection.prepare(collection)
    end

    def permitted_params
      params.permit(:parent_id, :name, :channel_type, :is_external_adapter, :has_tabbed_chat_adapter, :directory,
                    :allow_agent_resize_layout, widget_mapping: {}).tap do |permitted|
        layout_config = params[:layout_config]
        next if layout_config.blank?

        permitted[:layout_config] = if layout_config.is_a?(String)
                                      layout_config
                                    else
                                      layout_config&.to_unsafe_h
                                    end
      end
    end

    def authorize_agent_desktop_layouts
      # only the admin is allowed to use API
      authorize :agent_desktop_layout
    end
  end
end

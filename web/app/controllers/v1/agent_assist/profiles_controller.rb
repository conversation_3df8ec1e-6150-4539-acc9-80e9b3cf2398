class V1::AgentAssist::ProfilesController < ApplicationController
  before_action :require_agent_assist_platform, only: [:index, :create]
  before_action :require_agent_assist_profile, only: [:destroy]
  before_action :authorize_agent_assist_profile

  # GET /v1/agent_assist/platforms/:platform_id/profiles
  def index
    @agent_assist_profiles = @agent_assist_platform.agent_assist_profiles
    render json: ::AgentAssist::ProfileRepresenter.for_collection.prepare(@agent_assist_profiles)
  end

  # POST /v1/agent_assist/platforms/:platform_id/profiles
  def create
    credential_file = params.require(:credential)
    @agent_assist_credential = ::AgentAssist::CredentialFactory.create_or_update_from_service_key!(
      credential: credential_file,
      agent_assist_platform_id: @agent_assist_platform.id
    )

    @agent_assist_profiles = ::AgentAssist::ProfileFactory.create_from_credential!(agent_assist_credential: @agent_assist_credential)
    render json: ::AgentAssist::ProfileRepresenter.new(@agent_assist_profiles.first)
  end

  # DELETE /v1/agent_assist/profiles/:id
  def destroy
    @agent_assist_profile.destroy!
    head :ok
  end

  private

  def authorize_agent_assist_profile
    authorize AgentAssistProfile, policy_class: AgentAssist::BasePolicy
  end

  def require_agent_assist_platform
    @agent_assist_platform = AgentAssistPlatform.includes(:agent_assist_profiles).find(params[:platform_id])
  end

  def require_agent_assist_profile
    @agent_assist_profile = AgentAssistProfile.find(params[:id])
  end
end

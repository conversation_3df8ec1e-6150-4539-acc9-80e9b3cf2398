class V1::AgentAssist::PlatformsController < ApplicationController
  before_action :require_agent_assist_platform, only: %i[show update validate destroy]
  before_action :authorize_agent_assist_platform

  # GET /v1/agent_assist/platforms
  def index
    @agent_assist_platforms = AgentAssistPlatform.all
    render json: ::AgentAssist::PlatformRepresenter.for_collection.prepare(@agent_assist_platforms)
  end

  # GET /v1/agent_assist/platforms/:id
  def show
    render json: ::AgentAssist::PlatformRepresenter.new(@agent_assist_platform)
  end

  # POST /v1/agent_assist/platforms
  def create
    permitted_params_for_create = params.permit(:name, :platform_type)

    name = permitted_params_for_create.require(:name)
    platform_type = permitted_params_for_create.require(:platform_type)

    @agent_assist_platform = ::AgentAssist::PlatformFactory.create!(
      name:          name,
      platform_type: platform_type
    )

    render json: ::AgentAssist::PlatformRepresenter.new(@agent_assist_platform)
  end

  # PATCH | PUT /v1/agent_assist/platforms/:id
  def update
    permitted_params = params.permit(:name, :enabled)

    ::AgentAssist::PlatformService.update(agent_assist_platform: @agent_assist_platform, attrs: permitted_params.to_h)

    render json: ::AgentAssist::PlatformRepresenter.new(@agent_assist_platform)
  end

  # DELETE /v1/agent_assist/platforms/:id
  def destroy
    @agent_assist_platform.destroy!
    head :ok
  end

  # POST /v1/agent_assist/platforms/:id/validate
  def validate
    ::AgentAssist::Validator.new.validate_platform!(agent_assist_platform: @agent_assist_platform)
    render json: ::AgentAssist::PlatformRepresenter.new(@agent_assist_platform)
  end

  private

  def authorize_agent_assist_platform
    authorize AgentAssistPlatform, policy_class: AgentAssist::PlatformPolicy
  end

  def require_agent_assist_platform
    @agent_assist_platform = AgentAssistPlatform.find(params[:id])
  end
end

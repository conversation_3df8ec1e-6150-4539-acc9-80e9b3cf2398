class V1::Stats::QueuesController < ApplicationController
  around_action :use_dashboard_replica
  before_action :authorize_stats_queue

  def index
    attributes = permitted_params.to_h.symbolize_keys

    case attributes[:channel]
    when 'voice_call'
      attributes[:data_columns] = %w(
        type_breakdown
        status_breakdown
        total_volume
        average_volume_per_hour
        average_csat
        lowest_csat
        average_handle_time
        longest_handle_time
        average_wait_time
        longest_wait_time
        average_hold_time
        longest_hold_time
        repeat_contact
      )
    when 'chat'
      attributes[:data_columns] = %w(
        type_breakdown
        status_breakdown
        total_volume
        average_volume_per_hour
        average_csat
        lowest_csat
        average_handle_time
        longest_handle_time
        average_wait_time
        longest_wait_time
        average_response_time
        longest_response_time
        repeat_contact
      )

      attributes[:data_columns] += %w(
        average_duration_sms_chat_inbound
        total_volume_sms_chat_inbound
      ) if FeatureFlag.on?('sms-chat')
    end

    @stats = StatService.queue_list(**attributes.merge(data_scope: data_scope))

    filters = params.permit(:sort_column, :sort_direction, :search_text).to_h
    @stats.data = StatService.sort_and_filter(@stats.data, filters)

    headers["Total"] = @stats.data.size.to_s
    headers["Per-Page"] = params[:per_page] || Kaminari.config.default_per_page.to_s

    @stats.data = StatService.pagination(@stats.data, params[:page], params[:per_page])

    render json: @stats
  end

  private

  def permitted_params
    params.permit(:type, :channel, time_frame: [:from, :to])
  end

  def data_scope
    @data_scope ||= ::Performance::BasePolicy::Scope.new(current_user).resolve
  end

  def authorize_stats_queue
    authorize [:stats, :queue]
  end
end

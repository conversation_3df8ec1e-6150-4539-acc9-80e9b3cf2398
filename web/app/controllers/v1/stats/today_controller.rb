# frozen_string_literal: true

class V1::Stats::TodayController < ApplicationController
  around_action :use_dashboard_replica
  before_action :authorize_stats_today

  def show
    data = TodayStat.gather(
      call_agent_ids: [current_user.id],
      call_menu_ids: current_user.active_queue_relations.select {|q| q.as_q.call? }.map(&:menu_id).uniq,
      chat_agent_ids: [current_user.id],
      chat_menu_ids: current_user.active_queue_relations.select {|q| q.as_q.chat? }.map(&:menu_id).uniq
    )

    render json: data
  end

  private

  def authorize_stats_today
    authorize [:stats, :today]
  end
end

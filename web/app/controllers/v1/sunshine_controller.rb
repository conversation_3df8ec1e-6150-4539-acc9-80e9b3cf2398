class V1::SunshineController < ApplicationController

  skip_before_action :authenticate, if: proc { request.method.to_sym == :HEAD }

  def callback
    SunshineService.process_events(events_from_params)
    head :ok
  end

  protected

  def should_verify_authorized
    false
  end

  private

  def authenticate
    raise ServiceException, 'Sunshine callback request authentication failure' unless
      request.headers["HTTP_X_API_KEY"] == current_company.sunshine_settings.get_webhook_secret
    true
  end

  def events_from_params
    params.fetch(:events, []).map(&:to_unsafe_h)
  end
end

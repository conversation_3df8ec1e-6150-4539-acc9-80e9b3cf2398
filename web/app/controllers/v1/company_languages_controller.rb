class V1::CompanyLanguagesController < ApplicationController
  before_action :authorize_company_language
  before_action :require_language, only: [:show, :update, :destroy, :setup_status]

  def index
    languages = CompanyLanguage.where(company_id: company_id)
    render json: CompanyLanguageRepresenter.for_collection.prepare(languages)
  end

  def create
    @language = CompanyLanguageService.create!(attrs: params.require(:language).permit(:lang, :enabled).to_h)
    render_language
  end

  def update
    @language.update!(params.require(:language).permit(:enabled))
    render_language
  end

  def show
    render_language
  end

  def destroy
    @language = CompanyLanguage.find_by!(company_id: company_id, lang: params.require(:lang))
    CompanyLanguageService.destroy(@language)
    render_language
  end

  def setup_status
    render json: @language.setup_status
  end

  private
  def company_id
    Company.current.id
  end

  def authorize_company_language
    authorize @language || CompanyLanguage
  end

  def require_language
    @language = CompanyLanguage.find_by!(company_id: company_id, lang: params.require(:lang))
  end

  def render_language
    render json: CompanyLanguageRepresenter.new(@language)
  end
end

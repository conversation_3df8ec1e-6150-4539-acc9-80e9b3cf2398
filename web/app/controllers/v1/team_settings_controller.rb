# frozen_string_literal: true

module V1
  class TeamSettingsController < ApplicationController
    before_action :require_team
    before_action :authorize_team_setting
    before_action :require_team_setting
    before_action :get_parent_setting, only: [:show, :update]

    # GET /v1/teams/:team_id/settings
    def show
      render_contact_list
    end

    # PUT /v1/teams/:team_id/settings/contact_list
    def update_contact_list
      @team_setting.update!(permitted_params)
      TeamSettingService::SettingUpdater.new(team_setting: @team_setting, params: permitted_params).update_setting_descendants

      render_contact_list
    end

    private

    def get_parent_setting
      @parent_team_setting = Contact::SettingFinder.find_for_team(params[:team_id].to_i)
    end

    def render_contact_list
      res_contact_list = {
        contact_list_settings: {
          has_parent: @parent_team_setting.present?,
          sources: source,
          source_after_reset: source_after_reset
        }
      }
      render json: res_contact_list
    end

    def source
      setting = @team_setting.contact_override_inheritance && @parent_team_setting ? @parent_team_setting : @team_setting
      {
        team_id: setting.team_id,
        team_name: setting.team.name,
        settings: {
          contact_list_id: setting.contact_list_id,
          accessible_to_global_contact: setting.accessible_to_global_contact,
          contact_override_inheritance: @team_setting.contact_override_inheritance
        }
      }
    end

    def source_after_reset
      return nil unless @parent_team_setting.present?

      {
        team_id: @parent_team_setting.team_id,
        team_name: @parent_team_setting.team.name,
        settings: {
          contact_list_id: @parent_team_setting.contact_list_id,
          accessible_to_global_contact: @parent_team_setting.accessible_to_global_contact
        }
      }
    end

    def require_team_setting
      @team_setting = TeamSetting.find_by!(team_id: params[:team_id])
    end

    def permitted_params
      params.require(:team_settings).permit(:contact_list_id, :contact_override_inheritance, :accessible_to_global_contact)
    end

    def require_team
      @team = Team.find(params.require(:team_id))
    end

    def authorize_team_setting
      authorize @team || Team, policy_class: TeamPolicy
    end
  end
end

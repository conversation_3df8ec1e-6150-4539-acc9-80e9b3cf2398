# frozen_string_literal: true

class V1::Ucaas::MsTeamsController < ApplicationController

  before_action :authenticate, except: ['oauth_callback','oauth_callback_end','presence_listener']

  def oauth_callback

    # auth_code. we will resend it back to ms for token info
    Ucaas::MsTeamsService.new.request_token(params.require(:code), callback_uri('oauth_callback'))

    redirect_to "#{callback_uri('oauth_callback_end')}#success=true&type=ms", allow_other_host: true
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.warn(error: e) { "Error in MS Oauth callback rest : #{e.message}" }
    error_text = ERB::Util.url_encode('Unable to authenticate Microsoft Teams User')
    redirect_to "#{callback_uri('oauth_callback_end')}#success=false&message=#{error_text}&type=ms", allow_other_host: true
  rescue StandardError => e # validation errors, record save error
    Rails.logger.warn(error: e) { "Error in Ms Oauth callback : #{e.message}" }
    message = ERB::Util.url_encode(e.message)
    redirect_to "#{callback_uri('oauth_callback_end')}#success=false&message=#{message}&type=ms", allow_other_host: true
  end

  def oauth_callback_end
    # create subscription upon successful/unsuccessful linking.
    Ucaas::MsTeamsService.new.create_presence_subscription(URLHelper.base_url(request, remove_api: true))

    # Close popup window
    render plain: ''
  end

  def presence_listener

    validation_token = params['validationToken'] || nil
    return render plain: validation_token, status: :ok unless validation_token.nil? # for internal use only

    changed = UserUcaasDataService.handle_presence_notification(params['value'])

    head(changed ? :ok : :accepted)
  end


  protected

  def should_verify_authorized
    false
  end

  private

  def callback_uri(callback_format)
    "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/ucaas/ms_teams/#{callback_format}"
  end

end

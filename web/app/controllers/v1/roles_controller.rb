module V1
  class RolesController < ApplicationController
    rescue_from 'RoleService::TooManyRolesForCompanyError' do |e|
      render json: { message: e.message }, status: :bad_request
    end

    rescue_from 'RoleValidationError' do |e|
      render json: e.message, status: :bad_request
    end

    def create
      authorize :custom_role, :create?
      @role = RoleService::RoleCreator.call(role_attrs_from_params, current_user)
      render_role
    end

    def index
      authorize :custom_role, :index?
      roles = CustomRole.includes(:custom_role_permissions).page(params[:page]).per(params[:per_page])
      add_pagination(roles)

      # render the json, and populate the json with the user counts.
      roles_json = CustomRoleRepresenter.for_collection.prepare(roles).to_hash

      render json: RoleService::UserCounter.call(roles_json)
    end

    def show
      @role = CustomRole.includes(:custom_role_permissions).find(params[:id])
      authorize @role, :show?
      render_role
    end

    def update
      role = CustomRole.find(params[:id])
      authorize role, :update?
      @role = RoleService::RoleUpdater.call(role, role_attrs_from_params, current_user)
      render_role
    end

    def destroy
      @role = CustomRole.find(params[:id])
      authorize @role, :destroy?
      RoleService::RoleDestroyer.call(@role)
      if @role.deleted?
        render_role
      else
        render json: { message: 'Error deleting role.' }, status: :conflict
      end
    end

    private

    def render_role
      render json: CustomRoleRepresenter.new(@role).to_json
    end

    def current_user
      RequestStore.store[:user]
    end

    def role_attrs_from_params
      result = params.require(:custom_role).permit(policy(@role || CustomRole).permitted_attributes)
      # see https://github.com/thoughtbot/til/blob/master/rails/deep_munge.md for why we have to do this.
      result[:assignable_custom_role_ids] = [] if result[:assignable_custom_role_ids].nil?
      result.to_h
    end
  end
end

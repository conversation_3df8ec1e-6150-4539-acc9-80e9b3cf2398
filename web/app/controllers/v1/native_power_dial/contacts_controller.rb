class V1::NativePowerDial::ContactsController < ApplicationController
  before_action :authorize_native_power_dial_contact
  before_action :require_native_campaign

  def create
    phone_number = params.require(:contact).require(:phone)
    @contact = NativePowerDial::ContactService.save_contact(@campaign, phone_number, permitted_params)
    render_contact
  end

  def index
    contacts = @campaign.contacts
    render json: NativePowerDial::ContactRepresenter.for_collection.prepare(contacts)
  end

  def update
    @contact.update!(permitted_params)
    render_contact
  end

  def destroy
    @contact.destroy
    render_contact
  end

  def show
    contact_id = params[:id]
    @contact = @campaign.contacts.find(contact_id)
    render_contact
  end

  def import
    if @campaign.campaign_type != 'default'
      return render json: []
    end

    contacts_to_save = []
    contact_csv_processor = ::NativePowerDial::ContactCsvProcessingService.new(@campaign)
    files = params.require(:files)
    Rails.logger.info("[NativePowerDial] Import Contacts Files Count: #{files.length()}")
    numbers_seen = Set.new([])
    files.each_with_index do |file, index|
      contact_csv_processor.process_csv(file, contacts_to_save, numbers_seen)
    end
    Rails.logger.info("[NativePowerDial] Import contacts_to_save Count: #{contacts_to_save.length()}")
    contact_csv_processor.persist_contacts(contacts_to_save)

    head :ok
  rescue NativePowerDial::ContactService::DuplicateContactPhoneOnCampaign
    if @campaign.contacts.count == 0
      @campaign.destroy
    end
    render json: {message: "Duplicate phone number on campaign."}, status: :bad_request
  rescue NativePowerDial::ContactService::EmptyContactCSV
    render json: {message: "Empty CSV file."}, status: :bad_request
  rescue NativePowerDial::ContactService::CustomFieldForContactExists
    if @campaign.contacts.count == 0
      @campaign.destroy
    end
    render json: {message: "Custom field for this contact already exists."}, status: :bad_request
  rescue Exception => e
    if @campaign.contacts.count == 0
      @campaign.destroy
    end
    render json: {message: "ERROR: #{e}"}, status: :bad_request
  end

  def render_contact
    render json: NativePowerDial::ContactRepresenter.new(@contact)
  end

  def require_native_campaign
    @campaign = ::NativePowerDial::Campaign.find params[:campaign_id]
  end

  def authorize_native_power_dial_contact
    authorize ::NativePowerDial::CampaignContact
  end
end

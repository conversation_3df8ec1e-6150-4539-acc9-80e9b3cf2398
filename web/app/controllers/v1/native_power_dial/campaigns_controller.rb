class V1::NativePowerDial::CampaignsController < ApplicationController
  before_action :authorize_native_power_dial_campaign
  before_action :require_campaign, except: [:create, :index, :queues]

  def create
    if ::NativePowerDial::Campaign.where(name: permitted_params[:name]).exists?
      raise ServiceException, "Duplicated campaign name: %s" % permitted_params[:name]
    end

    campaign_params = permitted_params
    campaign_params[:name] = campaign_params[:name].strip
    @campaign = ::NativePowerDial::Campaign.create!(campaign_params)

    # Create an external list campaign (if necessary)
    if @campaign.campaign_type == 'external_list'
      # For now, let's not fail if we don't get an external campaign ID
      begin
        external_campaign_id = Authority::Api::CreateCampaign.new(@campaign).call
        @campaign.update!(external_campaign_id: external_campaign_id)
      rescue Exception => e
        Rails.logger.error("[CampaignsController] Error #{e}")
      end
    end

    render_campaign
  end

  def show
    render_campaign
  end

  def index
    with_deleted = ActiveRecord::Type::Boolean.new.deserialize(params.fetch(:with_deleted, false))
    if with_deleted
      campaigns = ::NativePowerDial::Campaign.with_deleted
    else
      campaigns = ::NativePowerDial::Campaign.where.not(status: :deleted) 
    end

    if params[:page].present? && params[:per_page].present?
      campaigns = campaigns.page(params[:page]).per(params[:per_page])
      add_pagination(campaigns)
    end

    render json: NativePowerDial::CampaignRepresenter.for_collection.prepare(campaigns)
  end

  def update
    @campaign.refresh_status!

    if @campaign.name != permitted_params[:name] && ::NativePowerDial::Campaign.where(name: permitted_params[:name]).exists?
      raise ServiceException, "Duplicated campaign name: %s" % permitted_params[:name]
    end

    @campaign.with_lock do
      @campaign.update!(permitted_params_without_status)

      status = params.require(:campaign).fetch(:status, nil)

      case NativePowerDial::Campaign.statuses.key(status)
      when 'started'
        @campaign.started!
      when 'pause'
        @campaign.pause!
      when 'finished'
        @campaign.finished!
      end
    end

    render_campaign
  end

  def destroy
    if @campaign.status == "started"
      render json: {message: "Campaign is still running, please pause campaign before deleting it"}, status: :bad_request
    else
      @campaign.delete!
      FirebaseWorker.perform_async(:delete, "campaigns/#{@campaign.id}", nil)
      render_campaign
    end
  end

  def queues
    lang = params.require(:lang)
    includes = [:settings, :voice_call_settings]
    render json: Menu::SetupStatus.get_status(
      menus: Menu::IvrMenu.leaves.includes(*includes),
      lang: lang,
      include_hidden: true
    )
  end

  def require_campaign
    @campaign = ::NativePowerDial::Campaign.find params[:id]
  end

  def permitted_params
    params.require(:campaign).permit(:name,
                                     :mode,
                                     :lang,
                                     :queue_id,
                                     :status,
                                     :ringing_timeout,
                                     :overdial_adjustment_multiplier,
                                     :max_abandonment_percent,
                                     :contact_duplication_check_hours,
                                     :contact_list_type,
                                     :max_redial_count,
                                     :redial_interval_minutes,
                                     :campaign_type,
                                     :schema_id,
                                     :brand_name)
  end

  def permitted_params_without_status
    params.require(:campaign).permit(:name,
                                     :mode,
                                     :lang,
                                     :queue_id,
                                     :status,
                                     :ringing_timeout,
                                     :overdial_adjustment_multiplier,
                                     :max_abandonment_percent,
                                     :contact_duplication_check_hours,
                                     :contact_list_type,
                                     :max_redial_count,
                                     :redial_interval_minutes,
                                     :campaign_type,
                                     :schema_id,
                                     :brand_name)
  end

  def render_campaign
    render json: ::NativePowerDial::CampaignRepresenter.new(@campaign)
  end

  def authorize_native_power_dial_campaign
    authorize ::NativePowerDial::Campaign
  end

  def validate_predictive_campaign_parameters
    if @campaign.blank?
      return unless permitted_params[:mode] == NativePowerDial::Campaign.modes[:predictive]
    end

    if permitted_params[:ringing_timeout].to_i < 1 || permitted_params[:ringing_timeout].to_i > 10
      raise ServiceException, "Ringing timeout should be between 1 to 10"
    elsif permitted_params[:overdial_adjustment_multiplier].to_i < 1 || permitted_params[:overdial_adjustment_multiplier].to_i > 10
      raise ServiceException, "Overdial adjustment multiplier should be between 1 to 10"
    elsif permitted_params[:max_abandonment_percent].to_i < 1 || permitted_params[:max_abandonment_percent].to_i > 50
      raise ServiceException, "Max abandonment percent should be between 1 to 50"
    elsif permitted_params[:max_redial_count].present? && permitted_params[:max_redial_count].to_i < 0 || permitted_params[:max_redial_count].to_i > 100
      raise ServiceException, "Number of redials should be between 0 to 100"
    elsif permitted_params[:redial_interval_minutes].present? && permitted_params[:redial_interval_minutes].to_i < 1 || permitted_params[:redial_interval_minutes].to_i > 40320
      raise ServiceException, "Please enter a number between 1 to 40320"
    end
  end
end

# frozen_string_literal: true

class V1::NativePowerDial::TimezoneSchemasController < ApplicationController

  before_action :authorize_timezone_schemas
  before_action :require_timezone_schema, except: [:create, :index]

  def show
    render json: ::NativePowerDial::Timezone::SchemaRepresenter.new(@schema)
  end

  def create
    timezone_schema = ::NativePowerDial::Timezone::SchemaCreator.new(params).call
    render json: ::NativePowerDial::Timezone::SchemaRepresenter.new(timezone_schema)
  end

  def destroy
    ::NativePowerDial::Timezone::SchemaDestroyer.new(@schema).call
    head :ok
  end 

  def index
    schemas = ::NativePowerDial::CampaignTimeSchema.includes(:rules).page(params[:page]).per(params[:per_page])
    add_pagination(schemas)
    render json: ::NativePowerDial::Timezone::SchemaRepresenter.for_collection.prepare(schemas)
  end

  def update
    timezone_schema = ::NativePowerDial::Timezone::SchemaUpdater.new(params, @schema).call
    render json: ::NativePowerDial::Timezone::SchemaRepresenter.new(timezone_schema)
  end

  def authorize_timezone_schemas
    authorize [:native_power_dial, :campaign_timezone_schema]
  end

 def require_timezone_schema
    @schema = ::NativePowerDial::CampaignTimeSchema.find(params[:id])
    raise ServiceException, 'Schema not found' unless @schema.present? 
  end
end

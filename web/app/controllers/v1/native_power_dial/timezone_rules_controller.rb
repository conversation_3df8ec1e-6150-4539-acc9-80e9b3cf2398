# frozen_string_literal: true

class V1::NativePowerDial::TimezoneRulesController < ApplicationController
  
  before_action :authorize_timezone_rules
  before_action :require_timezone_rule, except: [:create, :index]

  def show
    render json: ::NativePowerDial::Timezone::RuleRepresenter.new(@rule)
  end

  def create
    timezone_rule = ::NativePowerDial::Timezone::RuleCreator.new(params).call
    render json: ::NativePowerDial::Timezone::RuleRepresenter.new(timezone_rule)
  end

  def destroy
    ::NativePowerDial::Timezone::RuleDestroyer.new(@rule).call
    head :ok
  end 

  def index
    rules = ::NativePowerDial::CampaignTimeRule.page(params[:page]).per(params[:per_page])
    add_pagination(rules)
    render json: ::NativePowerDial::Timezone::RuleRepresenter.for_collection.prepare(rules)
  end

  def update
    timezone_rule = ::NativePowerDial::Timezone::RuleUpdater.new(params, @rule).call
    render json: ::NativePowerDial::Timezone::RuleRepresenter.new(timezone_rule)
  end

  def require_timezone_rule
    @rule = ::NativePowerDial::CampaignTimeRule.find(params[:id])
    raise ServiceException, 'Rule not found' unless @rule.present? 
  end

  def authorize_timezone_rules
    authorize [:native_power_dial, :campaign_timezone_rule]
  end
end

# frozen_string_literal: true

module V1
  class AgentAnnouncementsController < ApplicationController
    ACTIVE = "active".freeze
    EXPIRED = "expired".freeze
    ASSIGN_ALL_AGENTS = 1.freeze

    before_action :authorize_agent_announcement
    before_action :require_agent_announcement, only: [:show]
    before_action :validate_assignment, only: [:create, :update]

    def list_announcements
      page = params.fetch(:page, 1).to_i
      per_page = params.fetch(:per_page, 100).to_i
      type = params.fetch(:type, ACTIVE)

      is_active = type == ACTIVE
      offset = (page - 1) * per_page

      announcements = AgentAnnouncements::AgentAnnouncementService.list_announcements(
        offset, per_page, is_active
      )
      total_count = AgentAnnouncements::AgentAnnouncementService.count_announcements(is_active)
      total_pages = (total_count.to_f / per_page).ceil

      response = { 
        type: is_active ? ACTIVE : EXPIRED,
        data: announcements,
        totalPage: total_pages
      }
      response = OpenStruct.new(response)

      render json: AgentAnnouncements::AgentAnnouncementsListRepresenter.new(response)
    end

    def show
      render_agent_announcement
    end

    def create
      announcement = AgentAnnouncements::AgentAnnouncementService.create(permitted_params.to_h)
      render json: AgentAnnouncements::AgentAnnouncementInfoRepresenter.new(announcement)
    end

    def update
      render json: AgentAnnouncements::AgentAnnouncementInfoRepresenter.new(
        AgentAnnouncements::AgentAnnouncementService.update(params[:id], permitted_params.to_h)
      )
    end
    
    def destroy
      AgentAnnouncements::AgentAnnouncementService.destroy(params.require(:id))

      render json: { status: 200 }
    end

    private
    
    def render_agent_announcement
      if @agent_announcement
        render json: AgentAnnouncements::AgentAnnouncementInfoRepresenter.new(@agent_announcement)
      else
        render json: nil
      end
    end

    def require_agent_announcement
      @agent_announcement = AgentAnnouncements::AgentAnnouncement.find(params[:id])
    end

    def authorize_agent_announcement
      # only the admin is allowed to use API
      authorize resource_class
    end

    def resource_class
      AgentAnnouncements::AgentAnnouncement
    end
    

    def permitted_params
      params.require(:agent_announcement).permit(policy(resource_class).permitted_attributes)
    end

    def validate_assignment
      if params[:assignment_all_agents] == ASSIGN_ALL_AGENTS && (params[:assignment_queues].present? || params[:assignment_agents].present? || params[:assignment_teams].present?)
        raise ArgumentError, 'Cannot assign queues/agents/teams when assigning all agents'
      end
    end
  end
end

class V1::ProactiveChatTriggersController < ApplicationController
  wrap_parameters include: ProactiveChatTrigger.attribute_names + ["messages"]

  before_action :require_trigger, only: [:show, :update, :destroy]
  before_action :authorize_proactive_chat_trigger

  def index
    @triggers = ProactiveChatTrigger.includes(:messages).page(params[:page]).per(params[:per_page])
    add_pagination(@triggers)
    render json: ProactiveChatTriggerRepresenter.for_collection.prepare(@triggers)
  end

  def show
    render_trigger
  end

  def create
    @trigger = ProactiveChatTrigger.new(permitted_params.except(:messages))
    update_trigger_messages
    @trigger.save!
    render_trigger

  rescue ActiveRecord::RecordNotUnique
    render json: {message: "Name has already been taken"}, status: :bad_request
  end

  def update
    @trigger.attributes = permitted_params.except(:messages)
    update_trigger_messages
    @trigger.save!
    render_trigger

  rescue ActiveRecord::StaleObjectError
    @trigger.reload
    message = "Another user has updated that record since you accessed."
    representer = ProactiveChatTriggerRepresenter.new(@trigger)
    render json: {message: message, record: representer}, status: :conflict
  end

  def destroy
    @trigger.destroy!
    render_trigger
  end

  private

  def authorize_proactive_chat_trigger
    authorize @trigger || ProactiveChatTrigger
  end

  def require_trigger
    @trigger = ProactiveChatTrigger.includes(:messages).find(params[:id])
  end

  def permitted_params
    # for keywords_included and keywords_excluded, both a scalar or an array value is permitted
    # because they can be an array of strings or nil.
    @permitted_params ||= begin
    def normalize_keywords(key, fallback = nil)
      value = params[key]
      return fallback unless value.present?
      return value if value.is_a?(Array)
      fallback
    end

    keywords_included_or = normalize_keywords(:keywords_included_or)
    keywords_excluded_or = normalize_keywords(:keywords_excluded_or)

    has_included_or = params.has_key?(:keywords_included_or)
    has_excluded_or = params.has_key?(:keywords_excluded_or)

    params.require(:proactive_chat_trigger)
      .permit(:name, :enabled, :stay_time, :visitor_type, :page_visit_count, :menu_id,
        :lock_version, :keywords_included, :keywords_excluded, :lang, :keywords_included_or, :keywords_excluded_or,
        keywords_included: [], keywords_excluded: [], messages: [:lang, :text]
      ).tap do |params|
        # permit only an array or nil for keywords_* parameters.
        if params.has_key?(:keywords_included)
          params[:keywords_included] = nil unless params[:keywords_included].is_a?(Array)
        end

        if params.has_key?(:keywords_excluded)
          params[:keywords_excluded] = nil unless params[:keywords_excluded].is_a?(Array)
        end

        if has_included_or
          params[:keywords_included_or] = keywords_included_or.presence || nil
        end

        if has_excluded_or
          params[:keywords_excluded_or] = keywords_excluded_or.presence || nil
        end
      end
    end
  end

  def update_trigger_messages
    permitted_params.fetch(:messages, []).each do |message_params|
      message = @trigger.messages.find {|message| message.lang == message_params[:lang]}
      if message.present?
        message.attributes = message_params
      else
        @trigger.messages.build(message_params)
      end
    end
  end

  def render_trigger
    render json: ProactiveChatTriggerRepresenter.new(@trigger)
  end
end

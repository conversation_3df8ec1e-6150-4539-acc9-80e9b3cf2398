# frozen_string_literal: true

class V1::AgentDirectoryController < ApplicationController
  before_action :authorize_user

  def index
    permitted_params = params.permit(:name, :status)
    name = permitted_params[:name]
    status = permitted_params[:status]
    status_id = UserStatus.get_by_name(status) if status.present?

    scope = User.with_extension_numbers_only
    scope = scope.with_name(name) if name.present?
    scope = scope.where(status_id:) if status_id.present?

    users = scope.all.where.not(id: current_user.id)

    render json: AgentApi::AgentDirectoryRepresenter.for_collection.prepare(users)
  end

  private

  def authorize_user
    # only the admin is allowed to use API
    authorize @user || User
  end
end

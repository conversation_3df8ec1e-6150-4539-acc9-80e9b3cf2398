# frozen_string_literal: true

module V1
  class AgentDesktopWidgetsController < ApplicationController
    before_action :authorize_agent_desktop_widget
    before_action :require_widget, only: [:show, :update, :destroy]
    before_action :validate_payload, :set_panel_name, only: [:create, :update]

    def index
      agent_desktop_widgets = if params['type'].present?
                                AgentDesktopWidget.includes(:layouts).where(type: params[:type])
                              else
                                AgentDesktopWidget.all
                              end
      render json: AgentDesktopWidgetRepresenter.for_collection.prepare(agent_desktop_widgets)
    end

    def show
      render_widget
    end

    def create
      @agent_desktop_widget = AgentDesktopWidget.create!(permitted_params)
      render_widget
    end

    def update
      @agent_desktop_widget.update!(permitted_params)
      render_widget
    end

    def destroy
      @agent_desktop_widget.destroy!
      head :ok
    rescue StandardError => e
      unhandled_exception(e, @agent_desktop_widget.errors)
    end

    private

    def render_widget
      render json: AgentDesktopWidgetRepresenter.new(@agent_desktop_widget)
    end

    def require_widget
      @agent_desktop_widget = AgentDesktopWidget.includes(:layouts).find(params[:id])
    end

    def permitted_params
      params.permit(:category, :name, :type, configuration: {})
    end

    def authorize_agent_desktop_widget
      authorize :agent_desktop_widget
    end

    def validate_payload
      case params[:type]
      when 'custom_url'
        validate_custom_url
      when 'custom_links'
        validate_custom_links
      end
    end

    def validate_custom_url
      configuration = params[:configuration]

      if configuration['links'].present?
        raise ServiceException, 'Links is only used for Link list panel, not for Custom URL Widget'
      end

      raise ServiceException, 'Url is required with Custom URL Widget' if configuration.dig('url').blank?

      if configuration.dig('panel_names').blank? ||
         !configuration['panel_names'].is_a?(Array)
        raise ServiceException, 'panel_names should be provided with translated name and language'
      end

      duplicates = AgentDesktopLayouts::AgentDesktopWidgetService.get_existing_panel_names(
        configuration['panel_names'], params[:id]
      )

      if duplicates.length > 0
        render json: { message: 'Duplication found', data: duplicates },
               status: :bad_request
      end
    end

    def validate_custom_links
      configuration = params[:configuration]

      raise ServiceException, 'Url is required with Custom Links Widget' if configuration.dig('url').blank?

      if configuration.dig('panel_names').blank? ||
         !configuration['panel_names'].is_a?(Array)
        raise ServiceException, 'panel_names should be provided with translated name and language'
      end

      duplicates = AgentDesktopLayouts::AgentDesktopWidgetService.get_existing_panel_names(
        configuration['panel_names'], params[:id]
      )

      if duplicates.length > 0
        render json: { message: 'Duplication found', data: duplicates },
               status: :bad_request
      end

      if configuration.dig('links').blank? ||
         !configuration['links'].is_a?(Array)
        raise ServiceException, 'Links is required with Custom Links Widget'
      end

      configuration['links'].each do |link|
        raise ServiceException, 'Url is required with Custom Links Widget' unless link['url'].is_a?(String)

        link['translations'].each do |translation|
          unless translation.keys.sort == %w[default lang translated_name].sort &&
                 translation['lang'].is_a?(String) &&
                 translation['translated_name'].is_a?(String) &&
                 translation['default'].in?([true, false]) 
            raise ServiceException, 'Links should be provided with translated name and language'
          end
        end
      end
    end

    def set_panel_name
      return if params[:type] == 'custom_url'
      return if params[:name].present? && params[:action] == 'create'

      params[:name] = params[:configuration]['panel_names'].find { |panel| panel['default'] == true }['translated_name']
    end
  end
end

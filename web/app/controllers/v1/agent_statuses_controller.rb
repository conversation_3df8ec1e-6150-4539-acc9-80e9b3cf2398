# frozen_string_literal: true

class V1::AgentStatusesController < ApplicationController
  around_action :use_read_replica
  before_action :authorize_agent_status

  def index
    ## MEMO: do not call .order method with hash parameter (ex. order(working_status: 'asc'))
    ## when called with hash parameter, ActiveRecord generate sql with 'users.' table name prefix
    ## so it will raise error when try to order by custom selects (ex working_status, total_calls)
    agents = policy_scope(User.with_agent_permission)
    agent_statuses = Stat::QueryAgentStatuses.new(user_scope: agents, query_params: Stat::AgentStatusQueryParams.new(params))
    add_pagination(agent_statuses.query)
    render json: convert_to_representer(agent_statuses.call)
  end

  def summary
    agents = policy_scope(User.with_agent_permission)
    render json: Stat::AgentStatusOverview.new(summary_scope(agents)).to_json
  end

  def combined_summary
    agents = policy_scope(User.with_agent_permission)
    agent_status_query = Stat::QueryAgentStatuses.new(user_scope: agents, query_params: Stat::AgentStatusQueryParams.new(params))
    agent_statuses = Stat::AgentStatuses.new(
      convert_to_representer(agent_status_query.call),
      agent_status_query.query.total_count,
      per_page(agent_status_query.query)
    )
    agent_status_summary = Stat::AgentStatusOverview.new(summary_scope(agents))

    render json: { 'summary' => agent_status_summary, 'agents' => agent_statuses }
  end

  private

  def convert_to_representer(agent_status_data)
    agent_status_data.map do |agent_status|
      AgentStatusRepresenter.new(
        user_and_stat: agent_status[:agent_data],
        daily_stat: agent_status[:daily_agent_stats_data],
        connected_call_queue: agent_status[:connected_call_queue_data],
        assigned_chat_queue: agent_status[:assigned_chat_queue_data],
        assigned_email_queue: agent_status[:assigned_email_queue_data],
        communications: agent_status[:communications],
        internal_call_participants: agent_status[:internal_call_participants]
      )
    end
  end

  def authorize_agent_status
    # only the admin is allowed to use API
    authorize :agent_status
  end

  def policy_scope(user_scope)
    AgentStatusPolicy::Scope.new(current_user, user_scope).resolve
  end

  def summary_scope(user_scope)
    channel = params[:channel]
    user_scope = user_scope.assigned_to_channel(channel: channel) unless channel.nil?
    user_scope
  end
end

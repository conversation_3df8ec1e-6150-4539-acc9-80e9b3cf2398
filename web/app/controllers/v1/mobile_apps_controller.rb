class V1::MobileAppsController < ApplicationController
  before_action :authorize_mobile_app
  before_action :validate_and_encrypt_firebase_service_account_key_params, only: [:create, :update]
  before_action :find_mobile_app, except: [:index, :create]

  def index
    @mobile_apps = MobileApp.order(default: 'DESC')
    render json: MobileAppRepresenter.for_collection.prepare(@mobile_apps)
  end

  def show
    render json: MobileAppRepresenter.new(@mobile_app)
  end

  def create
    @mobile_app = MobileApp.new(permitted_params)
    @mobile_app.set_settings(setting_params.to_h)
    @mobile_app.save!
    render_mobile_app
  end

  def update
    @mobile_app.set_settings(setting_params.to_h)
    @mobile_app.update!(permitted_params)
    render_mobile_app
  end

  def destroy
    @mobile_app.destroy!
    head :ok
  end

  private

  def find_mobile_app
    @mobile_app = MobileApp.find(params[:id])
  end

  def render_mobile_app
    render json: MobileAppRepresenter.new(@mobile_app)
  end

  def authorize_mobile_app
    authorize @mobile_app || MobileApp
  end


  ## When updating, we need to set credentials too. or it will be <PERSON><PERSON><PERSON>
  def permitted_params
    params.permit(
      :name,
      :android_app_identifier,
      :ios_app_identifier,
      :raw_fallback_number,
      :send_sms_to_download_app,
      :enabled,
      :firebase_service_account_key
    )
  end

  def setting_params
    return {} if params[:settings].blank?

    params[:settings].permit(
      :apns_certificate_voip,
      :apns_certificate_general,
      :apns_sandbox, :gcm_key,
      :fallback_number_threshold, :ios_appstore_url, :ios_app_url,
      :android_appstore_url, :android_app_url,
      :prevent_direct_pstn_call,
      :pstn_fallback_enabled
    )
  end

  def validate_and_encrypt_firebase_service_account_key_params
    return true unless params[:firebase_service_account_key]

    valid_key = FirebaseMessaging::FirebaseServiceAccountKeyValidator.new(params[:firebase_service_account_key]).call
    params[:firebase_service_account_key] = Ujet.encryption_service.encrypt(valid_key)
  rescue FirebaseMessaging::InvalidFormatError,
         FirebaseMessaging::MissingRequiredFieldsError,
         FirebaseMessaging::InvalidProjectIdError,
         FirebaseMessaging::InvalidClientEmailError,
         FirebaseMessaging::InvalidUrlError,
         FirebaseMessaging::InvalidPrivateKeyFormatError,
         StandardError => e
    raise InvalidParameterException, "Service Account key: #{e.message}"
  end
end

# frozen_string_literal: true

class V1::AgentDeflectionSettingController < ApplicationController
  before_action :authorize_setting, only: [:index, :user_setting, :team_setting]

  def index
    settings = AgentDeflectionSettingService.new.global_parent_settings
    render json: { parent_settings: settings }
  end

  def user_setting
    settings = AgentDeflectionSettingService.new.user_parent_settings(user_id: params[:id])
    render json: { parent_settings: settings }
  end

  def team_setting
    settings = AgentDeflectionSettingService.new.team_parent_settings(team_id: params[:id])
    render json: { parent_settings: settings }
  end

  def authorize_setting
    authorize :agent_deflection_setting
  end
end

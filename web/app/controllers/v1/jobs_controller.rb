class V1::JobsController < ApplicationController
  def show
    @job = Job.find(params[:id])

    authorize @job

    if @job.completed? and @job.result.present?
      result = case @job.result
               when BulkCompleteUser
                 v1_bulk_users_list_url(@job.result)
               when BulkSmsJob
                 # could not get url_for to work with these jobs even if changing the route or model name, since it
                 # has both create and show routes polymorphic routing (which is what is used when including the v1
                 # modifier) will only ever return the create route, because it is what v1_bulk_sms_jobs gets mapped to.
                 # singular resources get mapped to v1_bulk_sms_job, but polymorphic routing will only ever look for the
                 # v1_bulk_sms_jobs route
                 # also v1_bulk_sm_url is NOT a typo, rails just does a weird thing when a resource name ends with an s
                 v1_bulk_sm_url(@job.result)
               when BulkCompleteContact
                 v1_bulk_contacts_list_url(@job.result)
               when CustomForms::Question
                 v1_form_question_url(@job.result.custom_form, @job.result)
               else
                 url_for([:v1, @job.result])
               end

      render json: @job.as_json(only: [:id, :type, :status]).merge(links: { result: result })
    elsif @job.failed?
      render json: @job.to_json(only: [:id, :type, :status, :error_message])
    else
      render json: @job.to_json(only: [:id, :type, :status])
    end
  end
end

class V1::MenusController < ApplicationController
  before_action :require_menu, only: [:show, :update, :destroy, :status]
  before_action :authorize_menu
  around_action :use_read_replica, only: [:name_only, :queue_view]
  before_action :preload_virtual_agent_assignments, only: [:show, :update, :destroy]

  rescue_from ClosureTreeHierarchyLocking::LockNotAcquired do |_error|
    Rails.logger.warn { 'menu hierarchy lock not acquired' }
    render json: { message: 'Menus are being updated by others now. Please try again later.' },
           status: :service_unavailable
  end

  rescue_from ActiveRecord::RecordNotFound do |_error|
    render json: { message: "Queue's ID Not Found" }, status: :not_found
  end

  def simple
    validate_lang

    type = simple_params[:type]
    languages = simple_params[:lang]&.split(',')
    is_leaf = ActiveRecord::Type::Boolean.new.cast(simple_params[:is_leaf]) || false
    name_query = simple_params[:name_query]

    result = if type.present?
               Menu::RepresenterService.get_simple_menus(type, languages, is_leaf, name_query:)
             else
               Menu::RepresenterService.get_all_simple_menus(languages, name_query:)
             end

    render json: result
  end

  def simple_queues
    validate_lang

    type = params.require(:type)
    languages = params[:lang]&.split(',')
    root = params[:root] =~ /\Atrue\z/i ? true : false
    id = params[:id]

    result = Menu::RepresenterService.get_simple_queues(type, languages, root, id)
    render json: result
  end

  def index
    render json: Menu::RepresenterService.get_menus(params[:type], params[:exclude], params[:tree])
  end

  def social_messaging_types
    channels = ::Messaging::Channel.enabled_social_messaging
    render json: channels.map! { |c| ::Messaging::Channel.to_menu_type(c) }
  end

  def name_only
    klass = params[:type].present? ? Menu.class_by_type(params[:type]) : Menu::Base

    menus = policy_scope(klass.where(type: params[:type].present? ? klass.to_s : Menu.menu_class_types),
                         policy_scope_class: "#{policy_class}::Scope".constantize)

    result = menus.includes(:settings).map do |menu|
      {
        id: menu.id,
        type: menu.type,
        name: menu.setting(lang: 'en').name
      }
    end
    render json: result
  end

  def queue_view
    render json: Menu::RepresenterService.menus_for_queue_view
  end

  def create
    @menu = Menu::BasicService.create(
      type: params.require(:type),
      attributes: menu_params.to_h
    )

    render_menu
  end

  def show
    @menu.load_assigned_agents_count
    @menu.load_assigned_virtual_agents_count

    @menu.settings.each do |setting|
      setting.parent_target_metric = Menu::Setting::CurrentTargetMetric.call(menu_id: @menu.parent_id,
                                                                             lang: setting.lang)
    end

    render_menu(include_queue_path: true)
  end

  def update
    attrs = params.require(:menu).permit(:parent_id, :position)
    new_parent = attrs[:parent_id]
    current_id = @menu.id

    invalid_move_languages = @menu.settings.reduce([]) do |result, setting|
      lang = setting.lang
      Route::RouteFinder.new.menu_move_invalid?(current_id, new_parent, lang) ? result << lang : result
    end

    attrs.merge!(override_inheritance: invalid_move_languages)
    Menu::BasicService.update(@menu, attrs.to_h)

    @menu.load_assigned_agents_count
    @menu.load_assigned_virtual_agents_count

    render_menu(include_queue_path: true, warning_route_language: invalid_move_languages)
  end

  def destroy
    if params[:dry_run] == 'yes'
      errors = Menu::DestroyService.validate(@menu)
      render json: { errors: }
    else
      Menu::DestroyService.run(@menu)
      render_menu
    end
  end

  def copy
    klass = Menu.class_by_type(params.require(:type))
    from_klass = Menu.class_by_type(params.require(:from))
    Menu::CopyService.copy_all(to_class: klass, from_class: from_klass)

    @menus = [Menu::IvrMenu, Menu::WebMenu].include?(klass) ? klass.includes(:payment_settings).trees : klass.trees
    representer_klass = Menu.representer_class_by_type(params[:type])
    render json: representer_klass.for_collection.prepare(@menus)
  end

  def wait_time
    klass = Menu.class_by_type(params[:type])
    menus = klass.leaves
    result = QueueWaitTime.calculate(menus:, channel_type: params[:channel], lang: params[:lang])

    render json: result.map { |k, v| { menu_id: k, wait_time: v } }
  end

  def status
    lang = params.require(:lang)
    render json: Menu::SetupStatus.get_status(menus: [@menu], lang:)
  end

  def all_statuses
    type = params.require(:type)
    lang = params.require(:lang)

    klass = Menu.class_by_type(type)

    includes = status_settings(klass)

    render json: Menu::SetupStatus.get_status(menus: klass.leaves.includes(*includes), lang:, include_hidden: true)
  end

  def statuses
    type = params.require(:type)
    lang = params.fetch(:lang, nil)
    include_invalid = ActiveRecord::Type::Boolean.new.cast(params.fetch(:include_invalid, true))

    klass = Menu.class_by_type(type)

    includes = status_settings(klass)

    if lang.present?
      render json: Menu::SetupStatus.get_status(menus: klass.leaves.includes(*includes), lang:, include_invalid:)
    else
      render json: Menu::SetupStatus.get_all_lang_status(menus: klass.leaves.includes(*includes), include_invalid:)
    end
  end

  def invalid_statuses
    type = params.require(:type)
    lang = params.require(:lang)
    parent_id = params[:parent_id]
    with_root = !(params[:with_root] =~ /\Atrue\z/i).nil?

    menus = Menu::RepresenterService.menus_for_validating(type, parent_id, with_root)

    render json: Menu::SetupStatus.get_status(menus:, lang:, include_valid: false)
  end

  def phone_assignments
    type = params.require(:type)
    lang = params.require(:lang)
    result = Menu::PhoneNumberAssignment.all_for_menu_type_and_language(menu_type: type, lang:)
    render json: result.to_json(except: [:id, :menu_id, :direction])
  end

  protected

  def policy_class
    Menu::BasePolicy
  end

  private

  def status_settings(klass)
    Menu::Base.settings_for_includes(klass)
  end

  def menu_params
    params.require(:menu).permit(
      :parent_id, :position,
      settings: [
        :hidden, :name, :lang, :id, :_destroy, :enabled_self,
        :remove_voice_file, :voice_file_data_uri, :use_voice_file, # for IvrMenu
        :extension_directory_enabled,
      ]
    )
  end

  def simple_params
    params.permit(:name_query, :type, :lang, :is_leaf)
  end

  def validate_lang
    return unless params[:lang].present?

    languages = params[:lang].split(',')
    invalid_lang_codes = languages - Language.all_codes
    raise ServiceException, "Invalid language code #{invalid_lang_codes.join(', ')}" if invalid_lang_codes.present?
  end

  def require_menu
    @menu = Menu.find(params.require(:id))
  end

  def preload_virtual_agent_assignments
    return unless FeatureFlag.any_on?('chatbot', 'voicebot')

    ActiveRecord::Associations::Preloader.new(records: [@menu],
                                              associations: { virtual_agent_assignments: :virtual_agent }).call
  end

  def authorize_menu
    authorize @menu || Menu::Base, policy_class:
  end

  def render_menu(include_queue_path: false, warning_route_language: false)
    menu_type_key = Menu::MENU_CLASS_TYPES[@menu.type.constantize]
    representer_klass = Menu::MENU_REPRESENTER_CLASSES[menu_type_key]
    render json: representer_klass.new(@menu).to_json(
      user_options: {
        include_queue_path:,
        warning_route_language:
      },
      exclude: [:children]
    )
  end
end

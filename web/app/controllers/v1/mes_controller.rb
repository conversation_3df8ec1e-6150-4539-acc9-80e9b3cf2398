class V1::Mes<PERSON><PERSON>roller < ApplicationController
  before_action :verify_password, only: [:update, :change_password]
  before_action :authorize_me

  # GET /v1/me
  def show
    render_current_user
  end

  # PUT|PATCH /v1/me
  def update
    permitted_params = params.require(:me).permit(:email, :first_name, :middle_name, :last_name, :phone_number, :alias)

    UserService.update(current_user, permitted_params.to_h)

    render_current_user
  end

  # POST /v1/me/avatar
  def upload_avatar
    if Company.current.settings.force_global_avatar
      raise ServiceException, 'Sorry your company setting does not allow you to set your own avatar'
    end

    current_user.avatar_data_uri = params.require(:data_uri)
    current_user.save!
    current_user.sync_update!(:avatar_url)

    render json: { url: current_user.avatar_url }
  end

  # POST /v1/me/password
  def change_password
    permitted_params = params.permit(:new_password, :new_password_confirmation)
    if params[:new_password] == params[:password]
      raise ServiceException, 'New password should be different than the old password'
    end

    current_user.update!(
      password: permitted_params[:new_password],
      password_confirmation: permitted_params[:new_password_confirmation]
    )

    render json: { message: 'Password updated successfully' }
  end

  # GET /v1/me/phone_number
  def show_phone_number
    render_user_phone_number
  end

  # PUT|PATCH /v1/me/phone_number
  def update_phone_number
    permitted_params = params.permit(:phone_number)

    UserService.update(current_user, permitted_params.to_h)

    render_user_phone_number
  end

  private

  def authorize_me
    authorize current_user, policy_class: MePolicy
  end

  def verify_password
    raise ServiceException, 'Password is invalid' unless current_user.authenticate(params[:password])
  end

  def render_current_user
    render json: MeRepresenter.new(current_user)
  end

  def render_user_phone_number
    render json: { phone_number: current_user.phone_number }
  end
end

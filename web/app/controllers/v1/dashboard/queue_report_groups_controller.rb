# frozen_string_literal: true

# rubocop:disable Style/ClassAndModuleChildren
class V1::Dashboard::QueueReportGroupsController < ApplicationController
  # rubocop:enable Style/ClassAndModuleChildren
  wrap_parameters :queue_report_group,
                  include: ['name', 'comm_type', 'hidden', 'hidden_productive_agents', 'user_statuses',
                            'queue_report_group_items']

  before_action :require_queue_report_group, only: [:show, :update, :destroy]
  before_action :authorize_queue_report_group

  def index
    @queue_report_groups = scope.page(params[:page]).per(params[:per_page])

    add_pagination(@queue_report_groups)

    render json: ::Dashboard::QueueReportGroupRepresenter.for_collection.prepare(@queue_report_groups)
  end

  def create
    queue_report_group_params = permitted_queue_report_group_params
    queue_report_group_item_params = permitted_queue_report_group_item_params

    ::Dashboard::QueueReportGroup.transaction do
      @queue_report_group = ::Dashboard::QueueReportGroup.new(queue_report_group_params)

      if queue_report_group_item_params.present?
        @queue_report_group.queue_report_group_items = queue_report_group_item_params.map do |attrs|
          ::Dashboard::QueueReportGroupItem.new(attrs)
        end
      end

      @queue_report_group.save!
    end

    render_queue_report_group
  end

  def show
    render_queue_report_group
  end

  def update
    queue_report_group_params = permitted_queue_report_group_params
    queue_report_group_item_params = permitted_queue_report_group_item_params

    ::Dashboard::QueueReportGroup.transaction do
      @queue_report_group.update!(queue_report_group_params) if queue_report_group_params.present?

      unless queue_report_group_item_params.nil?
        @queue_report_group.queue_report_group_items = queue_report_group_item_params.map do |attrs|
          ::Dashboard::QueueReportGroupItem.new(attrs)
        end
      end
    end

    render_queue_report_group
  end

  def destroy
    @queue_report_group.destroy
    head :ok
  end

  protected

  def policy_class
    ::Dashboard::QueueReportGroupPolicy
  end

  private

  def require_queue_report_group
    @queue_report_group = ::Dashboard::QueueReportGroup.find(params[:id])
  end

  def authorize_queue_report_group
    authorize @queue_report_group || ::Dashboard::QueueReportGroup, policy_class: policy_class
  end

  def scope
    "#{policy_class}::Scope".constantize.new(current_user).resolve || raise('No scope found for query')
  end

  def permitted_queue_report_group_params
    params.require(:queue_report_group).permit(:name, :comm_type, :hidden, :hidden_productive_agents,
                                               :queue_report_group, :user_statuses => [])
  end

  def permitted_queue_report_group_item_params
    queue_report_group_params = params.require(:queue_report_group)
    return nil unless queue_report_group_params.include?(:queue_report_group_items)

    queue_report_group_item_params = queue_report_group_params.permit(queue_report_group_items: %i[lang menu_id])
    queue_report_group_item_params[:queue_report_group_items] || []
  end

  def render_queue_report_group
    render json: ::Dashboard::QueueReportGroupRepresenter.new(@queue_report_group)
  end
end

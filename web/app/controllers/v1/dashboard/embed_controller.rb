# frozen_string_literal: true

module V1
  module Dashboard
    class EmbedController < ApplicationController
      # TODO: DW-1668
      before_action :skip_authorization

      def sso
        sso = ::Embed::Sso.new(**permitted_sso_params)
        render json: { embed_url: sso.generate }
      end

      protected

      def policy_class
        # TODO: DW-1668
      end

      private

      def authorize_embed
        # TODO: DW-1668
      end

      def permitted_sso_params
        p = params.permit(:slug, {theme: {}}, :advanced_reporting)
        {
          slug: p[:slug],
          theme: p[:theme].present? ? p[:theme] : {},
          user: current_user,
          advanced_reporting: p[:advanced_reporting],
        }
      end
    end
  end
end

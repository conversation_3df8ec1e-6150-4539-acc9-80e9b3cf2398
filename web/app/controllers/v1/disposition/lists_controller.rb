module V1
  module Disposition
    class ListsController < ApplicationController
      before_action :require_disposition_list, only: [:show, :update, :destroy, :activate, :deactivate]
      before_action :authorize_disposition_list

      def index
        lists = ::Disposition::List.page(params[:page]).per(params[:per_page])
        add_pagination(lists)
        render json: ::Disposition::ListRepresenter.for_collection.prepare(lists)
      end

      def show
        render json: ::Disposition::ListRepresenter.new(@list)
      end

      def create
        @list = ::Disposition::List.new(permitted_params)
        @list.save!
        render json: ::Disposition::ListRepresenter.new(@list)
      end

      def update
        ::Disposition::ListService.new(@list).validate_to_deactivate if !params[:active].nil? && !params[:active]
        @list.update!(permitted_params)
        require_disposition_lists
        convert_to_representer
      end
      
      def destroy
        ::Disposition::ListService.new(@list).destroy
        head :ok
      end

      private

      def require_disposition_list
        @list = ::Disposition::List.find(params[:id])
      end

      def require_disposition_lists
        @lists = ::Disposition::List.all
      end

      def authorize_disposition_list
        authorize @list || ::Disposition::List
      end
 
      def convert_to_representer
        render json: ::Disposition::ListRepresenter.for_collection.prepare(@lists)
      end

      def permitted_params
        params.permit(*policy(::Disposition::List).permitted_attributes)
      end
    end
  end 
end

# frozen_string_literal: true

module V1
  module Disposition
    class CodesController < ApplicationController
      before_action :require_disposition_code, only: [:show, :update, :destroy]
      before_action :authorize_disposition_code

      def index
        @codes = ::Disposition::Code.all
        render json: ::Disposition::CodeRepresenter.for_collection.prepare(@codes)
      end

      def show
        render json: ::Disposition::CodeRepresenter.new(@code)
      end

      def crm_defined_codes
        render json: crm_defined_disposition_codes
      end

      def create
        @code = ::Disposition::Code.new(permitted_params)
        @code.save!
        render json: ::Disposition::CodeRepresenter.new(@code)
      end

      def update
        code_service.resolve_position_duplication
        code_service.activate_member if params[:enabled] == true
        code_service.deactivate if params[:enabled] == false
        @code.update!(permitted_params)
        render json: ::Disposition::CodeRepresenter.new(@code)
      end

      def destroy
        code_service.destroy
        head :ok
      end

      private

      def crm_defined_disposition_codes
        return [] unless current_company.crm_type == :hubspot

        CRM::CrmServer.request(url: '/get_defined_disposition_codes', method: :get)
      end

      def require_disposition_code
        @code = ::Disposition::Code.find(params[:id])
      end

      def code_service
        ::Disposition::CodeService.new(@code)
      end

      def authorize_disposition_code
        authorize @code || ::Disposition::Code
      end

      def permitted_params
        params.permit(policy(::Disposition::Code).permitted_attributes)
      end
    end
  end
end

module V1
  module Disposition
    class CodeMembershipController < ApplicationController
      before_action :require_disposition_code_list, only: [:index, :show, :update, :get_latest_used_members, :create, :activate, :deactivate]
      before_action :get_member, only: [:destroy, :update, :deactivate, :activate ]
      before_action :get_channel, only: [ :destroy, :deactivate ]
      before_action :authorize_disposition_code

      def index
        @memberships = ::Disposition::Lists::CodeMembership.where(list_id: params[:list_id])
        convert_to_representer
      end

      def show
        @memberships = Array(::Disposition::Lists::CodeMembership.find(id: params[:id]))
        convert_to_representer
      end

      def get_latest_used_members 
        @memberships = member_service.latest_used_members(from: start_date, to: end_date)
        convert_to_representer 
      end

      def create
        member = ::Disposition::Lists::CodeMembership.includes(:disposition_code).where(list_id: params[:list_id], code_id: params[:code_id]).first
        raise ServiceException, "Code #{member.disposition_code.name} already exists!" unless member.blank?
        member_service.validate_member_count
        @membership = ::Disposition::Lists::CodeMembership.new(permitted_params)
        @membership.save!
        require_disposition_code_list
        convert_to_representer
      end

      def update
        @member.update!(permitted_params)
        require_disposition_code_list
        convert_to_representer
      end

      def destroy
        member_service.remove_member_code
      end

      private

      def get_member
        @member = ::Disposition::Lists::CodeMembership.find(params[:id])
      end


      def get_channel
        @channel = params[:channel]
      end

      def member_service
        ::Disposition::ListMembersService.new(member: @member, members: @memberships, channel: @channel, list_id: params[:list_id])
      end

      def require_disposition_code_list
        @memberships = ::Disposition::Lists::CodeMembership.includes(:disposition_code).where(list_id: params[:list_id])
      end
    
      def authorize_disposition_code
        authorize @memberships || ::Disposition::Lists::CodeMembership
      end
        
      def convert_to_representer
        render json: ::Disposition::CodeMembershipRepresenter.for_collection.prepare(@memberships)
      end

      def start_date
        DateTime.parse(params.require(:from))
      rescue Date::Error
        raise ServiceException, "Start date is on wrong format"
      end
     

      def end_date 
        DateTime.parse(params.require(:to))
      rescue Date::Error
        raise ServiceException, "End date is on wrong format"
      end
  
      def permitted_params
        params.permit(policy(::Disposition::Lists::CodeMembership).permitted_attributes)
      end
    end
  end
end
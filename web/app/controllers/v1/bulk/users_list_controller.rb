# frozen_string_literal: true

module V1
  module Bulk
    class UsersListController < ApplicationController
      before_action :authorize_user

      def create
        @job = BulkService::UsersCsvHelper.create_complete_users_async(parameters: { request_user_id: current_user.id})
        head :accepted, location: v1_job_url(@job)
      end

      def show
        @bulk_complete_user = BulkCompleteUser.find(params[:id])
        uploader = @bulk_complete_user.file # BulkCompleteUserUploader
        aws_file = uploader.file # CarrierWave::Storage::AWSFile
        send_data(aws_file.read, filename: "#{aws_file.filename}.csv", type: 'text/csv')
      end

      private

      def authorize_user
        authorize :bulk
      end

      def permitted_params
        @permitted_params ||= params.permit(:page, :per_page)
      end
    end
  end
end

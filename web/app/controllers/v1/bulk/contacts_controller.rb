# frozen_string_literal: true

module V1
  module Bulk
    class ContactsController < ApplicationController
      before_action :authorize_contact

      # POST /v1/bulk/contacts/template
      def template
        data = BulkService::Contacts::CsvHelper.template(user: current_user)
        send_data(data, filename: 'bulk_contact_manage_template.csv', type: 'text/csv')
      end

      # POST /v1/bulk/contacts/upload
      def upload
        # Add action :create for create contact job.
        job = BulkService::Contacts::Job.process_file_in_background(
          action: :create,
          params: {
            user: current_user,
            file: params.require(:file)
          }
        )

        head :accepted, location: v1_job_url(job)
      rescue StandardError => e
        Rails.logger.error("Error uploading file: #{e}, #{e.backtrace}")
        render json: { message: 'Internal Server Error' }, status: :internal_server_error
      end

      # PUT /v1/bulk/contacts/upload
      def update_csv
        # Add action :update for update contact job.
        job = BulkService::Contacts::Job.process_file_in_background(
          action: :update,
          params: {
            id: params[:id],
            user: current_user,
            file: params.require(:file)
          }
        )

        head :accepted, location: v1_job_url(job)
      rescue StandardError => e
        Rails.logger.error("Error updating CSV: #{e}, #{e.backtrace}")
        render json: { message: 'Internal Server Error' }, status: :internal_server_error
      end

      # POST /v1/bulk/contacts/proceed
      def proceed
        contact_list_id = params.require(:contact_list_id)
        job = BulkContactsJob.find(params[:id])
        job.proceed(user_id: current_user.id)

        BulkContactUpdateWorker.perform_async(job.id, contact_list_id)
      rescue StandardError => e
        Rails.logger.error("Error proceeding with import: #{e}, #{e.backtrace}")
        render json: { message: 'Internal Server Error' }, status: :internal_server_error
      end

      private

      def authorize_contact = authorize :bulk_contact
    end
  end
end

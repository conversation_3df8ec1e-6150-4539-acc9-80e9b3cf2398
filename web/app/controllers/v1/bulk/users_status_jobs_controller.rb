# frozen_string_literal: true

module V1
  module Bulk
    class UsersStatusJobsController < ApplicationController
      before_action :authorize_user

      def index
        jobs = SimpleQuery.call(
          scoped: BulkUserStatusesJob.includes(:uploaded_user, :proceed_user).order(id: :desc),
          args: permitted_params.to_h
        )

        add_pagination(jobs)
        render json: BulkUsersStatusJobRepresenter.for_collection.prepare(jobs)
      end

      def show
        job = BulkUserStatusesJob.includes(:uploaded_user, :proceed_user).find(params[:id])
        render json: BulkUsersStatusJobRepresenter.new(job)
      end

      private

      def authorize_user
        authorize :bulk_status_management
      end

      def permitted_params
        @permitted_params ||= params.permit(:page, :per_page)
      end
    end
  end
end

# frozen_string_literal: true

module V1
  module Bulk
    class UsersStatusManagementsController < ApplicationController
      before_action :authorize_user

      def template
        data = BulkService::BulkStatusService.template(type: params[:type] )
        filename = "bulk_#{params[:type]}_management_template.csv"
        send_data(data, filename: filename, type: 'text/csv')
      end

      def list
        data = BulkService::CustomStatuses.list
        send_data(data, filename: 'complete_custom_statuses_list.csv', type: 'text/csv')
      end

      def upload
        file = params.require(:file)
        service = BulkService::BulkStatusUploadService.new(status_list_id: params[:status_list_id])
        job = service.upload(file: file, user: current_user)

        render json: BulkUploadStatusRepresenter.new(
          job: job,
          errors: job.simple_scheme_errors
        )
      end
      
      def update_csv
        file = params.require(:file)
        job = BulkUserStatusesJob.find(params[:id])
        service = BulkService::BulkStatusUploadService.new(status_list_id: params[:status_list_id])
        job = service.update(file: file, user: current_user, job: job)

        render json: BulkUploadStatusRepresenter.new(
          job: job,
          errors: job.simple_scheme_errors
        )
      end

      def proceed
        job = BulkUserStatusesJob.find(params[:id])
        job.proceed(user_id: current_user.id)
        BulkStatusUpdateWorker.perform_async(job.id, params[:status_list_id])
        head :ok
      end

      private

      def authorize_user
        authorize :bulk_status_management
      end
    end
  end
end

# frozen_string_literal: true

module V1
  module Bulk
    class SmsController < ApplicationController
      before_action :authorize_user
      before_action :require_job, only: [:show, :update]

      def status
        in_progress_job = BulkService::SmsJobRepository.find_in_progress(user: current_user)
        represented = BulkSmsJobRepresenter.new(in_progress_job) if in_progress_job.present?

        render json: {
          enabled: ::Sms::ChatService.bulk_outbound_enabled?,
          in_progress: represented
        }
      end

      def show
        render json: BulkSmsJobRepresenter.new(@bulk_sms_job)
      end

      def update
        permitted = params.permit(:status)
        @bulk_sms_job.update!(permitted.to_h)

        render json: BulkSmsJobRepresenter.new(@bulk_sms_job)
      end

      def create
        bulk_sms_job = BulkService.create_sms_job(
          user: current_user,
          file: params.require(:file),
          message: params.require(:message),
          outbound_number_id: params.require(:outbound_number_id)
        )

        render json: BulkSmsJobRepresenter.new(bulk_sms_job)
      end

      private

      def authorize_user
        authorize :bulk_sms
      end

      def require_job
        @bulk_sms_job = BulkSmsJob.find(params[:id])
      end
    end
  end
end

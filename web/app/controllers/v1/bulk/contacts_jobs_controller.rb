# frozen_string_literal: true

module V1
  module Bulk
    class ContactsJobsController < ApplicationController
      before_action :authorize_bulk_contact_job
      before_action :require_bulk_contact_job, only: [:show]

      def index
        jobs = SimpleQuery.call(
          scoped: bulk_contacts_job_scope.order(id: :desc),
          args: permitted_params.to_h
        )

        add_pagination(jobs)
        render json: BulkContactsJobRepresenter.for_collection.prepare(jobs)
      end

      def show
        render json: BulkContactsJobRepresenter.new(@bulk_contact_job)
      end

      private

      def authorize_bulk_contact_job = authorize :bulk_contact_job

      def require_bulk_contact_job
        @bulk_contact_job = bulk_contacts_job_scope.find(params[:id])
      end

      def bulk_contacts_job_scope
        BulkContactsJob.includes(:uploaded_user, :proceed_user)
      end

      def permitted_params
        @permitted_params ||= params.permit(:page, :per_page)
      end
    end
  end
end

module V1
  module Bulk
    class ContactsListController < ApplicationController
      before_action :authorize_bulk_contact_job

      # POST /v1/bulk/contacts/list
      def create
        raise ServiceException, 'Invalid contact list id' if ContactList.find_by(id: params[:contact_list_id]).blank?

        parameters = {
          request_conatct_list_id: params[:contact_list_id]
        }
        job = BulkService::Contacts::CsvHelper.create_complete_contact_async(parameters:)

        head :accepted, location: v1_job_url(job)
      end

      # GET /v1/bulk/contacts/lists/:id
      def show
        bulk_complete_contact = BulkCompleteContact.find(params[:id])
        uploader = bulk_complete_contact.file
        file_uplad = uploader.file

        send_data(file_uplad.read, filename: "#{file_uplad.filename}.csv", type: 'text/csv')
      end

      private

      def authorize_bulk_contact_job
        authorize :bulk_contact_list
      end
    end
  end
end

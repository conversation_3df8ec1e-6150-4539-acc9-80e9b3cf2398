# frozen_string_literal: true

class V1::Bulk::Errors::UploadSchemeController < ApplicationController
  before_action :authorize_user

  def show
    errors = BulkUserStatusesSchemaErrorLog.where(
      job_id: params[:id],
      error_type: :detail_error
    )
    csv = BulkService::BulkStatusService.generate_error_csv(errors: errors)
    send_data(csv, filename: 'bulk_user_status_scheme_errors.csv', type: 'text/csv')
  end

  private

  def authorize_user
    authorize :bulk_status_management
  end
end

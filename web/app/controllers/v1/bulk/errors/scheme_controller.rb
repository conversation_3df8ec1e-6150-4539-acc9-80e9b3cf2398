# frozen_string_literal: true

class V1::Bulk::Errors::SchemeController < ApplicationController
  before_action :authorize_user

  def show
    errors = BulkSchemeErrorLog.where(
      job_id: params[:id],
      error_type: :detail_error
    )
    csv = BulkService::UsersCsvHelper.generate_error_csv(errors: errors)

    send_data(csv, filename: 'bulk_user_scheme_errors.csv', type: 'text/csv')
  end

  private

  def authorize_user
    authorize :bulk
  end
end

# frozen_string_literal: true

module V1
  module Bulk
    module Errors
      module Contacts
        class UpdateController < ApplicationController
          before_action :authorize_contact

          def show
            errors = BulkContactsSchemeLog.where(job_id: params[:id], error_type: :error)
            errors_csv = BulkService::Contacts::CsvHelper.generate_error_csv(errors:, error_type: 'Error')

            warnings = BulkContactsSchemeLog.where(job_id: params[:id], error_type: :warning)
            warnings_csv = BulkService::Contacts::CsvHelper.generate_error_csv(errors: warnings, error_type: 'Warning')

            csv_string = "#{errors_csv}\n#{warnings_csv}"

            send_data(csv_string, filename: 'bulk_contacts_update_errors.csv', type: 'text/csv')
          end

          private

          def authorize_contact = authorize ::Bulk::Errors::ContactPolicy, policy_class: ::Bulk::Errors::ContactPolicy
        end
      end
    end
  end
end

# frozen_string_literal: true

module V1
  module Bulk
    module Errors
      module Contacts
        class SchemeController < ApplicationController
          before_action :authorize_contact

          def show
            errors = BulkContactsSchemeLog.where(
              job_id: params[:id],
              error_type: :detail
            )

            csv = BulkService::Contacts::CsvHelper.generate_error_csv(errors:)

            send_data(csv, filename: 'bulk_contacts_scheme_errors.csv', type: 'text/csv')
          end

          private

          def authorize_contact = authorize ::Bulk::Errors::ContactPolicy, policy_class: ::Bulk::Errors::ContactPolicy
        end
      end
    end
  end
end

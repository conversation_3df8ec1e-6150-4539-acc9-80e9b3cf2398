# frozen_string_literal: true

class V1::Bulk::Errors::UpdateStatusController < ApplicationController
  before_action :authorize_user

  def show
    errors = BulkUserStatusesUpdateErrorLog.where(job_id: params[:id], error_type: :error)
    errors_csv = BulkService::BulkStatusService.generate_error_csv(errors: errors, error_type: 'Error')

    warnings = BulkUserStatusesUpdateErrorLog.where(job_id: params[:id], error_type: :warning)
    warnings_csv = BulkService::BulkStatusService.generate_error_csv(errors: warnings, error_type: 'Warning')

    csv_string = "#{errors_csv}\n#{warnings_csv}"

    send_data(csv_string, filename: 'bulk_user_status_update_errors.csv', type: 'text/csv')
  end

  private

  def authorize_user
    authorize :bulk_status_management
  end
end

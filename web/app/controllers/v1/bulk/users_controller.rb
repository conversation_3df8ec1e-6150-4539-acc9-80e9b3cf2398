# frozen_string_literal: true

module V1
  module Bulk
    class UsersController < ApplicationController
      before_action :authorize_user

      def template
        data = BulkService::UsersCsvHelper.template(user: current_user)
        send_data(data, filename: 'bulk_user_manage_template.csv', type: 'text/csv')
      end

      def proceed
        job = BulkUsersJob.find(params[:id])

        job.proceed(user_id: current_user.id)

        BulkUpdateWorker.perform_async(job.id)
        head :ok
      end

      def upload
        file = params.require(:file)
        bulk_users_job = BulkUsersJob.create!(uploaded_user: current_user, file: file, status: :created, data_type: 'csv')
        job = BulkService.validate_users_jobs(bulk_job: bulk_users_job)

        head :accepted, location: v1_job_url(job)
      end

      def update_csv
        file = params.require(:file)
        bulk_users_job = BulkUsersJob.update(params[:id], { file: file, uploaded_user: current_user })
        job = BulkService.validate_users_jobs(bulk_job: bulk_users_job)

        head :accepted, location: v1_job_url(job)
      end

      private

      def authorize_user
        authorize :bulk
      end
    end
  end
end

# frozen_string_literal: true

class V1::TeamsController < ApplicationController
  wrap_parameters :team, format: [:json, :url_encoded_form, :multipart_form],
                         include: Team.attribute_names + ['transfer_restriction'] + ['availability_filter']

  before_action :require_team, only: [:show, :update, :destroy]
  before_action :authorize_team, except: [:add_members, :remove_members]
  around_action :use_read_replica, only: [:index]

  rescue_from ClosureTreeHierarchyLocking::LockNotAcquired do |_error|
    Rails.logger.warn { 'team hierarchy lock not acquired' }
    render json: { message: 'Teams are being updated by others now. Please try again later.' },
           status: :service_unavailable
  end

  def index
    teams = policy_scope(Team.includes(:agent_desktop_default_layout, :availability_filter_assignment).all,
                         policy_scope_class: "#{policy_class}::Scope".constantize)

    teams = teams.with_name(index_params[:name_query]) if index_params[:name_query].present?

    teams.empty? ? render(json: []) : render_team_trees(teams) { TeamService::TreeLoader.load(teams) }
  end

  def user_teams
    user_ids = params[:user_ids]

    teams = Team.where(id: TeamMembership.where(user_id: user_ids).select(:team_id))
                .page(params[:page]).per(params[:per_page])

    ActiveRecord::Associations::Preloader.new(records: teams, associations: :managers).call

    render json: TeamWithMemberRepresenter.for_collection.prepare(teams)
  end

  def managed_teams
    report_type = params[:report_type] || 'agent'
    is_restricted_data = ReportService::ReportRestrictionChecker.check_restricted_data(report_type:,
                                                                                       custom_permissions: @current_user.custom_permissions)
    scope = if is_restricted_data
              Dashboard::DataScope::RestrictedToTeam.new(@current_user).managed_teams
            else
              policy_scope(Team.all, policy_scope_class: "#{policy_class}::Scope".constantize)
            end

    render json: scope.select(:id, :name)
  end

  def managed_users
    report_type = params[:report_type] || 'agent'
    is_restricted_data = ReportService::ReportRestrictionChecker.check_restricted_data(report_type:,
                                                                                       custom_permissions: @current_user.custom_permissions)
    scope = if is_restricted_data
              Dashboard::DataScope::RestrictedToTeam.new(@current_user).users
            else
              User.with_agent_permission
            end
    render json: scope.select(:id, :first_name, :last_name)
  end

  def create
    create_params = team_params.to_h
    create_params.merge!(default_layout_settings_params.to_h)

    @team, team_setting = TeamService.create_team(create_params, team_settings_params.to_h).values

    TeamSettingService::SettingUpdater.new(team_setting:,
                                           params: team_settings_params).update_setting_descendants
    TeamSettingService::DeflectionSettingUpdater.new.save(@team, permitted_deflection_setting_attributes)

    @team.reload

    render_team
  end

  def show
    render_team
  end

  def update
    update_params = team_params.to_h
    update_params.merge!(default_layout_settings_params.to_h)

    TeamService.update_team(@team, update_params)
    TeamService.update_team_setting(@team, team_update_setting_params) if team_update_setting_params
    TeamSettingService::DeflectionSettingUpdater.new.save(@team, permitted_deflection_setting_attributes)

    @team.reload

    render_team
  end

  def destroy
    TeamService.destroy_team(@team)
    render_team
  end

  def multiple_destroy
    destroyed_teams = TeamService.multiple_destroy(team_ids: params[:team_ids])
    render json: {
      destroyed_team_ids: destroyed_teams.map(&:id)
    }
  end

  def overview
    team_tree = Team.includes(:members).trees
    render json: TeamTreeOverviewRepresenter.for_collection.prepare(team_tree)
  end

  def add_members
    team_ids = params[:team_ids]
    user_ids = params[:user_ids]
    membership_type = params[:membership_type]

    authorize_teams(Team.find(team_ids))

    result = TeamService::MultipleMembershipUpdator.add(team_ids:, user_ids:,
                                                        membership_type:)

    render json: result
  end

  def remove_members
    team_ids = params[:team_ids]
    user_ids = params[:user_ids]

    authorize_teams(Team.find(team_ids))

    result = TeamService::MultipleMembershipUpdator.remove(team_ids:, user_ids:)

    render json: result
  end

  protected

  def policy_class
    TeamPolicy
  end

  private

  def team_params
    params.require(:team)
          .permit(
            :name,
            :parent_id,
            :position,
            :enter_ticket_enabled,
            :enter_ticket_required,
            :crm_custom_field_key,
            :availability_filter_id,
            availability_filter: [:availability_filter_longevity_time],
            transfer_restriction: [
              :allow_from,
              :allow_to,
              :disallow_to_same_team,
              :transfer_team_option,
              {
                selected_teams: [:team_id, :supervisor, :subordinate]
              }
            ]
          )
  end

  def index_params
    params.permit(:name_query)
  end

  def team_settings_params
    return {} unless params[:team_setting]

    params.require(:team_setting).permit(:contact_list_id,
                                         :contact_override_inheritance,
                                         :accessible_to_global_contact,
                                         :user_status_list_id)
  end

  def default_layout_settings_params
    return {} unless params[:default_layout_settings]
    # this is a guard from a FE bug, the FE needs to be fixed
    if params[:default_layout_settings].key?(:configuration) && params[:default_layout_settings][:configuration].blank?
      return {}
    end

    params.permit(default_layout_settings: [
                    :should_reset,
                    { configuration: AgentDesktopLayouts::AgentDesktopLayoutReference::LAYOUT_SETTING_TYPES }
                  ])
  end

  def render_team_trees(scope)
    scope_cache = ScopeCache.new(scope, key: 'json/v1/teams/index', timestamp: :ext_updated_at)
    return unless stale?(scope_cache, template: false)

    options = {
      expires_in: 24.hours, compress: true, compress_threshold: 1.kilobyte,
      lock: { acquire: 5, life: 5 }
    }

    json = Cache::Lock.fetch(scope_cache.cache_key, options) do
      @teams = yield
      TeamTreeRepresenter.for_collection.prepare(@teams).to_json
    end
    render json:
  rescue Redis::Lock::LockNotAcquired
    render json: { message: 'Temporarily unavailable' }, status: :service_unavailable
  end

  def require_team
    @team = Team.find(params.require(:id))
  end

  def authorize_teams(teams)
    teams.each { |team| authorize team, policy_class: }
  end

  def authorize_team
    authorize @team || Team, policy_class:
  end

  def render_team
    render json: TeamRepresenter.new(@team).to_json(team_representer_options)
  end

  def team_representer_options
    user_options = { user_status_list_inheritance:, include_transfer_restriction: true }
    exclude_params.merge(user_options:)
  end

  def user_status_list_inheritance
    @user_status_list_inheritance = TeamSettingService::UserStatusList::SettingFinder.team_ancestor_to_inherit_user_status_list(@team.id)
    {
      parent_name: @user_status_list_inheritance&.name,
      user_status_list_id_to_inherit: @user_status_list_inheritance&.user_status_list_id
    }
  end

  def exclude_params
    {
      exclude: Array(params[:exclude]).map(&:to_sym)
    }
  end

  def permitted_deflection_setting_attributes
    return {} if params.blank? || params[:team_setting].blank?

    params[:team_setting].permit(TeamSettingPolicy.permitted_deflection_setting_attributes).to_h
  end

  # params to directly update into team_setting table
  def team_update_setting_params
    params.permit(team_setting: [:user_status_list_id])[:team_setting]
  end
end

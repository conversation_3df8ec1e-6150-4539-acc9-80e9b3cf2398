# frozen_string_literal: true

class V1::SalesforceController < ApplicationController
  skip_before_action :authenticate, except: [:oauth_start]
  before_action :require_agent, only: [:oauth_start]

  # If the signed_request is not valid, UnauthorizedException is raised.
  rescue_from UnauthorizedException do |error|
    redirect_to "#{base_url}/401?error=#{error.message}", allow_other_host: true
  end

  def start
    signed_request = params[:signed_request]
    if signed_request.nil?
      error_msg = "We do not support an individual OAuth procedure. Please ask you adminstrator"
      raise UnauthorizedException, error_msg
    end

    data = CRM::Salesforce.parse_signed_request(signed_request)
    if data[:payload] and data[:company_key]
      # New secured widget
      company = CRM::Salesforce.parse_signed_payload(data[:company_key], data[:payload], data[:org_id])
    else
      # Old unsecured widget (Deprecated in package 1.15)
      company = switch_tenant(data[:org_name], data[:type])
    end

    unless CRM::Salesforce.valid_signed_request?(company.oauth_client_secret, signed_request)
      raise UnauthorizedException, "Invalid signed request"
    end

    hash_key = CRM::Salesforce.save_signed_request(signed_request)

    queries = {
      from: "salesforce",
      # type can be
      # - chat : chat
      # - call_t1 : call using mobile sdk(fitbit Tier 1 case)
      # - call_t2 : call not using mobile sdk(fitbit Tier 2 case)
      type: data[:type],
      # Instead of sending signed_request directly, sends hash_key.
      # It is to prevent the signed request from exposing in logs.(Rails logs, nginx logs)
      # Expects the web app exchange hash key to signed_request.
      hash_key: hash_key,
      # Sets subdomain since we cannot include subdomain in the host.
      # We use SFDC API call using canvas event, since canvas app has nested iframe
      # and SFDC does not allow to use `postMessage` under the nested iframe.
      # see https://releasenotes.docs.salesforce.com/en-us/winter16/release-notes/rn_canvas_double_iframe.htm
      # The host name from SFDC is main.ujet.co. We cannot make it configurable
      # from SFDC side for a company. If we change it to like `fitbit.ujet.co`
      # and redirect, SFDC does not allow to comunicate with canvas event, since
      # iframe domain `fitbit.ujet.co` is not same as canvas app configuration
      # `main.ujet.co` So, we have to use `main.ujet.co` as a redirect url.
      # In this case, we need to set the subdomain info in API request, So sends
      # subdomain, and expects the web client include subdomain in `UjetSubdomain` header.
      subdomain: company.subdomain,
      is_lightning: data[:is_lightning]
    }

    redirect_to "#{base_url}?#{queries.to_query}", allow_other_host: true
  end

  def signed_request
    signed_request = CRM::Salesforce.get_signed_request(params[:hash_key])
    render json: {signed_request: signed_request}
  end

  def callback
    render json: nil
  end

  def oauth_start
    oauth = CRM::Client::Salesforce.oauth_auth(
      user_id: current_user.id,
      callback_domain: URLHelper.base_url(request, remove_api: true)
    )

    start_url = oauth['authorization_url'] + "&state=#{current_user.id}&prompt=login"

    render json: { redirect_url: start_url }
  rescue RestClient::ExceptionWithResponse => e
    render plain: e.response.try(:to_s)
  end

  def oauth_callback
    # This is callback from admin console, don't use for agent widget
    if params[:state] == 'admin_token'
      user_id = 'admin_token'
    else
      user_id = params[:state].to_i
      agent = User.with_agent_permission.find(user_id)
    end

    res = CRM::Client::Salesforce.oauth_callback(
      user_id: user_id,
      callback_domain: URLHelper.base_url(request, remove_api: true),
      code: params[:code],
      state: params[:state]
    )

    agent.update_oauth_tokens(res.symbolize_keys) if agent.present? && res.present?

    # Need valid URL with hash value to close OAuth popup window
    redirect_to "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/salesforce/oauth_callback_end#success=true", allow_other_host: true
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.warn(error: e) { "Error in Salesforce callback rest : #{e.message}" }
    body = JSON.parse(e.response.body)
    text = body.dig('message') || 'Unable to authenticate Salesforce user'
    redirect_to "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/salesforce/oauth_callback_end#success=false&message=#{text}", allow_other_host: true
  rescue StandardError => e
    Rails.logger.warn(error: e) { "Error in Salesforce callback : #{e.message}" }
    text = 'Unable to authenticate Salesforce user'
    redirect_to "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/salesforce/oauth_callback_end#success=false&message=#{text}", allow_other_host: true
  end

  def oauth_callback_end
    # Use to close OAuth popup window
    render plain: ''
  end

  def presence_status_callback
    signed_request = params.require(:signed_request)

    payload, header = JWT.decode(signed_request, current_company.secret, true)

    changed = SfdcUserStatusService.apply_presence_status(
      crm_user_id: payload['user_id'],
      presence_status_id: payload['presence_status_id']
    )

    head(changed ? :ok : :accepted)
  end


  private

  def should_verify_authorized
    false
  end

  def base_url
    url = "https://#{URLHelper.host(request, remove_api: true)}"
    url += ":#{Rails.configuration.env[:client_app_port]}" if Rails.configuration.env[:client_app_port].present?
    url
  end

  def switch_tenant(org_name, type)
    company = Company.where(sfdc_org_name: org_name, crm: Company.crms[:salesforce]).first

    if company.nil?
      error_msg = "Company named #{org_name} does not exist. Please ask your administrator"
      raise UnauthorizedException, error_msg
    end
    TenantSelect.switch!(company.subdomain)
    company
  end
end

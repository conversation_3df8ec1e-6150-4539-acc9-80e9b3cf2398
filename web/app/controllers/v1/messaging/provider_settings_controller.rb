# frozen_string_literal: true

class V1::Messaging::ProviderSettingsController < ApplicationController
  before_action :authorize_provider_settings

  def index
    render json: provider_settings
  end

  private

  def provider_settings
    {
      whatsapp: {
        sunshine: {
          enabled: Company.current.sunshine_settings.enabled,
          app_id: Company.current.sunshine_settings.app_id
        }.compact
      },
      amb: {
        business_id: Company.current.amb_settings.business_id
      }.compact
    }
  end

  def authorize_provider_settings
    authorize [:messaging, :provider_setting]
  end
end

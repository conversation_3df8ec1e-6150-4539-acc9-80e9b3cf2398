# frozen_string_literal: true
class V1::UserStatusListsController < ApplicationController
  before_action :authorize_user_status
  before_action :require_user_status_list, only: [:show, :destroy, :update, :remove_status, :assignment_names]
  before_action :require_user_status, only: [:remove_status]

  # GET /v1/user_status_lists/:id
  def show
    @user_status_list.inject_user_statuses
    render json: UserStatusListRepresenter.new(@user_status_list)
  end

  # DELETE /v1/user_status_lists/:id
  def destroy
    render json: UserStatusListRepresenter.new(UserStatusListService.destroy(params[:id]))
  end

  # POST /v1/user_status_lists
  def create
    render json: UserStatusListRepresenter.new(UserStatusListService.create(permitted_params))
  end

  # PUT/PATCH /v1/user_status_lists
  def update
    render json: UserStatusListRepresenter.new(UserStatusListService.update(@user_status_list,
                                                                            permitted_params_for_update))
  end

  # GET /v1/user_status_lists
  def index
    all_lists_with_status_ids = UserStatusList.all_status_lists
    render json: UserStatusListRepresenter.for_collection.new(all_lists_with_status_ids).to_json(
      user_options: { include_status_ids: true }
    )
  end

  # DELETE v1/user_status_lists/:id/statuses/:name
  def remove_status
    render json: UserStatusListRepresenter.new(UserStatusListService.remove_status(@user_status_list, @user_status))
  end

  # GET v1/user_status_lists/:id/assignment_names
  # get the assigned queue and team names
  def assignment_names
    render json: UserStatusListService.cached_assignment_names_by_user_status_list_id(@user_status_list.id)
  end

  def permitted_params
    params.require(:name)
    params.permit(:name, status_ids: [])
  end

  def permitted_params_for_update
    params.permit(:name, status_ids: [])
  end

  def authorize_user_status
    authorize UserStatusList
  end

  def require_user_status_list
    list_id = params.require(:id).to_i

    # Predefined lists has id<=0 and is not in DB
    if UserStatusList.predefined_status_list_ids.include?(list_id) &&
       ['destroy', 'update', 'remove_status'].include?(action_name)

      return render json: { error: "Predefined Status List can't be deleted or modified." }, status: :forbidden
    end

    @user_status_list = UserStatusListService.get_by_id(list_id)
    return render json: { error: 'User Status List not found' }, status: :not_found unless @user_status_list
  end

  def require_user_status
    params.require(:name)
    @user_status = UserStatus.get_by_name(params[:name])
    render json: { error: 'User Status not found' }, status: :not_found unless @user_status
  end
end

# frozen_string_literal: true

class V1::ChatShortcutsController < ApplicationController
  before_action :require_shortcut, only: %i[update destroy]
  before_action :authorize_shortcut

  def index
    shortcuts = ChatShortcut.page(params[:page]).per(params[:per_page])
    add_pagination(shortcuts)
    render json: ChatShortcutRepresenter.for_collection.prepare(shortcuts)
  end

  def create
    shortcut = ChatShortcut.create!(permitted_params)
    render json: ChatShortcutRepresenter.new(shortcut)
  rescue ActiveRecord::RecordNotUnique
    render json: { message: 'Key has already been taken' }, status: :bad_request
  end

  def update
    @shortcut.update!(permitted_params)
    render json: ChatShortcutRepresenter.new(@shortcut)
  rescue ActiveRecord::RecordNotUnique
    render json: { message: 'Key has already been taken' }, status: :bad_request
  end

  def destroy
    @shortcut.destroy!
    head :ok
  end

  private

  def require_shortcut
    @shortcut = ChatShortcut.find(params[:id])
  end

  def authorize_shortcut
    authorize @shortcut || ChatShortcut
  end

  def permitted_params
    params.permit(:category_id, :key, :message)
  end
end

class V1::Chatbot::CredentialsController < ApplicationController
  before_action :require_chatbot_credential, only: [:show, :destroy]
  before_action :authorize_chatbot_credential
  before_action :require_chatbot_platform, only: [:service_key, :oauth, :index]

  # POST /v1/chatbot/platforms/:id/credentials/service_key
  def service_key
    credential = params.require(:credential)
    chatbot_credential = ::Chatbot::CredentialFactory.create_from_service_key!(
      credential: credential,
      chatbot_platform_id: @chatbot_platform.id
    )

    render json: ::Chatbot::CredentialRepresenter.new(chatbot_credential)
  end

  # POST /v1/chatbot/platforms/:id/credentials/oauth
  def oauth
    configuration = params.require(:configuration)
    chatbot_credential = ::Chatbot::CredentialFactory.create_from_oauth!(
      configuration: configuration,
      chatbot_platform_id: @chatbot_platform.id
    )

    # TODO move following logic out of controller

    # Authorization proceeds and redirects to fetch and save access token
    authorizer = ::Chatbot::AuthenticationService.create_authorizer(configuration)
    raise ServiceException, "OAuth authorizer is not created" if authorizer.nil?

    # url to Google OAuth with base callback url
    url = authorizer.get_authorization_url(
      base_url: "#{Company.current.base_url}/v1/chatbot/credentials/",
      state: chatbot_credential.id
    )

    render json: ::Chatbot::CredentialRepresenter.new(chatbot_credential).to_json(user_options: { url: url })
  end

  # GET /v1/chatbot/platforms/:id/credentials
  def index
    @credentials = @chatbot_platform.chatbot_credentials
    render json: ::Chatbot::CredentialRepresenter.for_collection.prepare(@credentials).to_json
  end

  # GET /v1/chatbot/credentials/:id
  def show
    render json: ::Chatbot::CredentialRepresenter.new(@chatbot_credential)
  end

  # DELETE /v1/chatbot/credentials/:id
  def destroy
    @chatbot_credential.destroy!

    head :ok
  end

  # GET /v1/chatbot/credentials/oauth_callback
  def oauth_callback
    code = params.require(:code)
    credential_id = params.require(:state)
    scope = params.require(:scope)

    ::Chatbot::AuthenticationService.handle_callback(code, credential_id, scope, Company.current.base_url)

    # TODO redirect back to developer settings
    head :ok
  end

  # Use callbacks to share common setup or constraints between actions.
  def require_chatbot_credential
    id = params.require(:id)
    @chatbot_credential = ::ChatbotCredential.find(id)
  end

  def authorize_chatbot_credential
    authorize ChatbotCredential, policy_class: ::Chatbot::CredentialPolicy
  end

  def require_chatbot_platform
    id = params.require(:platform_id)
    @chatbot_platform = ::ChatbotPlatform.includes(:chatbot_credentials).find(id)
  end
end

class V1::Chatbot::PlatformsController < ApplicationController
  before_action :require_chatbot_platform, only: %i[show update validate destroy]
  before_action :authorize_chatbot_platform

  # GET /v1/chatbot/platforms
  def index
    @chatbot_platforms = ChatbotPlatform.all
    render json: ::Chatbot::PlatformRepresenter.for_collection.prepare(@chatbot_platforms)
  end

  # GET /v1/chatbot/platforms/:id
  def show
    render json: ::Chatbot::PlatformRepresenter.new(@chatbot_platform)
  end

  # POST /v1/chatbot/platforms
  def create
    permitted_params_for_create = params.permit(:name, :platform_type)

    name          = permitted_params_for_create.require(:name)
    platform_type = permitted_params_for_create.require(:platform_type)

    @chatbot_platform = ::Chatbot::PlatformFactory.create!(
      name:          name,
      platform_type: platform_type
    )
    render json: ::Chatbot::PlatformRepresenter.new(@chatbot_platform)
  end

  # PATCH | PUT /v1/chatbot/platforms/:id
  def update
    permitted_params = params.permit(:name, :enabled)

    ::Chatbot::PlatformService.update(chatbot_platform: @chatbot_platform, attrs: permitted_params.to_h)

    if permitted_params.key?(:enabled)
      if @chatbot_platform.enabled
        Chatbot::EmailNotificationService.send_chatbot_platform_enabled(
          chatbot_platform: @chatbot_platform,
          by_user:          current_user
        )
      else
        Chatbot::EmailNotificationService.send_chatbot_platform_disabled(
          chatbot_platform: @chatbot_platform,
          by_user:          current_user
        )
      end
    end

    render json: ::Chatbot::PlatformRepresenter.new(@chatbot_platform)
  end

  # POST /v1/chatbot/platforms/:id/validate
  def validate
    # validate keys of all workflows in this platform
    ::Chatbot::Validator.new.validate_platform!(chatbot_platform: @chatbot_platform)
    render json: ::Chatbot::PlatformRepresenter.new(@chatbot_platform)
  end

  # DELETE /v1/chatbot/platforms/:id
  def destroy
    @chatbot_platform.destroy!

    Chatbot::EmailNotificationService.send_chatbot_platform_deleted(
      chatbot_platform: @chatbot_platform,
      by_user:          current_user
    )

    head :ok
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def require_chatbot_platform
    @chatbot_platform = ChatbotPlatform.find(params[:id])
  end

  def authorize_chatbot_platform
    authorize ChatbotPlatform, policy_class: ::Chatbot::PlatformPolicy
  end
end

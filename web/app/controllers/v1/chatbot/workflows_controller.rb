class V1::Chatbot::WorkflowsController < ApplicationController
  # list of platform-specific workflow fields for any platform
  WORKFLOW_METADATA_FIELDS = [
    :location,
    :workflowId
  ].freeze

  before_action :require_chatbot_workflow, only: [:show, :destroy]
  before_action :require_chatbot_platform, only: [:index, :create]
  before_action :require_chatbot_credential, only: [:create_from_credential]
  before_action :authorize_chatbot_workflow

  # GET /v1/chatbot/platforms/:platform_id/workflows
  def index
    @chatbot_workflows = @chatbot_platform.chatbot_workflows
    render json: ::Chatbot::WorkflowRepresenter.for_collection.prepare(@chatbot_workflows)
  end

  # GET /v1/chatbot/workflows/:id
  def show
    render json: ::Chatbot::WorkflowRepresenter.new(@chatbot_workflow)
  end

  # POST /v1/chatbot/platforms/:platform_id/workflows
  # @deprecated, use create_from_credential instead
  # DO NOT DELETE YET - frontend has not fixed on their end.
  def create
    credential_file = permitted_params_for_create.require(:credential)
    @chatbot_credential = ::Chatbot::CredentialFactory.create_from_service_key!(
      credential: credential_file,
      chatbot_platform_id: @chatbot_platform.id
    )

    @chatbot_workflows = ::Chatbot::WorkflowFactory.create_from_credential!(chatbot_credential: @chatbot_credential)
    render json: ::Chatbot::WorkflowRepresenter.new(@chatbot_workflows[0])
  end

  # POST /v1/chatbot/credentials/:credential_id/workflows
  def create_from_credential
    @chatbot_workflows = ::Chatbot::WorkflowFactory.create_from_credential!(chatbot_credential: @chatbot_credential)
    render json: ::Chatbot::WorkflowRepresenter.for_collection.prepare(@chatbot_workflows)
  end

  # DELETE /v1/chatbot/workflows/:id
  def destroy
    @chatbot_platform = @chatbot_workflow.chatbot_platform
    ::Chatbot::WorkflowDestroy.destroy_workflow!(chatbot_platform: @chatbot_platform, chatbot_workflow: @chatbot_workflow)
    head :ok
  end

  private

  # Use callbacks to share common setup or constraints between actions.
  def require_chatbot_platform
    @chatbot_platform = ChatbotPlatform.includes(:chatbot_workflows).find(params[:platform_id])
  end

  def require_chatbot_workflow
    @chatbot_workflow = ChatbotWorkflow.find(params[:id])
  end

  def require_chatbot_credential
    @chatbot_credential = ChatbotCredential.includes(:chatbot_workflows).find(params[:credential_id])
  end

  def permitted_params_for_create
    params.permit(
      :credential,
      :platform_id,
      workflows: [:project_id, { metadata: WORKFLOW_METADATA_FIELDS }]
    )
  end

  def authorize_chatbot_workflow
    authorize ChatbotWorkflow, policy_class: Chatbot::WorkflowPolicy
  end
end

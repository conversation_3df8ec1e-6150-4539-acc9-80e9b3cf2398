# frozen_string_literal: true

module V1
  module Chatbot
    class VirtualAgentsController < ApplicationController
      PERMITTED_PARAMS = [
        :name,
        :chatbot_workflow_id,
        :enabled,
        :avatar_data_uri,
        :assign_to_all_queues,
        :agent_type,
        :dtmf_enabled,
        :data_parameters_enabled,
        data_parameters: [:field, :type, :value, :source, :source_field, :required],
        channel_types: [],
        menu_types: [],
        langs: []
      ].freeze

      wrap_parameters :virtual_agent, include: PERMITTED_PARAMS

      before_action :require_virtual_agent, only: %i[show update]
      before_action :authorize_virtual_agent

      # GET /v1/chatbot/virtual_agents
      def index
        virtual_agents = virtual_agent_scope.to_a
        preload_channel_settings(virtual_agents.flat_map(&:virtual_agent_assignments))

        if params.key?(:enabled)
          enabled_param = ActiveRecord::Type::Boolean.new.cast(params[:enabled])
          virtual_agents = virtual_agents.where(enabled: enabled_param)
        end

        render json: ::Chatbot::VirtualAgentRepresenter.for_collection.prepare(virtual_agents)
      end

      # GET /v1/chatbot/virtual_agents/name_only?menu_type=menu_type&lang=en
      # get available virtual_agents' [{id:, name:}] for the menu_type
      def name_only
        menu_type = params.require(:menu_type)

        scope = VirtualAgent.with_menu_types(menu_type)
        scope = scope.with_langs(params[:lang]) if params[:lang].present?
        if params.key?(:enabled)
          enabled_param = ActiveRecord::Type::Boolean.new.cast(params[:enabled])
          scope = scope.where(enabled: enabled_param)
        end

        virtual_agents = scope.select(:id, :name)

        render json: virtual_agents.compact
      end

      # GET /v1/chatbot/virtual_agents/:id
      def show
        render_virtual_agent
      end

      # POST /v1/chatbot/virtual_agents
      def create
        assign_to_all_queues = permitted_params.delete(:assign_to_all_queues)

        @virtual_agent = ::Chatbot::VirtualAgentService.new.create!(**params_for_create)

        if assign_to_all_queues
          ::Chatbot::VirtualAgentChannelAssignmentService.assign_to_all_queues(virtual_agent: @virtual_agent)
        end

        render_virtual_agent
      end

      # PATCH | PUT /v1/chatbot/virtual_agents/:id
      def update
        ::Chatbot::VirtualAgentService.new.update!(
          virtual_agent: @virtual_agent,
          user: current_user,
          params: params_for_update.to_h
        )

        render_virtual_agent
      end

      # DELETE /v1/chatbot/virtual_agents/:id
      def destroy
        virtual_agent = VirtualAgent.find(params[:id])
        virtual_agent.destroy!

        ::Chatbot::EmailNotificationService.send_virtual_agent_deleted(
          by_user: current_user,
          virtual_agent: virtual_agent
        )

        head :ok
      end

      private

      def virtual_agent_scope
        VirtualAgent.includes(
          :languages,
          chatbot_workflow: [:chatbot_platform],
          virtual_agent_assignments: [menu: [:settings]]
        )
      end

      def preload_channel_settings(assignments)
        return if assignments.empty?

        chat_menus = []
        call_menus = []
        assignments.each do |assignment|
          next if assignment.menu.nil?
          chat_menus << assignment.menu if assignment.menu.class.has_chat_setting?
          call_menus << assignment.menu if assignment.menu.class.has_voice_call_setting?
        end

        ActiveRecord::Associations::Preloader.new(records: chat_menus, associations: :chat_settings).call if chat_menus.present?
        ActiveRecord::Associations::Preloader.new(records: call_menus, associations: :voice_call_settings).call if call_menus.present?
      end

      def require_virtual_agent
        @virtual_agent ||= virtual_agent_scope.find(params[:id])
        preload_channel_settings(@virtual_agent.virtual_agent_assignments)
      end

      def params_for_create
        @params_for_create ||= {
          name: permitted_params.fetch(:name),
          chatbot_workflow_id: permitted_params.fetch(:chatbot_workflow_id, nil),
          menu_types: permitted_params.fetch(:menu_types, [:web_menu, :mobile_menu]),
          # default to chat for backward compatibility, can remove later
          channel_types: permitted_params.fetch(:channel_types, [:chat]),
          agent_type: permitted_params.fetch(:agent_type, :support),
          langs: permitted_params.fetch(:langs),
          avatar_data_uri: permitted_params.fetch(:avatar_data_uri, nil),
          enabled: permitted_params.fetch(:enabled, false),
          data_parameters_enabled: permitted_params.fetch(:data_parameters_enabled, false),
          data_parameters: permitted_params.fetch(:data_parameters, []),
          dtmf_enabled: permitted_params.fetch(:dtmf_enabled, false)
        }
      end

      def params_for_update
        @params_for_update ||= permitted_params.except(:agent_type)
      end

      def permitted_params
        @permitted_params ||= params.permit(PERMITTED_PARAMS)
      end

      def render_virtual_agent
        render json: ::Chatbot::VirtualAgentRepresenter.new(@virtual_agent)
      end

      def authorize_virtual_agent
        authorize VirtualAgent, policy_class: ::Chatbot::VirtualAgentPolicy
      end
    end
  end
end

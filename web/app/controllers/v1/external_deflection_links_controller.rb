# frozen_string_literal: true

class V1::ExternalDeflectionLinksController < ApplicationController
  attr_accessor :external_deflection_link

  before_action :require_external_deflection_link, only: [:show, :update, :destroy]
  before_action :authorize_external_deflection_link

  def index
    render json: ExternalDeflectionLinkRepresenter.for_user(current_user)
                                                  .for_collection
                                                  .prepare(ExternalDeflectionLink.all)
                                                  .to_hash(user_options: { include_queues: include_queues? })
  end

  def show
    render_link
  end

  def create
    self.external_deflection_link = ExternalDeflectionLink.create!(
      {
        name: params.require(:name),
        display_name: params.require(:display_name),
        url: params.require(:url),
        enabled: params.require(:enabled)
      }.merge(params.permit(:call_to_action, :mobile_icon_id, :web_icon_data_uri).to_h)
    )

    render_link
  end

  def update
    external_deflection_link.update!(
      params.permit(:name, :display_name, :url, :enabled, :call_to_action, :mobile_icon_id, :web_icon_data_uri)
    )

    render_link
  end

  def destroy
    external_deflection_link.destroy!

    render json: {}
  end

  private

  def require_external_deflection_link
    self.external_deflection_link = ExternalDeflectionLink.find params[:id]
  end

  def render_link
    render json: ExternalDeflectionLinkRepresenter.for_user(current_user)
                                                  .new(external_deflection_link)
                                                  .to_hash(user_options: { include_queues: include_queues? })
  end

  def authorize_external_deflection_link
    authorize external_deflection_link || ::ExternalDeflectionLink
  end

  def include_queues?
    ActiveModel::Type::Boolean.new.cast(params['include_queues']) || false
  end
end

# frozen_string_literal: true

class V1::Settings::ChatSettingsController < V1::Settings::BaseController
  wrap_parameters :chat_setting, include: [
    :enabled, :timeout, :after_hours_error_message, :enable_chats_in_call, :enable_calls_in_chat,
    :enable_agent_attach_file, :enable_format_messages, :enable_emojis, :max_chats_per_agent, :transfer_limits,
    :max_chats_per_agent_enabled, :post_history_comment_in_crm, :post_history_attachment_in_crm,
    :abandoned_chat_ticket, :abandoned_chat_ticket_with_few_messages, :abandoned_chat_ticket_message_threshold,
    :delay_chat_ticket_consumer_messages, :delay_chat_ticket_consumer_message_threshold,
    :use_queue_aging, :queue_aging_interval, :deflection, :notification, :message_notification, :inactivity,
    :queue_expiration, :auto_remove_finished_chats_from_chat_adapter, :termination,
    :auto_wrap_up_enabled, :wrap_up_time_after_chat, :continue_wrap_up_after_disposition,
    :message_preview, :auto_load_crm_case, :post_session_transfer, :display_history, :realtime_redaction,
    :hide_va_to_va_system_messages
  ]

  attr_reader :setting_params

  before_action :fetch_setting_params, only: [:update]

  def show
    render_setting
  end

  def update
    safe_company_update do |company|
      SettingsService::ChatSettingsUpdater.new(company, setting_params, current_user).call
    end

    render_setting
  end

  private

  def authorize_setting
    authorize current_company.chat_settings
  end

  def fetch_setting_params
    @setting_params = params.require(:chat_setting).permit(policy(Settings::ChatSetting).permitted_attributes)

    # TODO: this fixes a client/backend mismatch in parameter name. To be removed in THIS-1050
    allow_transfer_after_hours = @setting_params.dig('deflection', 'after_hours', 'allow_transfer_after_hours')
    return if allow_transfer_after_hours.nil?

    @setting_params['deflection']['after_hours']['allow_transfer'] = allow_transfer_after_hours
    @setting_params['deflection']['after_hours'].delete('allow_transfer_after_hours')
  end

  def render_setting
    chat_setting = current_company.chat_settings
    render json: Settings::ChatSettingRepresenter.for_user(current_user).new(chat_setting)
  end
end

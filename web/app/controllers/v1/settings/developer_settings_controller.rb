class V1::Settings::DeveloperSettingsController < V1::Settings::BaseController

  def show
    render_setting
  end

  def update
    crm_type = params.dig(:developer_setting, :crm)

    if crm_type.present?
      begin
        current_company.crm_type = crm_type
      rescue ArgumentError => e
        raise ServiceException, e.message
      end
    end

    # Need in case we need to revert settings for Admin Token
    previous_settings = {
      default_access_token: current_company.default_access_token,
      default_access_secret: current_company.default_access_secret
    }

    setting_params = params.require(:developer_setting).permit(
      :sfdc_org_name, :sfdc_org_id, :sfdc_org_email, :sfdc_schedule_email_template, :sfdc_reschedule_email_template,
      :zendesk_subdomain, :oauth_client_id, :oauth_client_secret,
      :crm_subdomain, :default_access_token, :default_access_secret,
      :cobrowse
    )

    if crm_type == 'custom'
      current_company.crm_settings.custom_crm.lookup_method = params[:developer_setting][:crm_settings][:custom_crm][:lookup_method].to_s
      current_company.crm_settings.custom_crm.lookup_url_with_phone_number = params[:developer_setting][:crm_settings][:custom_crm][:lookup_url_with_phone_number].to_s
      current_company.crm_settings.custom_crm.lookup_url_with_cuid = params[:developer_setting][:crm_settings][:custom_crm][:lookup_url_with_cuid].to_s
      current_company.crm_settings.custom_crm.lookup_url_phone_number_format = params[:developer_setting][:crm_settings][:custom_crm][:lookup_url_phone_number_format].to_s
      current_company.crm_settings.custom_crm.send_outcome_via_email = params[:developer_setting][:crm_settings][:custom_crm][:send_outcome_via_email].to_s
      current_company.crm_settings.custom_crm.email = params[:developer_setting][:crm_settings][:custom_crm][:email].to_s
      current_company.crm_settings.custom_crm.auth_method = params[:developer_setting][:crm_settings][:custom_crm][:auth_method].to_s
      unless params[:developer_setting][:crm_settings][:custom_crm][:basic_auth_password].blank?
      #  dont update basic auth username/password in case of no new password entry
        current_company.crm_settings.custom_crm.basic_auth_username = params[:developer_setting][:crm_settings][:custom_crm][:basic_auth_username].to_s
        current_company.crm_settings.custom_crm.basic_auth_password = params[:developer_setting][:crm_settings][:custom_crm][:basic_auth_password].to_s
      end
      current_company.crm_settings.custom_crm.custom_headers = params[:developer_setting][:crm_settings][:custom_crm][:custom_headers].to_s
      current_company.crm_settings.custom_crm.oauth_client_id = params[:developer_setting][:crm_settings][:custom_crm][:oauth_client_id].to_s
      unless params[:developer_setting][:crm_settings][:custom_crm][:oauth_client_secret].blank?
        current_company.crm_settings.custom_crm.oauth_client_secret = params[:developer_setting][:crm_settings][:custom_crm][:oauth_client_secret].to_s
      end
      current_company.crm_settings.custom_crm.oauth_authorization_url = params[:developer_setting][:crm_settings][:custom_crm][:oauth_authorization_url].to_s
      current_company.crm_settings.custom_crm.oauth_token_url = params[:developer_setting][:crm_settings][:custom_crm][:oauth_token_url].to_s
      current_company.crm_settings.custom_crm.oauth_scope = params[:developer_setting][:crm_settings][:custom_crm][:oauth_scope].to_s
      current_company.crm_settings.custom_crm.oauth_audience = params[:developer_setting][:crm_settings][:custom_crm][:oauth_audience].to_s
      current_company.crm_settings.custom_crm.oauth_state = params[:developer_setting][:crm_settings][:custom_crm][:oauth_state].to_s
      current_company.crm_settings.custom_crm.oauth_access_type = params[:developer_setting][:crm_settings][:custom_crm][:oauth_access_type].to_s
      current_company.crm_settings.custom_crm.oauth_grant_type = params[:developer_setting][:crm_settings][:custom_crm][:oauth_grant_type].to_s
      current_company.crm_settings.custom_crm.oauth_include_grant_type = params[:developer_setting][:crm_settings][:custom_crm][:oauth_include_grant_type]
      current_company.crm_settings.custom_crm.oauth_use_redirect_url = params[:developer_setting][:crm_settings][:custom_crm][:oauth_use_redirect_url]
      current_company.crm_settings.custom_crm.api_request_timeout = params[:developer_setting][:crm_settings][:custom_crm][:api_request_timeout]
      current_company.crm_settings.custom_crm.contact_lookup_url = params[:developer_setting][:crm_settings][:custom_crm][:contact_lookup_url].to_s
      current_company.crm_settings.custom_crm.case_lookup_url = params[:developer_setting][:crm_settings][:custom_crm][:case_lookup_url].to_s
      current_company.crm_settings.custom_crm.crm_iframe_enabled = params[:developer_setting][:crm_settings][:custom_crm][:crm_iframe_enabled]
      current_company.crm_settings.custom_crm.crm_iframe_url = params[:developer_setting][:crm_settings][:custom_crm][:crm_iframe_url].to_s
    end
    if crm_type == 'servicenow'
      validate_servicenow_report_object_type(params[:developer_setting])
      current_company.crm_settings.base_record_object_type = params[:developer_setting][:crm_settings][:base_record_object_type]
      current_company.crm_settings.end_user_contact_field = params[:developer_setting][:crm_settings][:contact_field]
      current_company.crm_settings.auth_method = params[:developer_setting][:crm_settings][:auth_method]
    end
    if params.dig(:developer_setting, :crm_mappings)&.key?(:batch_frequency)
      current_company.crm_settings.batch_frequency = params[:developer_setting][:crm_mappings][:batch_frequency]
    end
    if params[:developer_setting][:crm_settings][:followup_ticket_status]
      current_company.crm_settings.followup_ticket_status = params[:developer_setting][:crm_settings][:followup_ticket_status]
    end
    if params[:developer_setting][:crm_settings][:phone_number_format]
      current_company.crm_settings.phone_number_format = params[:developer_setting][:crm_settings][:phone_number_format]
    end
    current_company.crm_settings.batch_add_comment = params[:developer_setting][:crm_mappings][:batch_add_comment]
    current_company.crm_settings.batch_update_assignment = params[:developer_setting][:crm_mappings][:batch_update_assignment]

    current_company.crm_settings.api_batching_enabled = params[:developer_setting][:crm_settings][:api_batching_enabled]

    if crm_type == 'zendesk' && (params[:developer_setting][:default_access_token].blank? || params[:developer_setting][:default_access_secret].blank?)
      current_company.crm_settings.use_admin_user = false
    else
      current_company.crm_settings.use_admin_user = params.dig(:developer_setting, :use_admin_user) || false
    end

    if crm_type == 'dynamics'
      if setting_params[:oauth_client_id].blank? || setting_params[:oauth_client_secret].blank?
        current_company.crm_settings.use_admin_user = false
      end

      current_company.crm_settings.use_custom_contact_to_ticket_association = params.dig(:developer_setting, :crm_settings, :use_custom_contact_to_ticket_association) || false
      if current_company.crm_settings.use_custom_contact_to_ticket_association
        permitted = params.require(:crm_settings).permit(custom_contact_to_ticket_association_config:[:account_obj, :account_name, :account_api, :account_attribute_type, :account_targets,
                                                                                                      :record_obj, :record_name, :record_api, :record_attribute_type, :record_targets])
        current_company.crm_settings.custom_contact_to_ticket_association_config = permitted[:custom_contact_to_ticket_association_config]
        current_company.crm_settings.use_default_record_when_custom_mapping_fails = params.dig(:developer_setting, :crm_settings, :use_default_record_when_custom_mapping_fails) || false
      end
    end

    if crm_type == 'oracle'
      # use_admin_user is always true for oracle
      current_company.crm_settings.use_admin_user = true

      # encode default_access_secret
      if setting_params[:default_access_secret].blank?
        # do not change credentials when password is empty
        setting_params[:default_access_token] = previous_settings[:default_access_token]
        setting_params[:default_access_secret] = previous_settings[:default_access_secret]
      else
        setting_params[:default_access_secret] = Ujet.encryption_service.encrypt({password: setting_params[:default_access_secret]})
      end
    end

    if crm_type == 'freshdesk' || crm_type == 'hubspot'
      # use_admin_user is always true for freshdesk and hubspot
      current_company.crm_settings.use_admin_user = true

      # freshdesk/hubspot does not need default_access_secret
      # let's use a dummy
      previous_settings[:default_access_secret] = 'X'
      setting_params[:default_access_secret] = 'X'
    end

    if crm_type == 'sap'
      current_company.crm_settings.use_admin_user = true
      current_company.crm_settings.end_user_account_creation_type = params[:developer_setting][:crm_settings][:end_user_account_creation_type]
    end
    current_company.crm_settings.use_admin_user = true if crm_type == 'sap' || crm_type == 'servicenow'

    if crm_type == 'salesforce' && params.dig(:developer_setting, :sf_admin_token, :found) && current_company.crm_settings.use_admin_user
      # To avoid both use_admin_user and use_admin_user_for_all_interactions being enabled
      current_company.crm_settings.use_admin_user_for_all_interactions = params.dig(:developer_setting, :use_admin_user_for_all_interactions) || false
    else
      current_company.crm_settings.use_admin_user_for_all_interactions = false
    end

    current_company.crm_settings.selected_pipeline = params[:developer_setting][:crm_settings][:selected_pipeline].to_s
    current_company.crm_settings.selected_pipeline_stage = params[:developer_setting][:crm_settings][:selected_pipeline_stage].to_s

    current_company.crm_settings.phone_custom_field_id = params[:developer_setting][:crm_mappings][:phone_custom_field_id]
    current_company.crm_settings.is_phone_lookup_by_custom_field = params[:developer_setting][:crm_mappings][:is_phone_lookup_by_custom_field]

    current_company.crm_settings.use_custom_field_matching = params[:developer_setting][:crm_settings][:use_custom_field_matching]
    if current_company.crm_settings.use_custom_field_matching
      current_company.crm_settings.matching_crm_field = params[:developer_setting][:crm_settings][:matching_crm_field]
    else
      current_company.crm_settings.matching_crm_field = ''
    end

    current_company.crm_settings.use_amb_custom_field_matching = params[:developer_setting][:crm_settings][:use_amb_custom_field_matching]
    if current_company.crm_settings.use_amb_custom_field_matching
      current_company.crm_settings.amb_custom_field_id = params[:developer_setting][:crm_settings][:amb_custom_field_id]
    else
      current_company.crm_settings.amb_custom_field_id = ''
    end

    current_company.crm_settings.use_custom_end_user_object = params[:developer_setting][:crm_settings][:use_custom_end_user_object]
    current_company.crm_settings.end_user_phone_type = params.dig(:developer_setting, :crm_settings, :end_user_phone_type) || 'Phone'
    current_company.crm_settings.end_user_phone_types = params.dig(:developer_setting, :crm_settings, :end_user_phone_types) || ['Phone']
    current_company.crm_settings.end_user_primary_phone_type = params.dig(:developer_setting, :crm_settings, :end_user_primary_phone_type) || 'Phone'
    current_company.crm_settings.use_custom_record_session_object = params.dig(:developer_setting, :crm_settings, :use_custom_record_session_object) || false
    current_company.crm_settings.end_user_direct_account_lookup = params.dig(:developer_setting, :crm_settings, :end_user_direct_account_lookup) || false
    if current_company.crm_settings.end_user_direct_account_lookup
      current_company.crm_settings.enabled_multi_cases_detect_outbound_calls = false
      current_company.crm_settings.enabled_create_new_case_option_outbound_calls = false
      current_company.crm_settings.enable_no_case_option_outbound_calls = false
    end

    current_company.crm_settings.end_user_object = params.dig(:developer_setting, :crm_settings, :end_user_object) || 'Contact'
    current_company.crm_settings.end_user_record_type = params.dig(:developer_setting, :crm_settings, :end_user_record_type) #deprecated
    current_company.crm_settings.end_user_record_types = params.dig(:developer_setting, :crm_settings, :end_user_record_types)
    current_company.crm_settings.crm_sub_type = params.dig(:developer_setting, :crm_settings, :crm_sub_type)
    current_company.crm_settings.lookup_field_encryption = params.dig(:developer_setting, :crm_settings, :lookup_field_encryption)

    if current_company.crm_settings.crm_sub_type == "salescloud"
      current_company.crm_settings.ticket_object_type = 'Opportunity'
    else
      current_company.crm_settings.ticket_object_type = 'Case'
    end

    if current_company.crm_settings.end_user_object == 'Person Account' && current_company.crm_settings.end_user_record_types.blank?
      raise ServiceException, 'Please select Record Types'
    end

    current_company.crm_settings.use_sip_header_contact_id_matching = params.dig(:developer_setting, :crm_settings, :use_sip_header_contact_id_matching)
    current_company.crm_settings.sip_header_contact_id = params.dig(:developer_setting, :crm_settings, :sip_header_contact_id)
    current_company.crm_settings.sip_header_crm_contact_id_field = params.dig(:developer_setting, :crm_settings, :sip_header_crm_contact_id_field)

    current_company.crm_settings.salesforce_metadata_mapping = params.dig(:developer_setting, :crm_settings, :salesforce_metadata_mapping) || 'Activity Object'

    current_company.crm_settings.use_lightning_url = params.dig(:developer_setting, :crm_settings, :use_lightning_url)
    current_company.crm_settings.opportunity_close_days = params.dig(:developer_setting, :crm_settings, :opportunity_close_days)

    current_company.crm_settings.external_id_prefix = params.dig(:developer_setting, :crm_settings, :external_id_prefix)&.first(20)

    if crm_type == 'custom'
      current_company.crm_settings.session_metadata_file_to_integrated_crm = false
      if current_company.crm_settings.custom_crm.lookup_method == 'customLink'
        current_company.crm_settings.enable_prompt_account_record_selection_inbound_calls = false
        current_company.crm_settings.enable_new_account_creation_inbound_calls = false
        current_company.crm_settings.inbound_calls_auto_select_account_timing = 'wrapup'
      end
    else
      current_company.crm_settings.session_metadata_file_to_integrated_crm = params.dig(:developer_setting, :crm_settings, :session_metadata_file_to_integrated_crm) || false
    end

    # ticket_display_fields, opportunity_display_fields
    if current_company.crm_type == :salesforce
      if params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :first_display_field).present?
        current_company.crm_settings.ticket_display_fields.object_type = params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :object_type)
        current_company.crm_settings.ticket_display_fields.first_display_field = params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :first_display_field)
        current_company.crm_settings.ticket_display_fields.second_display_enabled = params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :second_display_enabled)
        current_company.crm_settings.ticket_display_fields.second_display_field = params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :second_display_field)
        current_company.crm_settings.ticket_display_fields.third_display_enabled = params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :third_display_enabled) && current_company.crm_settings.ticket_display_fields.second_display_enabled
        current_company.crm_settings.ticket_display_fields.third_display_field = params.dig(:developer_setting, :crm_settings, :ticket_display_fields, :third_display_field)
      else
        current_company.crm_settings.ticket_display_fields.object_type = 'Case'
        current_company.crm_settings.ticket_display_fields.first_display_field = 'Id'
        current_company.crm_settings.ticket_display_fields.second_display_enabled = true
        current_company.crm_settings.ticket_display_fields.second_display_field = 'Subject'
        current_company.crm_settings.ticket_display_fields.third_display_enabled = false
        current_company.crm_settings.ticket_display_fields.third_display_field = nil
      end

      if params.dig(:developer_setting, :crm_settings, :opportunity_display_fields, :first_display_field).present?
        current_company.crm_settings.opportunity_display_fields.object_type = 'Opportunity'
        current_company.crm_settings.opportunity_display_fields.first_display_field = params.dig(:developer_setting, :crm_settings, :opportunity_display_fields, :first_display_field)
        current_company.crm_settings.opportunity_display_fields.second_display_enabled = params.dig(:developer_setting, :crm_settings, :opportunity_display_fields, :second_display_enabled)
        current_company.crm_settings.opportunity_display_fields.second_display_field = params.dig(:developer_setting, :crm_settings, :opportunity_display_fields, :second_display_field)
        current_company.crm_settings.opportunity_display_fields.third_display_enabled = params.dig(:developer_setting, :crm_settings, :opportunity_display_fields, :third_display_enabled) && current_company.crm_settings.opportunity_display_fields.second_display_enabled
        current_company.crm_settings.opportunity_display_fields.third_display_field = params.dig(:developer_setting, :crm_settings, :opportunity_display_fields, :third_display_field)
      else
        current_company.crm_settings.opportunity_display_fields.object_type = 'Opportunity'
        current_company.crm_settings.opportunity_display_fields.first_display_field = 'Id'
        current_company.crm_settings.opportunity_display_fields.second_display_enabled = true
        current_company.crm_settings.opportunity_display_fields.second_display_field = 'Name'
        current_company.crm_settings.opportunity_display_fields.third_display_enabled = false
        current_company.crm_settings.opportunity_display_fields.third_display_field = nil
      end

      current_company.crm_settings.end_user_direct_task_object_lookup = params.dig(:developer_setting, :crm_settings, :end_user_direct_task_object_lookup) || false
      current_company.crm_settings.end_user_direct_unattached_task_object_lookup = params.dig(:developer_setting, :crm_settings, :end_user_direct_unattached_task_object_lookup) || false
      if current_company.crm_settings.end_user_direct_task_object_lookup || current_company.crm_settings.end_user_direct_unattached_task_object_lookup
        current_company.crm_settings.ticket_object_type = 'Task'
        current_company.crm_settings.create_new_ticket_always = true #task objects can't be reused
      end
    end

    primary_crm_lookup_settings = params.dig(:developer_setting, :crm_lookup_settings, 0)

    if ((crm_type == 'salesforce' && FeatureFlag.on?('salescloud-flex-mapping')) || crm_type == 'servicenow') && primary_crm_lookup_settings.present?
      #backward compatibility: when turning off salescloud-flex-mapping from on
      current_company.crm_settings.end_user_object = primary_crm_lookup_settings[:object_type]
      current_company.crm_settings.end_user_record_type = primary_crm_lookup_settings[:record_type]
      current_company.crm_settings.end_user_record_types = primary_crm_lookup_settings[:record_types]
      current_company.crm_settings.end_user_phone_type = primary_crm_lookup_settings[:phone_field]
      current_company.crm_settings.end_user_phone_types = primary_crm_lookup_settings[:phone_fields]
      current_company.crm_settings.end_user_primary_phone_type = primary_crm_lookup_settings[:phone_number_primary_field]
      current_company.crm_settings.matching_crm_field = primary_crm_lookup_settings[:sdk_identifier_lookup_field]
      current_company.crm_settings.amb_custom_field_id = primary_crm_lookup_settings[:amb_custom_field_id]
      current_company.crm_settings.use_amb_custom_field_matching = primary_crm_lookup_settings[:use_amb_custom_field_matching]
    end

    # Todo: add it for servicenow when implementing account lookup.
    if current_company.crm_type == :salesforce || current_company.crm_type == :sap || current_company.crm_type == :servicenow
      validate_primary_phone_field(params[:developer_setting])
      update_crm_lookup_settings(populate_lookup_settings(params[:developer_setting]))
    end

    # update
    update_current_company(setting_params)

    # Check after param data has been set to current_company
    return render_setting unless invalid_admin_token(crm_type, previous_settings)

    handle_invalid_admin_token(crm_type)
  end

  def validate_servicenow_report_object_type(developer_settings)
    if developer_settings[:crm_settings][:crm_sub_type] == 'custom' && developer_settings[:crm_lookup_settings][0][:record_type].blank?
      raise ServiceException, 'Please select Record Object Type'
    end
  end
  #ToDo:  add validation for service now lookup.
  def validate_primary_phone_field(developer_settings)
    return unless current_company.crm_type == :salesforce || current_company.crm_type == :servicenow

    lookup_settings = developer_settings[:crm_lookup_settings]
    crm_settings = developer_settings[:crm_settings]

    unless crm_settings[:end_user_phone_types].include?(crm_settings[:end_user_primary_phone_type])
      crm_settings[:end_user_primary_phone_type] = crm_settings[:end_user_phone_types][0]
    end

    if lookup_settings.present? && lookup_settings[0].present? && !lookup_settings[0][:phone_fields]&.include?(lookup_settings[0][:phone_number_primary_field])
      lookup_settings[0][:phone_number_primary_field] = lookup_settings[0][:phone_fields][0]
    end
  end

  # GET v1/settings/developer/secret
  def show_secret
    render json: {secret: current_company.secret}
  end

  # POST v1/settings/developer/secret/regenerate
  def regenerate_secret
    current_company.generate_secret
    current_company.save!

    render json: {secret: current_company.secret}
  end

  # POST v1/settings/developer/test_push_notifications
  def send_test_push_notification
    data = params.permit(:device_token, :device_token_voip, :device_token_general)

    # Check device is available
    if params[:device_token].present?
      device = Device.where(device_token: data[:device_token]).first
      is_voip = false
    elsif params[:device_token_voip].present?
      device = Device.where(device_token_voip: data[:device_token_voip]).first
      is_voip = true
    elsif params[:device_token_general].present?
      device = Device.where(device_token_general: data[:device_token_general]).first
      is_voip = false
    end

    if device.blank?
      raise ServiceException, "No device registered. Please relaunch your mobile App and UJET sdk"
    end

    # Check push setup
    finder = Push::CredentialsFinder::Factory.create_credentials_finder(device: device, is_voip: is_voip)
    response = Push::Noti.test_message(finder: finder)
    render json: { "response": response }
  end

  # POST v1/settings/developer/create_custom_fields/
  def create_custom_fields
    case current_company.crm_type
    when :zendesk
      create_zendesk_custom_fields
    when :hubspot
      create_hubspot_custom_fields
    when :kustomer
      create_kustomer_custom_fields
    when :servicenow
      create_servicenow_custom_fields
    else
      raise ServiceException, 'Creating custom fields is not supported'
    end
  end

  def create_hubspot_custom_fields
    validate_hubspot_admin_token
    CRM::CrmServer.request(url: '/create_custom_fields', method: :post)
    render json: nil
  end

  def create_servicenow_custom_fields
    validate_servicenow_admin_token
    CRM::CrmServer.request(url: '/create_custom_fields', method: :post)
    render json: nil
  end

  def validate_servicenow_admin_token
    if current_company.crm_settings.auth_method == 'basic' && (current_company.default_access_token.blank? || current_company.default_access_secret.blank?)
      raise ServiceException, 'Please put your ServiceNow username and password'
    end

    if current_company.crm_settings.auth_method == 'oauth' && !Redis::CRMAdapter.current.exists?("auth_tokens", "admin_token")
      raise ServiceException, 'Please save your ServiceNow oauth credentials and link your user'
    end

    begin
      CRM::CrmServer.request(url: '/validate_admin_token', method: :get, user_id: 'admin_token')
    rescue StandardError, RestClient::ExceptionWithResponse => e
      # crm-server returns 407 error if the credential is invalid.
      # ServiceException will send bad_request status code in ErrorHandling.
      Rails.logger.warn(error: e) { "Error in validate_servicenow_admin_token : #{e.message}" }
      raise ServiceException, 'Provided admin user does not have admin permissions. Please update permissions in ServiceNow or enter a different user name.'
    end
  end

  def create_zendesk_custom_fields
    if current_company.default_access_token.blank? || current_company.default_access_secret.blank?
      raise ServiceException, 'Please put your Zendesk email address and API Token'
    end

    begin
      CRM::CrmServer.request(url: '/verify_admin_token', method: :get, user_id: 'admin_token')
    rescue StandardError, RestClient::ExceptionWithResponse => e
      # crm-server returns 407 error if the token is invalid.
      # ServiceException will send bad_request status code in ErrorHandling.
      Rails.logger.warn(error: e) { "Error in create_zendesk_custom_fields : #{e.message}" }
      raise ServiceException, 'Provided admin user does not have admin permissions. Please update permissions in Zendesk or enter a different user email.'
    end
    CRM::CrmServer.request(url: '/create_custom_fields', method: :post)
    render json: nil
  end

  def create_kustomer_custom_fields
    raise ServiceException, 'Please put your API Key' if current_company.default_access_token.blank?

    CRM::CrmServer.request(url: '/create_custom_fields', method: :post)
    render json: nil

  rescue StandardError, RestClient::ExceptionWithResponse => e
    # ServiceException will send bad_request status code in ErrorHandling.
    Rails.logger.warn(error: e) { "Error in create_kustomer_custom_fields : #{e.message}" }
    raise ServiceException, 'An error has occurred'
  end

  # POST v1/settings/developer/test_api_dap
  def test_api_dap
    data = params.permit(:url, :username, :password, :auth_method, :phone_number, :timeout, :phone_number_format,
                         :request_parameter, :custom_headers, :request_method, :data_parameters_enabled,
                         data_parameters: [:field, :type, :value, :source, :source_field])
                 .to_h.symbolize_keys
    settings = current_company.external_api_settings
    if data[:auth_method] == 'basic'
      data[:password] = settings.password if data[:password].blank?
      data[:username] = settings.username if data[:username].blank?
    end

    if data[:auth_method] == 'oauth'
      unless current_company.crm_type == :salesforce
        render plain: 'API DAP must use basic authentication if Salesforce is not the CRM being used.'
        return
      end

      token = oauth_token
      if token.blank?
        render plain: 'There is no OAuth token in UJET system yet. Please sign in through Salesforce Agent widget'
        return
      end
    end

    begin
      phone_number_format = settings.normalize_phone_number_format(data[:phone_number_format])
      input_phone_number = convert_phone_number_to_twilio(data[:phone_number])
      normalized_phone_number = normalize_phone_number_for_dap(input_phone_number, phone_number_format)

      # request_body is for showing actual request body in admin portal
      parameter = data[:request_parameter] || 'phone'
      url = data[:url]
      url = url.gsub('{phone}', CGI.escape(data[:phone_number])) if url.include? '{phone}'
      if data[:request_method] == 'post'
        request_body = { parameter => normalized_phone_number }

        if data[:data_parameters_enabled]
          additional_parameters = data[:data_parameters].each_with_object({}) do |param, hash|
            if param['type'] == 'fixed'
              hash[param['field']] = param['value']
            elsif param['type'] == 'dynamic'
              hash[param['field']] = ''
            end
          end

          request_body = request_body.merge(additional_parameters)
        end
        request_body = request_body.to_json
      else
        request_body = Api::Dap.format_url(url: url, parameters: [data[:request_parameter], CGI.escape(normalized_phone_number)])
      end

      response = Api::Dap.request(url: url,
                                  username: data[:username],
                                  password: data[:password],
                                  custom_headers: JSON.parse(data[:custom_headers]),
                                  phone_number: normalized_phone_number,
                                  auth_method: data[:auth_method],
                                  request_parameter: data[:request_parameter],
                                  request_method: data[:request_method],
                                  timeout: data[:timeout],
                                  data_parameters: data[:data_parameters]
      )
      dap = find_api_dap(response: JSON.parse(response))
      render json: { response: response, found_menu_name: dap&.menu&.name, request_body: request_body }
    rescue Exception => e
      render json: { error: e.to_s, request_body: request_body }
    end
  end

  # GET v1/settings/developer/salesforce_admin_token_check
  def salesforce_admin_token_check
    statuses = admin_token_statuses
    oauth = CRM::Client::Salesforce.oauth_auth(user_id: auth.user.id, callback_domain: URLHelper.base_url(request, remove_api: true))
    render json: {
      response: {
        found: !statuses.empty?,
        statuses: statuses,
        authorization_url: oauth['authorization_url'],
        login_url: current_company.crm_settings.salesforce_login_url || ''
      }
    }
  rescue RestClient::ExceptionWithResponse => e
    render plain: e.response.try(:to_s)
  end

  # PUT v1/settings/developer/salesforce_login_url
  def salesforce_login_url_save
    current_company.crm_settings.salesforce_login_url = params[:salesforce_login_url]
    current_company.save
    current_company.update_crm_settings
    render json: nil
  rescue RestClient::ExceptionWithResponse => e
    render plain: e.response.try(:to_s)
  end

  # DELETE v1/settings/developer/salesforce_admin_token_unlink
  def salesforce_admin_token_unlink
    crm_credential = CRM::AdminCredential.fetch_for_key(key: params['user_id'])
    crm_credential.destroy
    admin_tokens_exist = CRM::AdminCredential.does_exist?
    turn_off_admin_user unless admin_tokens_exist
    render json: {
      disable_admin_user: !admin_tokens_exist
    }
  end

  # GET v1/settings/developer/salesforce_custom_object
  def salesforce_custom_objects
    if CRM::Credential.does_exist?
      custom_object_feature = CRM::Client::Salesforce.is_feature_enabled(feature_type: 'salesforce_custom_objects')
      custom_object_feature = salesforce_custom_objects_default_results if custom_object_feature.nil?
    else
      custom_object_feature = salesforce_custom_objects_default_results
    end
    render json: {
      data: custom_object_feature
    }
  end

  def show_frame_ancestors
    render json: FrameAncestorsService.get
  end

  def save_frame_ancestors
    render json: FrameAncestorsService.set(params[:frame_ancestors].reject(&:blank?))
  end

  def get_crm_fields
    crm_fields = CRM::CrmServer.request(
      url: '/get_fields',
      method: :post,
      params: {
        object_type: params['object_type'].presence || end_user_object_type,
        field_type: params['field_type'],
        force_object_type: params['force_object_type'],
        include_standard_fields: params['include_standard_fields']
      }
    )
    filtered_crm_fields =
      current_company.amb_enabled? ? crm_fields : crm_fields.reject { |field| field['name'] =~ /amb/i }
    render json: filtered_crm_fields
  rescue StandardError, RestClient::ExceptionWithResponse => e
    # ServiceException will send bad_request status code in ErrorHandling.
    Rails.logger.warn(error: e) { "Error in get_crm_fields : #{e.message}" }
    raise ServiceException, 'An error has occurred'
  end

  def get_crm_objects
    render json: CRM::CrmServer.request(url: '/get_objects', method: :get)
  rescue StandardError, RestClient::ExceptionWithResponse => e
    # ServiceException will send bad_request status code in ErrorHandling.
    Rails.logger.warn(error: e) { "Error in get_crm_objects : #{e.message}" }
    raise ServiceException, 'An error has occurred'
  end

  def get_crm_record_types
    render json: CRM::CrmServer.request(url: '/get_record_types', method: :post, params: params)
  rescue StandardError, RestClient::ExceptionWithResponse => e
    # ServiceException will send bad_request status code in ErrorHandling.
    Rails.logger.warn(error: e) { "Error in get_crm_record_types : #{e.message}" }
    raise ServiceException, 'An error has occurred'
  end

  def crm_lookup_settings
    render json: CrmLookupRepresenter.new(CrmLookup.first).represented
  rescue StandardError, RestClient::ExceptionWithResponse => e
    # ServiceException will send bad_request status code in ErrorHandling.
    Rails.logger.warn(error: e) { "Error in get_crm_record_types : #{e.message}" }
    raise ServiceException, 'An error has occurred'
  end

  def get_crm_feature_enabled
    render json: CRM::CrmServer.request(url: '/is_feature_enabled', method: :post, params: {featureType: params['feature_type']})
  rescue
    render json: {result: false}
  end

  def google_oauth_url
    settings = params
    settings[:external_storage_google_scope] = ['https://www.googleapis.com/auth/devstorage.read_write']
    render json: CRM::CrmServer.request(url: '/google_oauth_url', method: :post, params: settings)
  rescue StandardError, RestClient::ExceptionWithResponse => e
    # ServiceException will send bad_request status code in ErrorHandling.
    Rails.logger.warn(error: e) { "Error in google_oauth_url : #{e.message}" }
    raise ServiceException, 'An error has occurred'
  end

  def save_outbound_payment_settings
    current_company.call_settings.outbound_payments.enabled = params.dig(:enabled) || false
    current_company.call_settings.outbound_payments.payment_provider_id = params.dig(:payment_provider_id) || nil
    current_company.call_settings.outbound_payments.zip_code_enabled = params.dig(:zip_code_enabled) || false
    current_company.call_settings.outbound_payments.default_currency = params.dig(:default_currency) || nil

    current_company.save
    render json: current_company.call_settings.outbound_payments
  end

  def show_outbound_payment_settings
    render json: current_company.call_settings.outbound_payments
  end

  def save_acqueon_settings
    current_company.acqueon_settings.enabled = params.dig(:enabled) || false
    current_company.acqueon_settings.url = params.dig(:url)
    current_company.acqueon_settings.app_key = params.dig(:app_key)
    current_company.acqueon_settings.secret = params.dig(:secret)
    current_company.acqueon_settings.contact_crm_page = params.dig(:contact_crm_page)

    (params.dig(:regular_preview_button_settings) || {}).each do
    |k,v| current_company.acqueon_settings.regular_preview_button_settings.public_send("#{k}=", v)
    end

    (params.dig(:callback_preview_button_settings) || {}).each do
    |k,v| current_company.acqueon_settings.callback_preview_button_settings.public_send("#{k}=", v)
    end

    (params.dig(:personal_callback_preview_button_settings) || {}).each do
    |k,v| current_company.acqueon_settings.personal_callback_preview_button_settings.public_send("#{k}=", v)
    end

    (params.dig(:aem_preview_button_settings) || {}).each do
    |k,v| current_company.acqueon_settings.aem_preview_button_settings.public_send("#{k}=", v)
    end

    current_company.save
    render json: current_company.acqueon_settings
  end

  def show_acqueon_settings
    render json: current_company.acqueon_settings
  end

  def save_cobrowse_settings
    data = current_company.settings.cobrowse
    data.domain = params[:domain] if (params.dig(:domain))
    data.license = params[:license] if (params.dig(:license))
    data.private_key = params[:private_key] if (params.dig(:private_key))
    data.enabled = params[:enabled] unless (params.dig(:enabled).nil?)

    current_company.settings.cobrowse = data
    current_company.save
    render json: current_company.settings
  end

  def save_external_storage
    if params[:external_storage_google_auth_type] == 'service_account_customer' && params[:external_storage_google_credentials_json].present?
      begin
        # TODO: save `external_storage_google_credentials_json` to external_storage_settings instead of crm_settings.
        credentials = params[:external_storage_google_credentials_json]
        credentials.rewind
        credentials = credentials.read
        # Checks if the credentials are a valid JSON file
        JSON.parse(credentials)
        current_company.crm_settings.media_service.external_storage_google_credentials_json = credentials
        current_company.save
        current_company.update_crm_settings
      rescue JSON::ParserError => _e
        raise ServiceException, 'The uploaded key is not a valid JSON file.'
      end
    end

    SettingsService::CcaiSettingsUpdater.new(
      current_company,
      params[:external_storage_enabled],
      params[:external_storage_selection]
    ).activate_ccai_setting

    settings_from_api = params.permit(*Company.current.external_storage_settings.attributes.keys, external_storage_session_data_feeds_langs: [])
    # Some settings may not be passed from API. Take the current values and merge onto it the payload from API
    settings = current_company.external_storage_settings.attributes.merge(settings_from_api)
    # Don't save other settings if enable storage is false
    settings = { external_storage_enabled: params[:external_storage_enabled] } if params[:external_storage_enabled] == false
    current_company.external_storage_settings = settings
    current_company.save!
    render json: { success: true, data: current_company.external_storage_settings.sanitized_attributes }
  end

  def get_external_storage
    render json: { success: true, data: current_company.external_storage_settings.sanitized_attributes }
  end

  def save_ccai_insight
    permitted_params = params.permit(:enabled, :ccai_project_id, :bucket_name, :region, :send_call_recordings,
                                     :send_chat_transcripts, :setup_type, :account_key, :account_key_file_name,
                                     :account_key_changed, :folder_path)
    raise ServiceException, 'CCAI Project ID is required' if params[:ccai_project_id].blank?
    raise ServiceException, 'Region is required' if params[:region].blank?
    raise ServiceException, 'Setup Type is required' if params[:setup_type].blank?

    permitted_params[:setup_type] = Integer(permitted_params[:setup_type])
    if (permitted_params[:setup_type]).zero?
      raise ServiceException, 'account key is empty' if permitted_params[:account_key_changed] && !permitted_params[:account_key].present?
      raise ServiceException, 'Bucket Name is required' if permitted_params[:bucket_name].blank?
    end

    account_key_changed = permitted_params[:account_key_changed]

    if permitted_params[:setup_type].zero? && account_key_changed == 'true'
      credentials = permitted_params[:account_key]
      permitted_params[:account_key_file_name] = credentials.original_filename
      credentials.rewind
      credentials = credentials.read
      permitted_params[:account_key] = credentials
    else
      ccai_insight_settings = current_company.ccai_insight_settings
      permitted_params[:account_key] = ccai_insight_settings.account_key
    end

    permitted_params.delete('account_key_changed')

    # trying to save to media server first. Letting it fail first in case media server's not responding.
    settings_response = CRM::MediaService.save_settings_ccai_insight(params: permitted_params)

    current_company.ccai_insight_settings = permitted_params.to_json
    current_company.save

    SettingsService::CallSettingsUpdater
      .new(current_company, permitted_params[:enabled])
      .activate_dual_channel_recording

    settings_response['data'].delete('account_key') if settings_response['data'].present? # Don't return this value to the frontend.
    render json: settings_response
  end

  def get_ccai_insight
    settings = Company.current.ccai_insight_settings
    render json: { success: true, data: settings }, except: ['account_key'] # Don't return this value to the frontend.
  end

  # GET v1/settings/developer/hubspot_sales_cloud_pipeline_objects
  def hubspot_sales_cloud_pipeline_objects
    handle_invalid_admin_token('hubspot') if invalid_default_admin_token(nil, 'hubspot')

    render json: { success: true, data: CRM::CrmServer.request(url: '/get_pipelines', method: :get) }
  rescue RestClient::PreconditionFailed
    # crm-server returns 412 if there is a timeout, we want to retry in that case
    render json: { success: false, data: nil, retry: true }
  rescue ServiceException => e
    render json: { success: false, data: nil, retry: false, message: e.message }
  end

  def save_survey_settings
    crm_settings = current_company.crm_settings

    crm_settings.survey.add_comment = params.dig(:add_comment)
    crm_settings.survey.attach_json = params.dig(:attach_json)
    crm_settings.survey.json_to_external_storage = params.dig(:json_to_external_storage)

    update_current_company({})

    render json: current_company.crm_settings.survey
  end

  def show_survey_settings
    render json: current_company.crm_settings.survey
  end

  def save_apps_api_settings
    current_company.settings.apps_api.chat_webhook.enabled = params.dig(:apps_api, :chat_webhook, :enabled)
    current_company.settings.apps_api.chat_webhook.webhook_url = params.dig(:apps_api, :chat_webhook, :webhook_url)

    current_company.save!

    render_apps_api_setting
  end

  def generate_webhook_secret
    secret = AppsApi::WebhookService.generate_secret!(key: params[:key])

    render json: { params[:key] => secret }
  end

  def rotate_webhook_secret
    AppsApi::WebhookService.rotate_secrets!
    render_apps_api_setting
  end

  #TODO Deprecate after 3.34
  def save_twilio_siprec_settings
    current_company.update_twilio_siprec(
      {
        current_company.twilio_settings.default_region => {
          :enabled => params.dig(:enabled) || false,
          :connector_name => params.dig(:connector_name),
          :connector_name_2 => params.dig(:connector_name2)
        }
      })
    current_company.save
    siprec = current_company.twilio_siprec
    render json: siprec
  end

  #TODO Deprecate after 3.34
  def show_twilio_siprec_settings
    siprec = current_company.twilio_siprec
    render json: siprec
  end

  def save_siprec_settings
    siprec_settings_service = SettingsService::SiprecSettingsService.new
    siprec_settings_service.update_twilio_siprec_settings(params[:twilio])
    siprec_settings_service.update_telnyx_siprec_settings(params[:telnyx])
    render json: siprec_settings_service.siprec_settings
  end

  def show_siprec_settings
    render json: SettingsService::SiprecSettingsService.new.siprec_settings
  end

  def show_ucaas_settings
    render json: current_company.ucaas_settings
  end

  def save_ucaas_settings
    ucaas_settings_params = params.require(:developer_setting).permit(
      :ucaas_type, :ucaas_app_id, :ucaas_app_secret, :multicast_limit
    )
    validate_ucaas_param(ucaas_settings_params)
    update_ucaas_settings(ucaas_settings_params)
  end

  def ucaas_token_unlink

    redis = Redis::Ucaas.current('ucaas')
    redis.del('ms_graph_access_token')
    current_company.ucaas_refresh_token = nil
    current_company.save!

    render plain: '', status: :ok

  rescue ServiceException => e
    render json: { success: false, message: e.message }, status: 400
  end

  def show_customer_logs_settings
    render json: {
      enabled: current_company.customer_logs_settings.enabled,
      observe_settings: {
        enabled: current_company.customer_logs_settings.observe_settings.enabled,
        configured: current_company.customer_logs_settings.observe_settings.configured
      }
    }
  end

  def save_customer_logs_settings
    enabling = params[:enabled]

    if enabling
      SettingsService::CustomerLogsSettingsService.enable_observe_settings_and_create_api_token
    else
      SettingsService::CustomerLogsSettingsService.disable_observe_settings_and_disable_api_token
    end

    show_customer_logs_settings
  rescue StandardError => e
    Rails.logger.warn(error: e) { "Error in save_customer_logs_settings : #{e.message}" }
    raise ServiceException, 'Failed to integrate with the logging platform. Please try again.'
  end

  private

  def end_user_object_type
    if current_company.crm_type == :salesforce
      if (current_company.crm_settings.use_custom_end_user_object || FeatureFlag.on?('sfdc-flexible-lookup')) && current_company.crm_settings.end_user_object.present?
        return current_company.crm_settings.end_user_object
      end
      return 'Contact'
    end
    if current_company.crm_type == :sap
      return current_company.crm_settings.end_user_object if current_company.crm_settings.end_user_object.present?
      return 'Account'
    end
    return 'User' if current_company.crm_type == :zendesk
    return 'customer' if current_company.crm_type == :kustomer
    return 'contact' if current_company.crm_type == :dynamics
    return 'sys_user' if current_company.crm_type == :servicenow
    return 'contact' if current_company.crm_type == :hubspot
  end

  def authorize_setting
    authorize :developer_setting
  end

  def render_setting
    render json: DeveloperSettingRepresenter.new(current_company).sanitized_hash
  end

  def render_apps_api_setting
    rendered_settings = current_company.settings.apps_api
    primary = rendered_settings.chat_webhook.primary_webhook_secret
    secondary = rendered_settings.chat_webhook.secondary_webhook_secret

    rendered_settings.chat_webhook.primary_webhook_secret = primary[0,5] if primary.present?
    rendered_settings.chat_webhook.secondary_webhook_secret = secondary[0,5] if secondary.present?

    render json: rendered_settings
  end

  def normalize_phone_number_for_dap(twilio_phone_number, phone_number_format)
    formatted = nil
    formatted = twilio_phone_number if phone_number_format == 'twilio'
    formatted = PhoneNumberHelper.format(twilio_phone_number, with: phone_number_format) if formatted.blank?
    formatted
  rescue StandardError
    raise ServiceException.new("Invalid phone number")
  end

  def convert_phone_number_to_twilio(input_phone_number)
    return PhoneNumberHelper.twilio(input_phone_number)
  rescue StandardError
    raise ServiceException.new("Invalid phone number")
  end

  def find_api_dap(response:)
    finder = DirectAccessKeyFinder.new(menu_type: :ivr_menu)
    finder.find_direct_access_key_with_api_response(response: response, support_phone_number: nil)
  end

  def oauth_token
    token = CRM::Credential.default_crm_credential
    return nil if token.blank?

    token.access_token
  rescue StandardError
    nil
  end

  # Return true if Admin Token is invalid, false otherwise
  def invalid_admin_token(crm_type, previous_settings)
    # Only check Zendesk for now but leave the option for other CRM's later
    case crm_type
    when 'zendesk'
      return invalid_admin_token_zendesk(previous_settings)
    when 'salesforce'
      return invalid_admin_token_salesforce
    when 'dynamics'
      return invalid_admin_token_dynamics(previous_settings)
    when 'oracle', 'freshdesk', 'hubspot', 'sap', 'servicenow'
      return invalid_default_admin_token(previous_settings, crm_type)
    end
    false
  end

  # Return true if Admin Token is invalid, false otherwise
  def invalid_admin_token_zendesk(previous_settings)
    # If inputs are blank then no need to validate admin token so set invalid_admin_token value to false
    return false if current_company.default_access_token.blank? && current_company.default_access_secret.blank?
    return true if current_company.default_access_token.blank? || current_company.default_access_secret.blank?

    return false if CRM::Client::Zendesk.validate_admin_token

    current_company.crm_settings.use_admin_user = false

    # Token is invalid, set tokens back to what they were previously
    current_company.default_access_token = previous_settings['default_access_token']
    current_company.default_access_secret = previous_settings['default_access_secret']
    update_current_company(previous_settings)
    true
  end

  # Return true if Admin Token is invalid, false otherwise
  def invalid_admin_token_salesforce
    return false unless salesforce_need_admin_token
    return false if CRM::AdminCredential.retrieve_admin_token

    turn_off_admin_user
    true
  end

  # Return true if Admin Token is invalid, false otherwise
  def invalid_admin_token_dynamics(previous_settings)
    return false unless current_company.crm_settings.use_admin_user
    return false if CRM::Client::Dynamics.validate_admin_token

    current_company.crm_settings.use_admin_user = false
    update_current_company(previous_settings)

    true
  end

  #THIS-2900: validate private app auth token and API key.
  def validate_hubspot_admin_token
    raise ServiceException, 'Please put your Hubspot Private App Access Token' if current_company.default_access_token.blank?
    begin
      CRM::CrmServer.request(url: '/validate_admin_token', method: :get, user_id: 'admin_token')
    rescue StandardError, RestClient::ExceptionWithResponse => e
      # crm-server returns 407 error if the token is invalid.
      # ServiceException will send bad_request status code in ErrorHandling.
      Rails.logger.warn(error: e) { "Error in validate_private_app_token : #{e.message}" }
      raise ServiceException, 'Provided Private App Access Token is invalid.'
    end
  end

  # Return true if Admin Token is invalid, false otherwise
  # admin token is mandatory for oracle & hubspot
  def invalid_default_admin_token(previous_settings, crm_type)
    #THIS-2995: Allow empty Private App Auth Token for now.  It will be remove shortly.
    return false if crm_type == 'servicenow' && current_company.crm_settings.auth_method == 'oauth'
    return false if crm_type == 'hubspot' && current_company.default_access_token.blank?
    return true if current_company.default_access_token.blank? || current_company.default_access_secret.blank?
    is_admin_token_valid = true
    begin
      CRM::CrmServer.request(url: '/validate_admin_token', method: :get, user_id: 'admin_token')
    rescue StandardError, RestClient::ExceptionWithResponse => e
      # crm-server returns 407 error if the token is invalid.
      # ServiceException will send bad_request status code in ErrorHandling.
      Rails.logger.warn(error: e) { "Error in checking invalid_default_admin_token : #{e.message}" }
      is_admin_token_valid = false
    end
    return false if is_admin_token_valid

    # use_admin_user is always true
    current_company.crm_settings.use_admin_user = true

    # Token is invalid, set tokens back to what they were previously
    if previous_settings.present?
      current_company.default_access_token = previous_settings['default_access_token']
      current_company.default_access_secret = previous_settings['default_access_secret']
      update_current_company(previous_settings)
    end

    true
  end

  # Return true if the setting requires an admin token
  def salesforce_need_admin_token
    return true if current_company.crm_settings.salesforce_metadata_mapping == 'UJET Session'
    return true if current_company.crm_settings.use_admin_user
    return true if current_company.crm_settings.use_admin_user_for_all_interactions
    false
  end

  def handle_invalid_admin_token(crm_type)
    case crm_type
    when 'zendesk'
      raise ServiceException, 'Zendesk user email address and API Token combination is invalid'
    when 'salesforce'
      raise ServiceException, 'No Salesforce Admin Token is linked'
    when 'oracle'
      raise ServiceException, 'Oracle API username and password is invalid'
    when 'freshdesk'
      raise ServiceException, 'Freshdesk API Key is invalid'
    when 'hubspot'
      raise ServiceException, 'HubSpot Private Access Token is invalid'
    when 'sap'
      raise ServiceException, 'SAP Authentication Credentials are invalid'
    when 'dynamics'
      raise ServiceException, 'MS Dynamics Credentials are invalid'
    when 'servicenow'
      raise ServiceException, 'Servicenow Credentials are invalid'
    end
  end

  def update_current_company(settings)
    current_company.save
    current_company.update_crm_settings
    current_company.update!(settings)
    current_company.update_default_token # DNULL-628: fix issue where admin tokens aren't getting added to Redis in some situations.
  end

  # @return [Array<Hash>]
  def populate_lookup_settings(developer_settings)
    # lookup settings must be array of hashes not ActionController::Parameters
    lookup_settings = developer_settings[:crm_lookup_settings]
    if FeatureFlag.on?('salescloud-flex-mapping') && lookup_settings.is_a?(Array)
      return lookup_settings.map(&:to_unsafe_h)
    end

    lookup_settings = {
      'object_type' => current_company.crm_settings.end_user_object,
      'record_type' => current_company.crm_settings.end_user_record_type,
      'record_types' => current_company.crm_settings.end_user_record_types,
      'phone_field' => current_company.crm_settings.end_user_phone_type,
      'phone_fields' => current_company.crm_settings.end_user_phone_types,
      'phone_number_primary_field' => current_company.crm_settings.end_user_primary_phone_type,
      'sdk_identifier_lookup_field' => current_company.crm_settings.matching_crm_field,
      'primary_display_field' => 'Name',
      'amb_custom_field_id' => current_company.crm_settings.amb_custom_field_id,
      'use_amb_custom_field_matching' => current_company.crm_settings.use_amb_custom_field_matching
    }

    obj = CrmLookup.first
    lookup_settings['id'] = obj.id if obj.present?
    [lookup_settings]
  end

  def update_crm_lookup_settings(settings)
    Company.current.update_crm_lookup_settings(settings)
  end

  def turn_off_admin_user
    current_company.crm_settings.use_admin_user = false
    current_company.crm_settings.use_admin_user_for_all_interactions = false
    current_company.crm_settings.salesforce_metadata_mapping = 'Activity Object'
    current_company.crm_settings.end_user_direct_account_lookup = false
    current_company.save
    current_company.update_crm_settings
  end

  def admin_token_statuses
    statuses = {}
    admin_credentials = CRM::AdminCredential.all
    if CRM::AdminCredential.all.blank?
      turn_off_admin_user
      return statuses
    end
    admin_credentials.each do |admin_token|
      statuses[admin_token.key] = {
        email: admin_token.try(:user_info)&.dig(:email),
        status: admin_token.status
      }
    end
    statuses
  end

  def salesforce_custom_objects_default_results
    {
      enable_banner: false,
      enable_metadata_mapping_option: false,
      current_package: nil,
      custom_object_package: nil
    }
  end

  def redis
    Redis::Tenant.current
  end

  def validate_ucaas_param(ucaas_setting)
    unless Company.ucaas.include?(ucaas_setting['ucaas_type'].to_s)
      raise ServiceException, 'ucaas_type value is not supported'
    end

    return if ucaas_setting['ucaas_type'].to_sym == :ujet

    if ucaas_setting['ucaas_type'].blank? ||
       ucaas_setting['ucaas_app_id'].blank? ||
       ucaas_setting['ucaas_app_secret'].blank? ||
       ucaas_setting['multicast_limit'].blank?
      raise ServiceException, 'param is missing or the value is empty'
    end
  end

  def update_ucaas_settings(ucaas_setting)
    # the following condition is considered a reset..

    unless current_company.ucaas_settings.ucaas_type.nil?
      ucaas_type_param = ucaas_setting['ucaas_type']
      if current_company.ucaas_settings.ucaas_type != 'ujet' && ucaas_type_param == 'ujet'
        reset_ucaas
        return render json: {
          ucaas_type: ucaas_setting['ucaas_type']
        }
      end
    end

    current_company.ucaas_settings.ucaas_type = ucaas_setting['ucaas_type']
    current_company.ucaas_settings.ucaas_app_id = ucaas_setting['ucaas_app_id']
    current_company.ucaas_settings.ucaas_app_secret = ucaas_setting['ucaas_app_secret']
    current_company.ucaas_settings.multicast_limit = validate_multicast_limit(ucaas_setting['multicast_limit'].to_i)
    current_company.save
    render json: ucaas_setting
  end

  def validate_multicast_limit(requested_value)
    if requested_value.positive? && requested_value <= Ucaas::Constants::UCAAS_MULTICAST_LIMIT
      requested_value
    else
      raise ServiceException, "#{requested_value} is not within the allowed range for multicast Limit"
    end
  end

  def reset_ucaas
    ucaas_type = current_company.ucaas_settings.ucaas_type.to_sym
    case ucaas_type
    when :msteams
      Ucaas::MsTeamsService.new.reset_ms_ucaas_settings
    else
      Rails.logger.debug("no uccas reset logic implmentation for #{ucaas_type}")
    end
  end
end

# frozen_string_literal: true

class V1::Settings::AgentQueueSettingsController < ApplicationController
  before_action :authorize_agent_queue_settings

  def show
    render_agent_queue_setting
  end

  def update
    SettingsService::AgentQueueSettingsService.new.update_agent_queue_setting(permitted_params)

    render_agent_queue_setting
  end

  private

  def render_agent_queue_setting
    render json: AgentQueueSettingsRepresenter.new(current_company.agent_queue_settings)
  end

  def authorize_agent_queue_settings
    authorize [:settings, :agent_queue_settings]
  end

  def permitted_params
    params.permit(Settings::AgentQueueSettingsPolicy.permitted_attributes).to_h
  end
end

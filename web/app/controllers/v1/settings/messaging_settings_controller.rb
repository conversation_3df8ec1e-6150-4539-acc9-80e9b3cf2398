class V1::Settings::MessagingSettingsController < V1::Settings::BaseController
  wrap_parameters :messaging_setting, include: Settings::MessagingSetting.attribute_names

  def show
    render_setting
  end

  def update
    updates = params.require(:messaging_setting).permit(self.class.permit_filters)
    messaging_settings = current_company.messaging_settings
    deactivated = deactivated_channels(updates, messaging_settings)
    messaging_settings.update(updates)
    current_company.save!

    send_deactivation_notifications(deactivated)
    render_setting
  end

  # POST v1/settings/messaging/activate/:channel
  def activate
    channel = params[:channel].presence&.to_sym
    raise ArgumentError, "Unknown channel #{channel}" unless Messaging::Channel.supported?(channel.to_sym)
    if channel == :amb
      business_id = params[:business_id].presence
      raise ArgumentError, "Unknown business_id" unless business_id
    end

    Messaging::Channel.activate(channel, business_id)
    head :ok
  end

  # POST v1/settings/messaging/deactivate/:channel
  def deactivate
    channel = params[:channel].presence&.to_sym
    raise ArgumentError, "Unknown channel #{channel}" unless Messaging::Channel.supported?(channel.to_sym)
    if channel == :amb
      business_id = params[:business_id].presence
      raise ArgumentError, "Unknown business_id" unless business_id
    end

    Messaging::Channel.deactivate(channel, business_id)
    head :ok
  end

  private

  def render_setting
    messaging_setting = current_company.messaging_settings
    render json: messaging_setting.attributes
  end

  def authorize_setting
    authorize %i[settings messaging_setting]
  end

  def self.permit_filters
    @permit_filters ||= setting_object_permit_filters(Settings::MessagingSetting)
  end

  def deactivated_channels(updates, messaging_settings)
    deactivated = []
    channels = Messaging::Channel.supported
    channels.each do |c|
      next unless updates.include?(c)
      next unless messaging_settings.enabled?(c)
      next if updates.dig(c, :enabled)

      deactivated << c.to_sym
    end
    
    deactivated
  end

  def send_deactivation_notifications(channels)
    channels.each do |c|
      SystemMailer.channel_deactivate_notification(c.capitalize.to_s, current_user).deliver_later
    end
  end
end

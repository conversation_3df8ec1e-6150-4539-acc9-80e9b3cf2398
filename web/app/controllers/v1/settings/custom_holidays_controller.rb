# frozen_string_literal: true

module V1
  module Settings
    class CustomHolidaysController < V1::Settings::BaseController
      wrap_parameters :holiday_setting, include: [
        :parent_id, :name, :dates_condition, :time_condition, :messaging
      ]

      before_action :require_holiday, only: [:index, :show, :update, :destroy]
      before_action :require_messages, only: [:index, :show]
      before_action :validate_create, only: [:create]
      before_action :validate_update, only: [:update]
      before_action :authorize_setting

      def create
        ActiveRecord::Base.transaction do
          @custom_holiday = CustomHolidays::CustomHoliday.create!(create_params.except(:messaging))
          update_messages(@create_params[:messaging])
        end

        render_holiday
      end

      def index
        render_holiday
      end

      def tree
        @custom_holidays = CustomHolidays::CustomHoliday.hash_tree.map do |model, child_hash_tree|
          TreeNode.new(nil, model, child_hash_tree)
        end
        render json: CustomHolidays::HolidayTreeRepresenter.for_collection.prepare(@custom_holidays)
      end

      def show
        render_holiday
      end

      def update
        calculate_position

        ActiveRecord::Base.transaction do
          @custom_holiday.update!(update_params.except(:messaging))
          update_messages(update_params[:messaging])
        end

        render_holiday
      end

      def destroy
        ActiveRecord::Base.transaction do
          @custom_holiday.destroy!
          message_service = Audible::Holiday::MessageService.new(holiday_id: @custom_holiday.id)
          message_service.destroy
        end

        render_holiday
      end

      private

      def validate_create
        validate_parent(create_params[:parent_id])
        return if create_params[:directory]

        raise ServiceException, 'Missing dates condition' unless create_params.key?(:dates_condition)
        raise ServiceException, 'Missing time condition' unless create_params.key?(:time_condition)

        Audible::Holiday::MessageHelper.validate_messages(@create_params[:messaging].to_h)
      end

      def validate_update
        return unless params.key?(:parent_id)

        validate_parent(update_params[:parent_id])
        Audible::Holiday::MessageHelper.validate_messages(update_params[:messaging].to_h)
      end

      def validate_parent(parent_id)
        raise ServiceException, "Can't create root directory" if parent_id.blank?

        parent = CustomHolidays::CustomHoliday.find_by(id: parent_id)
        raise ServiceException, 'Invalid parent directory' if parent.blank?
        raise ServiceException, 'Parent is not a directory' unless parent.directory
      end

      def require_holiday
        @custom_holiday = params[:id] ? CustomHolidays::CustomHoliday.find(params[:id]) : CustomHolidays::CustomHoliday.root
      end

      def require_messages
        message_service = Audible::Holiday::MessageService.new(holiday_id: @custom_holiday.id)
        holiday_ids = Array(@custom_holiday.id).concat(@custom_holiday.children.pluck(:id))
        @messages = message_service.find_by_holiday_ids(holiday_ids)
      end

      def authorize_setting
        authorize @custom_holiday || CustomHolidays::CustomHoliday
      end

      def render_holiday
        render json: CustomHolidays::SingleHolidayRepresenter.new(@custom_holiday).to_hash(
          user_options: { messaging: @messages }
        )
      end

      def shared_params
        @shared_params ||= params.permit(
          :parent_id,
          :name,
          :position,
          :directory,
          dates_condition: [
            :type,
            {
              days_of_week: [],
              occurrence: [],
              months_of_year: [],
              from: [
                :month,
                :day
              ],
              to: [
                :month,
                :day
              ]
            }
          ],
          time_condition: [
            :all_day,
            {
              from: [
                :hour,
                :minute
              ],
              to: [
                :hour,
                :minute
              ]
            }
          ],
          messaging: [
            :key, :lang, :type, :message_type, :text, :audio, :audio_url, :remove_audio, :remove_message, :changed_language
          ]
        )
      end

      def create_params
        @create_params ||= shared_params
        @create_params[:position] ||= CustomHolidays::CustomHoliday.where(parent_id: @create_params[:parent_id]).count
        @create_params
      end

      def update_params
        @update_params ||= shared_params.except(:directory)

        # do not update parent if the param is not an int
        unless @update_params[:parent_id].is_a?(Integer) || @update_params[:parent_id].to_i.to_s == @update_params[:parent_id]
          @update_params[:parent_id] = @custom_holiday.parent_id
        end

        @update_params
      end

      def update_messages(messages)
        return @messages = [] unless messages

        message_service = Audible::Holiday::MessageService.new(holiday_id: @custom_holiday.id)
        message_service.update(messages)

        @messages = message_service.find_by_holiday_ids(@custom_holiday.id)
      end

      # calculate the new position if it's not directly given in the params
      def calculate_position
        # calculate if update_params[:position] is not an int
        unless update_params[:position].is_a?(Integer) || update_params[:position].to_i.to_s == update_params[:position]
          @update_params[:position] = CustomHolidays::CustomHoliday.where(parent_id: update_params[:parent_id]).count
        end
      end
    end
  end
end

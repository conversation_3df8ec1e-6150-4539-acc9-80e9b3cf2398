class V1::Settings::Base<PERSON><PERSON>roller < ApplicationController
  before_action :authorize_setting

  private

  def authorize_setting
    authorize :setting
  end

  ##
  # @param setting_object_class [SettingObject] the SettingOjbect whose attributes are permitted
  # @param except [Array] the attributes of the SettingObject won't permit
  #
  def self.setting_object_permit_filters(setting_object_class, except: [])
    setting_object_class.attribute_types.map do |name, type|
      next nil if except.include?(name)
      case
      when type.is_a?(Symbol)
        name
      when type <= SettingObject
        {name => setting_object_permit_filters(type, except: except)}
      else
        raise "attribute type not supported: #{type}"
      end
    end.compact
  end
  private_class_method :setting_object_permit_filters
end

class V1::Settings::CallSettingsController < V1::Settings::BaseController
  wrap_parameters :call_setting, include: [
    :use_recording, :sales_phone_number,
    :enabled, :sales_enabled, :allow_call_without_agent, :primary_voip_provider,
    :announcement_interval, :announcement_interval_seconds, :auto_wrap_up_enabled, :wrap_up_time_after_call,
    :schedule_standby_duration, :schedule_expiration_duration, :schedule_same_day_only,
    :unanswered_call_expiration_duration, :unanswered_transfer_expiration_duration,
    :abandoned_call_ticket, :abandoned_call_ticket_with_wait_time_sms,
    :global_whisper_enabled, :whisper_enabled, :countdown_enabled, :use_queue_aging,
    :queue_aging_interval, :deflection, :estimated_wait_time, :notification, :whisper_speech_rate,
    :call_history, :hold_time_counter, :outbound_call_recording,
    :byoc_enabled, :byoc_sid, :byoc_origination_uri, :byoc_default_country_code, :emergency_dialing,
    :play_outbound_call_user_permission_message_first, :sms_notification_enabled, :post_session_transfer, :contact_list,
    :concurrency, :continue_call_on_user_hangup_enabled, :continue_recording_on_user_hangup_enabled, :skip_connecting_message_playback
  ]

  def show
    render_setting
  end

  def update
    # company attributes
    setting_params = params.require(:call_setting).permit(policy(Settings::CallSetting).permitted_attributes)

    channel_disabled = setting_params.include?(:enabled) && !setting_params[:enabled]

    # TODO: global_whisper_enabled will be replaced with whisper_enabled later
    if setting_params.include?(:whisper_enabled)
      setting_params[:global_whisper_enabled] = setting_params[:whisper_enabled]
    elsif setting_params.include?(:global_whisper_enabled)
      setting_params[:whisper_enabled] = setting_params[:global_whisper_enabled]
    end

    # TODO: remove block when announcement_interval, repeat props are removed
    # backward compatibility - start
    if setting_params[:announcement_interval].present?
      setting_params[:announcement_interval_seconds] = setting_params[:announcement_interval] * 60
    elsif setting_params[:announcement_interval_seconds].present?
      announcement_interval = setting_params[:announcement_interval_seconds] / 60
      setting_params[:announcement_interval] = announcement_interval.positive? ? announcement_interval : 1
    end

    if setting_params.dig(:estimated_wait_time, :repeat).present?
      if setting_params.dig(:estimated_wait_time, :repeat) == 'once'
        setting_params[:estimated_wait_time][:repeat_type] = 'limit'
        setting_params[:estimated_wait_time][:limit_count] = 1
      else
        setting_params[:estimated_wait_time][:repeat_type] = 'interval'
      end
    end
    # backward compatibility - end

    safe_company_update do |company|
      SettingsService::CallSettingsUpdater.new(company).update(setting_params)
    end

    current_company.update_crm_settings
    SystemMailer.channel_deactivate_notification('Call', current_user).deliver_later if channel_disabled

    # if original_auto_answer_enabled != call_setting.auto_answer_enabled
    #   apply_auto_answered(call_setting.auto_answer_enabled)
    # end

    render_setting
  end

  private

  def authorize_setting
    authorize current_company.call_settings
  end

  def render_setting
    call_setting = current_company.call_settings
    render json: Settings::CallSettingRepresenter.for_user(current_user).new(call_setting)
  end

  ##
  # [UJET-20946] We don't want to apply this change for calls since there are known issues. Needs to revisit later.
  # When there are auto-answered calls and non-auto-answered calls both at the same time, the client pass the param
  # `auto_answered` so that the server tries to pick a call among only auto-answered calls. It leads the issue where
  # a non-auto-answered call won't be picked until all auto-answered calls are picked up.
  #
  # @see CallService:QueueService.peek_communication
  # @see Menu::SettingUpdater#apply_auto_answered
  # def apply_auto_answered(activate)
  #   AutoAnswer::ActivationService.process_calls
  # end
end

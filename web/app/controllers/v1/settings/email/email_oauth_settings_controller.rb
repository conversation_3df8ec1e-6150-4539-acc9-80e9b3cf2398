# frozen_string_literal: true

module V1
  module Settings
    module Email
      class EmailOauthSettingsController < V1::Settings::BaseController
        before_action :validate_params, only: [:update]
        before_action :authorize_setting
        UNAVAILABLE_ERROR_MESSAGE = { message: 'Email service is not available now. The email storage settings are not verified. Please try again later.' }

        def show
          render_email_oauth
        end

        def update
          safe_company_update(perform_save: false) do |company|
            SettingsService::EmailOauthSettingsUpdater.new(company, permitted_params, current_user).call
          end

          begin
            SettingsService::EmailOauthSettingsService.new.update_oauth_settings(permitted_params)
          rescue Errno::ECONNREFUSED
            return render json: UNAVAILABLE_ERROR_MESSAGE, status: :service_unavailable
          end

          render_email_oauth
        end

        private

        def permitted_params
          @permitted_params ||= params.permit(
            email_oauth_settings: [
              :oauth_client_id,
              :oauth_client_secret,
              :oauth_authorization_url,
              :oauth_token_url,
              :oauth_scope,
              :oauth_audience,
              :oauth_state,
              :oauth_access_type,
              :oauth_grant_type,
              :oauth_include_grant_type,
              :oauth_use_redirect_url
            ]
          )
        end

        def authorize_setting
          authorize current_company, policy_class: ::Settings::Email::EmailOauthSettingsPolicy
        end

        def validate_params
          raise ServiceException, 'Client ID is required.' unless permitted_params[:email_oauth_settings][:oauth_client_id].present?
          raise ServiceException, 'Client Secret is required.' unless permitted_params[:email_oauth_settings][:oauth_client_secret].present?
          raise ServiceException, 'Authorization URL is required.' unless permitted_params[:email_oauth_settings][:oauth_authorization_url].present?
          raise ServiceException, 'Token URL is required.' unless permitted_params[:email_oauth_settings][:oauth_token_url].present?
        end

        def render_email_oauth
          email_setting = current_company.email_settings
          render json: ::Settings::Email::EmailOauthSettingsRepresenter.for_user(current_user).new(email_setting)
        end
      end
    end
  end
end

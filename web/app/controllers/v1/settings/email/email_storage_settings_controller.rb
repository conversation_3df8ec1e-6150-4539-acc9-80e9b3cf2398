# frozen_string_literal: true

module V1
  module Settings
    module Email
      class EmailStorageSettingsController < V1::Settings::BaseController
        before_action :validate_params, only: [:update, :verify_connection]
        before_action :validate_folder_path, only: [:update]
        before_action :authorize_setting
        FOLDER_PATH_REGEX = %r{\A[0-9a-zA-Z!_.'()/-]+\z}
        UNAVAILABLE_ERROR_MESSAGE = { message: 'Email service is not available now. The email storage settings are not verified. Please try again later.' }.freeze
        UNAVAILABLE_STATUS_CODE = 503

        def show
          render_email_storage
        end

        def update
          encrypt_credentials
          permitted_params[:email_storages][provider][:folder_path] = sanitized_path
          safe_company_update(perform_save: false) do |company|
            SettingsService::EmailStorageSettingsUpdater.new(company, permitted_params, current_user).call
          end

          if @credentials
            permitted_params[:email_storages][provider][:credentials] = @credentials
          else
            email_settings = Company.current.email_settings
            credentials = email_settings.email_storages.send(provider).credentials
            
            unless credentials.empty?
              permitted_params[:email_storages][provider][:credentials] = Ujet.encryption_service.decrypt(credentials).to_json
            end
          end

          begin
            SettingsService::EmailStorageSettingsService.new.update_storage_settings(permitted_params)
          rescue Errno::ECONNREFUSED
            return render json: UNAVAILABLE_ERROR_MESSAGE, status: :service_unavailable
          rescue StandardError => e
            if e.respond_to?(:http_code) && e.http_code == UNAVAILABLE_STATUS_CODE
              return render json: UNAVAILABLE_ERROR_MESSAGE,
                            status: :service_unavailable
            end
            return render json: { message: 'Email service update failed.' }, status: :internal_server_error
          end

          render_email_storage
        end

        def verify_connection
          if @credentials
            permitted_params[:email_storages][provider][:credentials] = @credentials.to_json
          else
            email_settings = Company.current.email_settings
            return head :bad_request unless email_settings.email_storages

            credentials = email_settings.email_storages.send(provider).credentials
            unless credentials.empty?
              permitted_params[:email_storages][provider][:credentials] = Ujet.encryption_service.decrypt(credentials).to_json
            end
          end

          begin
            SettingsService::EmailStorageSettingsService.new.test_storage_settings(permitted_params, false)
          rescue Errno::ECONNREFUSED
            return render json: UNAVAILABLE_ERROR_MESSAGE, status: :service_unavailable
          rescue StandardError => e
            if e.respond_to?(:http_code) && e.http_code == UNAVAILABLE_STATUS_CODE
              return render json: UNAVAILABLE_ERROR_MESSAGE,
                            status: :service_unavailable
            end
            raise ServiceException, 'Test connection failed. Please try again later.'
          end

          head :ok
        end

        private

        def permitted_params
          @permitted_params ||= params.require(:settings).permit(
            :storage_provider,
            email_storages: [
              gcs: [:credentials, :bucket_name, :folder_path, :key_name, :store_duration],
              s3: [:credentials, :bucket_name, :folder_path, :key_name, :store_duration]
            ]
          )
        end

        def authorize_setting
          authorize current_company, policy_class: ::Settings::Email::EmailStorageSettingsPolicy
        end

        def validate_params
          unless valid_bucket_name
            render json: { error: 'Bucket name is required.' }, status: :unprocessable_entity
          end

          @credentials = permitted_params[:email_storages][provider][:credentials]
          if @credentials
            @credentials.rewind
            key_name = @credentials.original_filename
            permitted_params[:email_storages][provider][:key_name] = key_name
            raise ServiceException, 'The uploaded key is not a valid JSON file.' unless is_json_file?(@credentials)

            begin
              @credentials = JSON.parse(@credentials.read)
            rescue JSON::ParserError
              raise ServiceException, 'The uploaded key is not a valid JSON file.'
            end
          end
        end

        def provider = permitted_params[:storage_provider]

        def encrypt_credentials
          if @credentials
            permitted_params[:email_storages][provider][:credentials] = Ujet.encryption_service.encrypt(@credentials)
          end
        end

        def render_email_storage
          email_setting = current_company.email_settings
          render json: ::Settings::Email::EmailStorageSettingsRepresenter.for_user(current_user).new(email_setting)
        end

        def validate_folder_path
          return if valid_folder_path?

          render json: { error: 'Folder Path is invalid' }, status: :unprocessable_entity
        end

        def sanitized_path
          folder_path = permitted_params[:email_storages][provider][:folder_path]
          return '' if folder_path.nil? || folder_path.empty?

          folder_path.gsub(%r{/+}, '/').gsub(%r{^/|/$}, '')
        end

        def valid_folder_path?
          return true if sanitized_path.empty?

          sanitized_path.match(FOLDER_PATH_REGEX)
        end

        def valid_bucket_name
          bucket_name = permitted_params[:email_storages][provider][:bucket_name]
          !bucket_name.empty? && bucket_name.length <= 250
        end

        def is_json_file?(file)
          File.extname(file.original_filename).downcase == ".json"
        end
      end
    end
  end
end

class V1::Settings::Nps::SurveySettingsController < V1::Settings::BaseController
  before_action :find_survey, only: [:show, :update, :destroy]
  wrap_parameters :survey, include: [:lang, :sign_off]

  def index
    lang = params[:lang]
    if lang.blank?
      @surveys = NpsSurvey.all
    else
      @surveys = NpsSurvey.where(lang: lang)
    end

    render json: ::Nps::SurveyRepresenter.for_collection.prepare(@surveys)
  end

  def show
    render_survey
  end

  def create
    @survey = NpsSurvey.new(permitted_params)
    if @survey.save
      render_survey
    else
      render json: {errors: @survey.errors}, status: :bad_request
    end

  end

  def update
    if @survey.update(permitted_params)
      render_survey
    else
      render json: {errors: @survey.errors}, status: :bad_request
    end
  end

  def destroy
    if @survey.destroy
      head :ok
    else
      render json: {errors: @survey.errors}, status: :bad_request
    end
  end

  private

  def authorize_setting
    authorize %i[settings survey_settings]
  end

  def find_survey
    @survey = NpsSurvey.find(params[:id])
  end

  def permitted_params
    params.require(:survey).permit(:lang, sign_off: [:format, :text, :audio, :display_text])
  end

  def render_survey
    render json: ::Nps::SurveyRepresenter.new(@survey)
  end
end
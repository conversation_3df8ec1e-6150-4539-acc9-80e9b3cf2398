class V1::Settings::Nps::QuestionSettingsController < V1::Settings::BaseController
  wrap_parameters :questions

  def create
    build_questions
    if @questions.nil?
      render json: {errors: "Bad questions format"}, status: :bad_request
      return
    end

    exception = transact_on_questions(method: 'save!')
    if exception
      render json: {errors: exception.message}, status: :bad_request
      return
    end

    render_all_surveys
  end

  def update
    ::Nps::Survey::Question::Base.transaction do
      begin
        params.fetch(:questions, {}).each do |index, attributes|
          permitted_attributes = permitted_attributes(attributes)
          question = ::Nps::Survey::Question::Base.find_by(nps_survey_id: permitted_attributes["nps_survey_id"],
                                               id: permitted_attributes["id"])
          permitted_attributes["type"] = ::Nps::Survey::Question::Base.class_by_type(permitted_attributes["type"])
          question.update!(permitted_attributes) if question
        end
      rescue ActiveRecord::StatementInvalid, ActiveRecord::RecordInvalid => exception
        render json: {errors: exception.message}, status: :bad_request
        return
      end
    end

    render_all_surveys
  end

  def destroy
    find_questions
    if @questions.nil?
      render json: {errors: "Bad questions format"}, status: :bad_request
      return
    end

    if @questions.empty?
      render json: {errors: "Questions not found"}, status: :not_found
      return
    end

    exception = transact_on_questions(method: 'destroy')
    if exception
      render json: {errors: exception.message}, status: :bad_request
      return
    end

    render_all_surveys
  end

  private

  def authorize_setting
    authorize %i[settings survey_settings]
  end

  def build_questions
    @questions = []
    params.fetch(:questions, {}).each do |index, attributes|
      permitted_attributes = permitted_attributes(attributes.except(:id))
      @questions.push(::Nps::Survey::Question::Base.build(permitted_attributes))
    end
    @questions
  end

  def find_questions
    @questions = []
    params.fetch(:questions, {}).each do |index, attributes|
      permitted_attributes = permitted_attributes(attributes)
      question = ::Nps::Survey::Question::Base.find_by(nps_survey_id: permitted_attributes["nps_survey_id"],
                                           id: permitted_attributes["id"])
      @questions.push(question) if question
    end
    @questions
  end

  def transact_on_questions(method:)
    Nps::Survey::Question::Base.transaction do
      begin
        @questions.each do |q|
          q.public_send(method)
        end
      rescue ActiveRecord::StatementInvalid, ActiveRecord::RecordInvalid => exception
        return exception
      end
    end
    nil
  end

  def permitted_attributes(attributes)
    permitted_attributes = attributes.permit(:id, :nps_survey_id, :name, :type, {valid_answers: [:key, :value]}, :crm_mapping_id, :is_csat,
                      :position, :format, :text, :audio, :display_text, :crm_field_api)

    crm_field_api = permitted_attributes.delete(:crm_field_api)
    permitted_attributes["crm_mapping_keys"] = ::CRM::CrmMappingHelper.simple_mapping_json(crm_field_api: crm_field_api)
    permitted_attributes
  end

  def all_surveys
    build_questions if @questions.nil?

    surveys = Set.new()
    @questions.each do |q|
      surveys.add(q.nps_survey)
    end
    surveys
  end

  def render_all_surveys
    render json: ::Nps::SurveyRepresenter.for_collection.prepare(all_surveys)
  end

end
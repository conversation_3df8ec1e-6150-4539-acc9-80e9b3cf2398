class V1::Settings::GeneralSettingsController < V1::Settings::BaseController
  wrap_parameters :general_setting, include: [:name, :display_name, :support_email]

  def show
    render_setting
  end

  def update
    settings_params = params.require(:general_setting).permit(permitted_param_names)
    current_company.update!(settings_params)
    render_setting
  end

  private

  def authorize_setting
    authorize [:settings, :operation, :operation_management]
  end

  def render_setting
    render json: current_company, only: permitted_param_names
  end

  def permitted_param_names
    [:name, :display_name, :support_email]
  end
end

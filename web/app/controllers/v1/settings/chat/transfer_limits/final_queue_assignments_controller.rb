# frozen_string_literal: true

module V1
  module Settings
    module Chat
      module TransferLimits
        class FinalQueueAssignmentsController < ApplicationController
          before_action :validate_params, only: :create
          before_action :authorize_final_queue_assignments

          # GET v1/settings/chat/transfer_limits/final_queue_assignments
          def index
            render_queues
          end

          # POST v1/settings/chat/transfer_limits/final_queue_assignments
          def create
            service = ::Settings::TransferLimits::FinalQueueAssignmentService.new(
              comm_type: 'Chat',
              assignments_param: permitted_params
            )
            service.create_or_update_final_queue_assignments

            render_queues
          end

          private

          def policy_class = ::Settings::Chat::TransferLimitFinalQueueAssignmentsPolicy

          def authorize_final_queue_assignments
            authorize(:transfer_limit_final_queue_assignments, policy_class:)
          end

          def render_queues
            assignments = CommTransfer::QueueAssignment.chat.with_menu

            render json: ::Settings::Chat::TransferLimits::FinalQueueAssignmentRepresenter.for_collection
                                                                                        .new(assignments)
          end

          def permitted_params
            params.require(:assignments).map do |menu|
              menu.permit(:lang, :menu_id, :menu_type, :id)
            end
          end

          def validate_params
            if permitted_params.any? { _1.values.any?(&:blank?) }
              raise InvalidParameterException, 'Invalid value type of final queue assignment parameters'
            end
          end
        end
      end
    end
  end
end

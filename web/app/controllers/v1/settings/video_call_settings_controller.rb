class V1::Settings::VideoCallSettingsController < V1::Settings::BaseController
  wrap_parameters :video_call_setting, include: [
    :enabled,
    :hide_in_off_hours,
    :hiding_threshold,
  ]

  def show
    render_setting
  end

  def update
    setting_params = params.require(:video_call_setting).permit(permitted_param_names)
    video_call_setting = current_company.video_call_settings
    video_call_setting.update(setting_params)
    current_company.save!
    render_setting
  end

  private

  def render_setting
    video_call_setting = current_company.video_call_settings
    render json: video_call_setting.attributes, only: [:enabled, :hide_in_off_hours, :hiding_threshold]
  end

  def permitted_param_names
    [
      :enabled,
      :hide_in_off_hours,
      :hiding_threshold,
    ]
  end
end

class V1::Settings::SessionDataExportSettingsController < V1::Settings::BaseController
  wrap_parameters :settings, include: [:nice, :alvaria, :qm_recordings]

  PERMIT_FILTERS = {
    nice: setting_object_permit_filters(Settings::NiceSetting),
    alvaria: setting_object_permit_filters(Settings::AlvariaSetting, except: ['key_name']),
    raw_data_export: setting_object_permit_filters(Settings::DataExportSetting, except: [:include_phone_numbers]),
    qm_recordings: [:enabled, verint_qm_email_domains: []],
    external_session_event: setting_object_permit_filters(Settings::ExternalSessionEventSetting),
  }.freeze

  # GET /v1/settings/session_data_export
  def show
    render_setting
  end

  # PUT|PATCH /v1/settings/session_data_export
  def update
    permitted_params = params.require(:settings).permit(PERMIT_FILTERS)

    updater = settings_service
    updater.update_nice_settings!(permitted_params[:nice].to_h)
    updater.update_alvaria_settings!(permitted_params[:alvaria].to_h)
    updater.update_data_export_settings!(permitted_params[:raw_data_export].to_h)
    updater.update_qm_recordings!(permitted_params[:qm_recordings].to_h)
    updater.update_external_session_event_settings!(permitted_params[:external_session_event].to_h)

    render_setting
  end

  private

  def settings_service
    @service ||= TenantSettings::Service.new(company: current_company)
  end

  def render_setting
    settings = {
      nice: settings_service.nice_settings,
      alvaria: settings_service.alvaria_settings,
      raw_data_export: settings_service.data_export_settings,
      external_session_event: settings_service.external_session_event_settings,
      qm_recordings: {
                        domains: current_company.settings.qm_recordings.verint_qm_email_domains,
                        enabled: current_company.settings.qm_recordings.enabled,
                      }
    }

    render json: settings
  end

  def authorize_setting
    authorize [:settings, :session_data_export_setting]
  end
end

# frozen_string_literal: true

module V1
  module Settings
    module Operation
      class VirtualAgentSettingsController < V1::Settings::BaseController
        SHARED_PARAMS = [
          :create_ticket_option,
          :create_ticket_option_threshold,
          :close_ticket_by_virtual_agent,
          :session_variable_enabled,
          :task_va_session_variable_enabled,
          :assign_ticket_option,
          :assign_to_crm_user_id,
          :assign_to_crm_user_name
        ].freeze

        TRANSCRIPT_PARAMS = [
          transcript: [
            :enabled,
            :task_enabled
          ]
        ].freeze

        VA_SESSION_PARAMS = [
          va_session: [
            :show_and_send_enabled
          ]
        ].freeze

        TASK_VA_SESSION_PARAMS = [
          task_va_session: [
            :show_and_send_enabled
          ]
        ].freeze

        RECORDING_PARAMS = [
          recording: [
            :enabled,
            :task_enabled
          ]
        ].freeze

        VERSION_OF_TASK_VA = "3.31.0"

        # GET /v1/settings/operation/virtual_agent
        def show
          render_setting
        end

        # PATCH|PUT /v1/settings/operation/virtual_agent
        # updates Settings::CrmSetting::VirtualAgentSetting
        def update
          ujet_version_of_request = request.headers['Ujet-Version']
          if before_task_va_session?(ujet_version_of_request)
            chatbot = params[:chat]
            chatbot[:task_va_session] = chatbot[:va_session]
            chatbot[:task_va_session_variable_enabled] = chatbot[:session_variable_enabled]
            params[:chat] = chatbot
            voicebot = params[:voice_call]
            voicebot[:task_va_session] = voicebot[:va_session]
            voicebot[:task_va_session_variable_enabled] = voicebot[:session_variable_enabled]
            params[:voice_call] = voicebot
          end

          current_company.crm_settings.update(
            chatbot: params.require(:chat).permit(SHARED_PARAMS | TRANSCRIPT_PARAMS | VA_SESSION_PARAMS | TASK_VA_SESSION_PARAMS),
            voicebot: params.require(:voice_call).permit(SHARED_PARAMS | TRANSCRIPT_PARAMS | RECORDING_PARAMS | VA_SESSION_PARAMS | TASK_VA_SESSION_PARAMS)
          )

          current_company.save!
          current_company.update_crm_settings

          render_setting
        end

        def fetch_crm_user
          crm_user_id = params['crm_user_id']
          result = CRM::CrmServer.request(url: '/fetch_crm_user', method: :post, params: { crm_user_id: })

          # result is { crm_user: { name: '', id: '' } }
          render json: { result: true, crm_user: (result || {}).with_indifferent_access[:crm_user] }
        rescue StandardError, RestClient::ExceptionWithResponse => e
          Rails.logger.warn(error: e) { "Error in fetch_crm_user : #{e.message}" }
          render json: { result: false }
        end

        private

        def authorize_setting
          authorize [:settings, :operation, :operation_management]
        end

        def render_setting
          render json: {
            chat: current_company.crm_settings.chatbot.attributes,
            voice_call: current_company.crm_settings.voicebot.attributes
          }
        end

        def before_task_va_session?(ujet_version_of_request)
          return false unless ujet_version_of_request.present?
          # for local, dev,or project env
          return false if Gem::Version.new('0.0.0') == Gem::Version.new(ujet_version_of_request)
    
          Gem::Version.new(ujet_version_of_request) < Gem::Version.new(VERSION_OF_TASK_VA)
        rescue ArgumentError
          raise InvalidParameterException, 'The Ujet-version is malformed'
        end
      end
    end
  end
end

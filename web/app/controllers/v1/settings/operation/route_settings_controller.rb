class V1::Settings::Operation::RouteSettingsController < V1::Settings::BaseController

  wrap_parameters :route_setting, include: [:call, :chat, :fifo_prioritization]

  def show
    render_setting
  end

  def update
    route_settings = current_company.route_settings
    route_settings.update(route_setting_params)

    pcm_setting = current_company.call_settings.pcm
    pcm_setting.update(pcm_setting_params)

    current_company.save!

    unless ActiveRecord::Type::Boolean.new.cast(route_settings.call.route_type == 'deltacast')
      Deltacast::DisableService.disable_call
    end

    unless ActiveRecord::Type::Boolean.new.cast(route_settings.chat.route_type == 'deltacast')
      Deltacast::DisableService.disable_chat
    end

    render_setting
  end

  private

  def render_setting
    route_settings = current_company.route_settings

    render json: route_settings.attributes
  end

  def route_setting_params
    @setting_params ||= params.require(:route_setting).permit(
      call: [
        :route_type,
        deltacast: [
          :auto_answer_enabled,
          :expiration_duration,
          :mobile_expiration_duration,
          :max_ignore_projection,
          :unresponsive_threshold,
          :apply_during_active_session,
          :skip_unavailable_cascade_group,
          :max_attempt,
          { agent_voice_detection: [
            :enabled,
            :time_limit
          ] }
        ]
      ],
      chat: [
        :route_type,
        deltacast: [
          :auto_answer_enabled,
          :expiration_duration,
          :max_ignore_projection,
          :call_weight,
          :unresponsive_threshold,
          :apply_during_active_session,
          :skip_unavailable_cascade_group,
          :max_attempt,
        ]
      ],
      fifo_prioritization: [
        :chat,
        :call,
      ]
    )
  end

  def pcm_setting_params
    @pcm_setting_params ||= begin
                              call_settings = route_setting_params[:call] || {}

                              {
                                enabled: call_settings[:route_type] == 'deltacast',
                                expiration_duration: call_settings.dig(:deltacast, :expiration_duration),
                                max_ignore_projection: call_settings.dig(:deltacast, :max_ignore_projection),
                                max_skipped_projection: call_settings.dig(:deltacast, :unresponsive_threshold)
                              }.compact
                            end
  end
end

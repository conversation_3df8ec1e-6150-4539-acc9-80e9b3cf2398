# frozen_string_literal: true

module V1
  module Settings
    module Operation
      class WfmSettingsController < V1::Settings::BaseController
        def show
          render_setting
        end

        def update
          settings_params = params.require(:wfm_setting).permit(:enabled)
          service.update_enabled!(settings_params[:enabled])

          render_setting
        end

        def cleanup
          if service.cleanup
            render_setting
          else
            render json: { message: 'Failed to request cleanup to WFM' }, status: :internal_server_error
          end
        end

        def authorize_setting
          authorize [:settings, :operation, :wfm_settings]
        end

        private

        def render_setting
          render json: current_company.wfm_settings
        end

        def service
          @service ||= Wfm::ActivationService.new
        end
      end
    end
  end
end

class V1::Settings::Operation::FaqSettingsController < V1::Settings::BaseController
  wrap_parameters :faq_setting, include: [:enabled, :url]

  def show
    render_setting
  end

  def update
    settings_params = params.require(:faq_setting).permit(:enabled, :url)
    current_company.faq_settings.update(settings_params)
    current_company.save!
    render_setting
  end

  private

  def authorize_setting
    authorize %i[settings operation operation_management]
  end

  def render_setting
    render json: current_company.faq_settings
  end
end

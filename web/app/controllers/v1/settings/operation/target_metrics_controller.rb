# frozen_string_literal: true

class V1::Settings::Operation::TargetMetricsController < V1::Settings::BaseController
  wrap_parameters :target_metrics, include: [:call, :chat, :video_call]

  def show
    render_target_metrics
  end

  def update
    target_metrics_params = params.require(:target_metrics).permit(self.class.permit_filters)
    update_target_metrics!(target_metrics_params)
    render_target_metrics
  end

  private

  def authorize_setting
    authorize [:settings, :operation, :target_metrics]
  end

  def render_target_metrics
    render json: {
      call: channel_target_metrics(:call),
      chat: channel_target_metrics(:chat),
      video_call: channel_target_metrics(:video_call)
    }
  end

  def update_target_metrics!(target_metrics_params)
    update_channel_target_metrics(:call, target_metrics_params[:call])
    update_channel_target_metrics(:chat, target_metrics_params[:chat])
    update_channel_target_metrics(:video_call, target_metrics_params[:video_call])
    save_target_metrics!
  end

  def update_channel_target_metrics(channel, channel_metrics_params)
    channel_target_metrics(channel).update(channel_metrics_params)
  end

  def channel_target_metrics(channel)
    case channel
    when :call
      current_company.call_settings.target_metric
    when :chat
      current_company.chat_settings.target_metric
    when :video_call
      current_company.video_call_settings.target_metric
    else
      raise ArgumentError, "Invalid channel: #{channel.inspect}"
    end
  end

  def save_target_metrics!
    current_company.save!
  end

  def self.permit_filters
    @permit_filters ||= {
      call: setting_object_permit_filters(Settings::CallSetting.attribute_types[:target_metric]),
      chat: setting_object_permit_filters(Settings::ChatSetting.attribute_types[:target_metric]),
      video_call: setting_object_permit_filters(Settings::VideoCallSetting.attribute_types[:target_metric])
    }
  end
end

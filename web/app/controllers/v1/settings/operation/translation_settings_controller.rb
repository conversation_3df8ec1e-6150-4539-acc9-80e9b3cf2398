class V1::Settings::Operation::TranslationSettingsController < V1::Settings::BaseController
  def show
    render_setting
  end

  def update
    settings_params = params.require(:translation_setting).permit(permitted_param_names)

    default_translation_profile = TranslationProfile.update_default!(model_ids: settings_params[:default_model_ids], glossary_ids: settings_params[:default_glossary_ids])

    current_company.settings.live_translation.update(enabled: settings_params[:enabled],
                                                     default_platform_id: settings_params[:default_platform_id])
    current_company.save!
    render_setting
  end

  private

  def authorize_setting
    authorize [:settings, :operation, :translation_settings]
  end

  def render_setting
    render json: {
      enabled: current_company.settings.live_translation.enabled,
      default_platform_id: current_company.settings.live_translation.default_platform_id,
      default_model_ids: TranslationProfile.default&.translation_model_ids || [],
      default_glossary_ids: TranslationProfile.default&.translation_glossary_ids || []
    }
  end

  def permitted_param_names
    [:enabled, :default_platform_id, { default_model_ids: [] }, { default_glossary_ids: [] }]
  end
end

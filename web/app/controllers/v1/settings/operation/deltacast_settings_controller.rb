class V1::Settings::Operation::DeltacastSettingsController < V1::Settings::BaseController
  wrap_parameters :deltacast_setting, include: [
    :unresponsive_threshold, :apply_during_active_session,
    :skip_unavailable_cascade_group, :call, :chat
  ]

  def show
    render_setting
  end

  def update
    deltacast_settings = current_company.deltacast_settings
    deltacast_settings.update(deltacast_setting_params)

    pcm_setting = current_company.call_settings.pcm
    pcm_setting.update(pcm_setting_params)
    
    if Company.column_names.include?('route_settings')
      route_settings = current_company.route_settings
      route_settings.update(route_setting_params)
    end
    if Menu::Setting.column_names.include?('route_settings')
      ActiveRecord::Base.connection.execute("UPDATE menu_settings SET route_settings = NULL WHERE route_settings IS NOT NULL")
    end

    current_company.save!

    unless ActiveRecord::Type::Boolean.new.cast(deltacast_settings.call.enabled)
      Deltacast::DisableService.disable_call
    end

    unless ActiveRecord::Type::Boolean.new.cast(deltacast_settings.chat.enabled)
      Deltacast::DisableService.disable_chat
    end

    render_setting
  end

  private

  def render_setting
    deltacast_settings = current_company.deltacast_settings
    render json: deltacast_settings.attributes
  end

  def deltacast_setting_params
    @deltacast_setting_params ||= params.require(:deltacast_setting).permit(
      :unresponsive_threshold, :apply_during_active_session, :skip_unavailable_cascade_group,
      { call: [:enabled, :expiration_duration, :mobile_expiration_duration, :max_ignore_projection] },
      { chat: [:enabled, :expiration_duration, :max_ignore_projection, :call_weight] }
    )
  end

  def pcm_setting_params
    @pcm_setting_params ||= begin
      call_settings = deltacast_setting_params[:call] || {}

      {
        enabled: call_settings[:enabled],
        expiration_duration: call_settings[:expiration_duration],
        max_ignore_projection: call_settings[:max_ignore_projection],
        max_skipped_projection: deltacast_setting_params[:unresponsive_threshold]
      }.compact
    end
  end

  def route_setting_params
    @route_setting_params ||= {
      call: {
        route_type: deltacast_setting_params[:call][:enabled] ? 'deltacast' : 'multicast',
        deltacast: {
          expiration_duration: deltacast_setting_params[:call][:expiration_duration],
          mobile_expiration_duration: deltacast_setting_params[:call][:mobile_expiration_duration],
          max_ignore_projection: deltacast_setting_params[:call][:max_ignore_projection],
          unresponsive_threshold: deltacast_setting_params[:unresponsive_threshold],
          apply_during_active_session: deltacast_setting_params[:apply_during_active_session],
          skip_unavailable_cascade_group: deltacast_setting_params[:skip_unavailable_cascade_group],
          max_attempt: 1
        }
      },
      chat: {
        route_type: deltacast_setting_params[:chat][:enabled] ? 'deltacast' : 'multicast',
        deltacast: {
          expiration_duration: deltacast_setting_params[:chat][:expiration_duration],
          max_ignore_projection: deltacast_setting_params[:chat][:max_ignore_projection],
          call_weight: deltacast_setting_params[:chat][:call_weight],
          unresponsive_threshold: deltacast_setting_params[:unresponsive_threshold],
          apply_during_active_session: deltacast_setting_params[:apply_during_active_session],
          skip_unavailable_cascade_group: deltacast_setting_params[:skip_unavailable_cascade_group],
          max_attempt: 1
        }
      }
    }
  end
end

class V1::Settings::Operation::CrmSettingsController < V1::Settings::BaseController
  wrap_parameters :crm_setting, include: [
    :access_allowed
  ]

  def show
    render_setting
  end

  def update
    setting_params = params.require(:crm_setting).permit(
      :access_allowed,
      :call_ticket_title,
      :voicemail_ticket_title,
      :chat_ticket_title,
      :ticket_tags_session_type,
      :ticket_tags_ujet,
      :salesforce_metadata_mapping,
      :end_user_direct_task_object_lookup,
      :end_user_direct_account_lookup,
      :end_user_direct_unattached_task_object_lookup,
      :end_user_no_data_mapping,
      :opportunity_close_days,
      :window_popup_type
    )

    # validation and fill default value
    Settings::CrmSetting.validate_ticket_title!(setting_params)

    # update
    update_current_company(setting_params)

    render_setting
  end

  private

  def authorize_setting
    authorize [:settings, :crm_setting]
  end

  def render_setting
    crm_setting = current_company.crm_settings
    render json: sanitized_attributes(crm_setting.attributes)
  end

  def sanitized_attributes(attributes)
    sanitized = attributes

    sanitized[:backup_access_token_exists] = sanitized[:backup_access_token].present?
    sanitized.delete(:backup_access_token)
    sanitized[:webhook_token_exists] = sanitized[:webhook_token].present?
    sanitized.delete(:webhook_token)

    if sanitized[:custom_crm].is_a?(Hash)
      sanitized[:custom_crm][:basic_auth_password_exists] = sanitized[:custom_crm][:basic_auth_password].present?
      sanitized[:custom_crm].delete(:basic_auth_password)

      sanitized[:custom_crm][:oauth_client_secret_exists] = sanitized[:custom_crm][:oauth_client_secret].present?
      sanitized[:custom_crm].delete(:oauth_client_secret)
    end

    if sanitized[:media_service].is_a?(Hash)
      sanitized[:media_service][:external_storage_google_credentials_json_exists] = sanitized[:media_service][:external_storage_google_credentials_json].present?
      sanitized[:media_service].delete(:external_storage_google_credentials_json)
    end

    sanitized
  end

  def update_current_company(setting_params)
    if current_company.crm_type == :salesforce
      update_sf_crm_settings(setting_params)
    elsif current_company.crm_type == :custom
      update_custom_crm_settings(setting_params)
    end

    crm_setting = current_company.crm_settings
    crm_setting.update(setting_params)
    current_company.save!

    # update redis
    current_company.update_crm_settings
  end

  def update_sf_crm_settings(setting_params)
    # Setting default object type before checking if it should be a task
    if current_company.crm_settings.crm_sub_type == "salescloud"
      current_company.crm_settings.ticket_object_type = 'Opportunity'
    else
      current_company.crm_settings.ticket_object_type = 'Case'
    end

    if setting_params[:end_user_direct_task_object_lookup] || setting_params[:end_user_direct_unattached_task_object_lookup]
      current_company.crm_settings.ticket_object_type = 'Task' #task objects can't be reused
      current_company.crm_settings.create_new_ticket_always = true
    elsif setting_params[:end_user_direct_account_lookup]
      current_company.crm_settings.enabled_multi_cases_detect_outbound_calls = false
      current_company.crm_settings.enabled_create_new_case_option_outbound_calls = false
      current_company.crm_settings.enable_no_case_option_outbound_calls = false
    end
  end

  def update_custom_crm_settings(setting_params)
    current_company.crm_settings.custom_crm.window_popup_type = setting_params[:window_popup_type]
  end
end

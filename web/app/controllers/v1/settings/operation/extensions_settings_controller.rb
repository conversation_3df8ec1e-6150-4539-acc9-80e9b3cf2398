# frozen_string_literal: true

# @deprecated Use Settings::ExtensionsSettingsController
class V1::Settings::Operation::ExtensionsSettingsController < ApplicationController
  before_action :authorize_extensions_settings

  def show
    render_extensions_settings
  end

  def update
    p_params = permitted_params
    after_hours_messages = p_params[:deflection][:after_hours_messages]
    over_capacity_messages = p_params[:deflection][:over_capacity_messages]

    current_company.extensions_settings = p_params
    current_company.save!

    update_target_relations

    update_after_hours_deflection_audible_messages(after_hours_messages)
    update_over_capacity_deflection_audible_messages(over_capacity_messages)

    if p_params[:enabled] == 'true'
      ExtensionNumbersService.new.create_base_extension_number_for_digits(p_params[:extension_digits_length])
    end

    render_extensions_settings
  end

  private

  def update_target_relations
    Menu::TargetMenuRelation.update_global_internal_call_after_hours!
    Menu::TargetMenuRelation.update_global_internal_call_over_capacity!

    OperationHours::OperationHourAssignmentService.new.update_global_internal_call_deflection
  end

  def update_after_hours_deflection_audible_messages(after_hours_messages)
    return if after_hours_messages.blank?

    announcement = after_hours_messages[:announcement]
    options = after_hours_messages[:options]
    message_greeting = after_hours_messages[:message_greeting]
    queue_greeting = after_hours_messages[:queue_greeting]
    voicemail_greeting = after_hours_messages[:voicemail_greeting]

    update_audible_message('call.extension.deflection.ah.announcement', announcement)
    update_audible_message('call.extension.deflection.ah.options', options)
    update_audible_message('call.extension.deflection.ah.message_greeting', message_greeting)
    update_audible_message('call.extension.deflection.ah.queue_greeting', queue_greeting)
    update_audible_message('call.extension.deflection.ah.voicemail_greeting', voicemail_greeting)
  end

  def update_over_capacity_deflection_audible_messages(overcapacity_messages)
    return if overcapacity_messages.blank?

    announcement = overcapacity_messages[:announcement]
    options = overcapacity_messages[:options]
    message_greeting = overcapacity_messages[:message_greeting]
    queue_greeting = overcapacity_messages[:queue_greeting]
    voicemail_greeting = overcapacity_messages[:voicemail_greeting]

    update_audible_message('call.extension.deflection.oc.announcement', announcement)
    update_audible_message('call.extension.deflection.oc.options', options)
    update_audible_message('call.extension.deflection.oc.message_greeting', message_greeting)
    update_audible_message('call.extension.deflection.oc.queue_greeting', queue_greeting)
    update_audible_message('call.extension.deflection.oc.voicemail_greeting', voicemail_greeting)
  end

  def update_audible_message(key, msg)
    return if msg.blank?

    type = msg[:type]
    text = msg[:text]
    audio = msg[:audio]

    message = Audible::Translation.find_by(key:) || {}
    deflection_msg = {
      key => {
        type:,
        text: text || message[:text],
        audio: audio || message[:audio]
      }
    }
    audible_updater = Audible::MessageUpdater.new(menu_id: nil, client_app_id: nil, lang: 'en')
    audible_updater.update(deflection_msg)
  end

  def render_extensions_settings
    render json: ExtensionsSettingsRepresenter.new(current_company.extensions_settings)
  end

  def authorize_extensions_settings
    authorize [:settings, :operation, :extensions_settings]
  end

  def permitted_params
    params.permit(Settings::Operation::ExtensionsSettingsPolicy.permitted_attributes).to_h
  end
end

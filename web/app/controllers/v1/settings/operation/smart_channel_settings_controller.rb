class V1::Settings::Operation::SmartChannelSettingsController < V1::Settings::BaseController
  wrap_parameters :smart_channel_setting, include: [:enabled, :android, :ios]

  def show
    render_setting
  end

  def update
    setting_params = params.require(:smart_channel_setting).permit(:enabled)
    validate_url_params!(setting_params)

    smart_channel_setting = current_company.call_settings.smart_channel
    smart_channel_setting.update(setting_params)
    current_company.save!
    render_setting
  end

  private

  def authorize_setting
    authorize %i[settings operation operation_management]
  end

  def render_setting
    smart_channel_setting = current_company.call_settings.smart_channel
    render json: smart_channel_setting.attributes
  end

  def validate_url_params!(setting_params)
    [:ios, :android].each do |mobile|
      next if setting_params[mobile].blank?
      next if setting_params[mobile].values.all? {|v| v.blank? }

      [:appstore_url, :app_url].each do |name|
        unless valid_url?(setting_params[mobile][name])
          mobile = {
            ios: "iOS",
            android: "Android"
          }[mobile]
          name = {
            appstore_url: "App Store URL",
            app_url: "App Open URL"
          }[name]
          raise ServiceException, "#{mobile} #{name} is invalid"
        end
      end
    end
  end

  def valid_url?(url)
    return if url.blank?
    uri = Addressable::URI.parse(url)
    uri.scheme.present? and uri.host.present?
  end
end

# frozen_string_literal: true

module V1
  module Settings
    module Operation
      class DefaultLayoutSettingsController < V1::Settings::BaseController
        before_action :require_default_layout_setting, only: [:update]

        def index
          render_setting
        end

        def create
          ActiveRecord::Base.transaction do
            @default_layout_setting = AgentDesktopLayouts::LayoutSettingReferenceService.create!(shared_params)
            update_agent_desktop_settings if agent_desktop_settings_present?

            render json: AgentDesktopLayouts::DefaultLayoutRepresenter.new(@default_layout_setting), status: :ok
          end
        rescue ActiveRecord::RecordInvalid => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        def update
          ActiveRecord::Base.transaction do
            @default_layout_setting = AgentDesktopLayouts::LayoutSettingReferenceService.update!(@default_layout_setting, shared_params)
            update_agent_desktop_settings if agent_desktop_settings_present?

            render json: AgentDesktopLayouts::DefaultLayoutRepresenter.new(@default_layout_setting), status: :ok
          end
        rescue ActiveRecord::RecordInvalid => e
          render json: { error: e.message }, status: :unprocessable_entity
        end

        private

        def render_setting
          default_params = { team_id: nil, menu_id: nil, lang: nil }
          @default_layout_setting = AgentDesktopLayouts::AgentDesktopDefaultLayout.find_by(default_params)
          if @default_layout_setting.present?
            render json: AgentDesktopLayouts::DefaultLayoutRepresenter.new(@default_layout_setting)
          else
            render json: {}
          end
        end

        def require_default_layout_setting
          @default_layout_setting = AgentDesktopLayouts::AgentDesktopDefaultLayout.find(params[:id])
        end

        def update_agent_desktop_settings
          agent_desktop_settings = current_company.agent_desktop_settings
          setting_params = agent_desktop_settings_params.to_h[:agent_desktop_settings]
          agent_desktop_settings.update(setting_params)
          current_company.save!
        end

        def agent_desktop_settings_present?
          params.dig(:default_layout_setting, :agent_desktop_settings).present?
        end

        def shared_params
          params.require(:default_layout_setting).permit(:team_id, :menu_id, :lang, configuration: {})
        end

        def agent_desktop_settings_params
          params.require(:default_layout_setting).permit(
            agent_desktop_settings: [
              :adapter_position,
              { session_data_feed_settings: [:enabled],
                session_tabs_settings: [
                  :open_session_tabs_limit, :open_session_tabs_timeout,
                  :session_tabs_menu_limit, :session_tabs_menu_timeout
                ] }
            ]
          )
        end
      end
    end
  end
end

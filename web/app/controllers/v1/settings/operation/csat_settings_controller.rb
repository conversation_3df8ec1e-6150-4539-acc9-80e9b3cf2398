class V1::Settings::Operation::CsatSettingsController < V1::Settings::BaseController
  wrap_parameters :csat_setting, include: [:enabled, :post_in_crm, :feedback_enabled]

  def show
    render_setting
  end

  def update
    setting_params = params.require(:csat_setting).permit(
      :enabled, :post_in_crm, :feedback_enabled
    )
    current_company.csat_settings.update(setting_params)
    current_company.save!
    render_setting
  end

  private

  def authorize_setting
    authorize %i[settings operation operation_management]
  end

  def render_setting
    render json: current_company.csat_settings
  end
end

class V1::Settings::Operation::PreSessionSmartActionSettingsController < V1::Settings::BaseController
  PERMITTED_PARAMS = [
    :enabled,
    {
      wait_time: [:enabled, :threshold]
    }
  ].freeze

  wrap_parameters :psa_setting, include: PERMITTED_PARAMS

  def show
    render_setting
  end

  def update
    current_company.settings.pre_session_smart_action.update(permitted_params)
    current_company.save!

    render_setting
  end

  private

  def permitted_params
    @permitted_params ||= params.permit(PERMITTED_PARAMS)
  end

  def authorize_setting
    authorize [:settings, :operation, :operation_management]
  end

  def render_setting
    render json: current_company.settings.pre_session_smart_action
  end
end

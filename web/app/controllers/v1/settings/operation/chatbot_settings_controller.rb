# frozen_string_literal: true

# deprecated: replaced with V1::Settings::Operation::VirtualAgentSettingsController
# remove in followup release after FE starts using the new API
module V1
  module Settings
    module Operation
      class ChatbotSettingsController < V1::Settings::BaseController
        # note that the enable/disable chatbot setting is controlled by V1::Settings::ChatbotSettingsController

        PERMITTED_PARAMS = %i[
          create_ticket_option
          create_ticket_option_threshold
          close_ticket_by_virtual_agent
          session_variable_enabled
          assign_ticket_option
          assign_to_crm_user_id
          assign_to_crm_user_name
        ].freeze

        SHARED_PERMITTED_PARAMS = %i[
          assign_ticket_option
          assign_to_crm_user_id
          assign_to_crm_user_name
        ].freeze

        wrap_parameters :chatbot_setting, include: PERMITTED_PARAMS

        # GET /v1/settings/operation/chatbot
        def show
          render_setting
        end

        # PATCH|PUT /v1/settings/operation/chatbot
        # updates Settings::CrmSetting::ChatbotSetting
        def update
          setting_params = params.require(:chatbot_setting).permit(PERMITTED_PARAMS)
          setting_params = ::Settings::CrmSetting.normalize_virtual_agent_ticket_assignment_params(setting_params)

          chatbot_setting = current_company.crm_settings.chatbot
          chatbot_setting.update(setting_params)

          # Update Settings::CrmSetting::VoicebotSetting together because the ticket assignment options
          # applies to both ChatbotSetting and VoicebotSetting.
          shared_setting_params = params.require(:chatbot_setting).permit(SHARED_PERMITTED_PARAMS)
          shared_setting_params = ::Settings::CrmSetting.normalize_virtual_agent_ticket_assignment_params(shared_setting_params)

          voicebot_setting = current_company.crm_settings.voicebot
          voicebot_setting.update(shared_setting_params)

          # update database
          current_company.save!

          # update redis
          current_company.update_crm_settings

          render_setting
        end

        def fetch_crm_user
          crm_user_id = params['crm_user_id']
          result = CRM::CrmServer.request(url: '/fetch_crm_user', method: :post, params: { crm_user_id: crm_user_id})

          # result is { crm_user: { name: '', id: '' } }
          render json: { result: true, crm_user: (result || {}).with_indifferent_access[:crm_user] }
        rescue StandardError, RestClient::ExceptionWithResponse => e
          Rails.logger.warn(error: e) { "Error in fetch_crm_user : #{e.message}" }
          render json: { result: false }
        end

        private

        def authorize_setting
          authorize %i[settings operation operation_management]
        end

        def render_setting
          render json: current_company.crm_settings.chatbot.attributes
        end
      end
    end
  end
end

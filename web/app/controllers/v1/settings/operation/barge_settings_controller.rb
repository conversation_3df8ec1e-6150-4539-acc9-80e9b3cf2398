# frozen_string_literal: true

class V1::Settings::Operation::BargeSettingsController < ApplicationController
  before_action :authorize_barge_settings

  def show
    render_barge_settings
  end

  def update
    service = SettingsService::BargeSettingsService.new(company: current_company,
                                                        ujet_version_of_request: request.headers['Ujet-Version'])
    render json: service.update_barge_settings(permitted_params)
  end

  private

  def render_barge_settings
    service = SettingsService::BargeSettingsService.new(company: current_company,
                                                        ujet_version_of_request: request.headers['Ujet-Version'])
    render json: service.barge_settings
  end

  def authorize_barge_settings
    authorize [:settings, :operation, :barge_settings]
  end

  def permitted_params
    params.permit(Settings::Operation::BargeSettingsPolicy.permitted_attributes)
  end
end

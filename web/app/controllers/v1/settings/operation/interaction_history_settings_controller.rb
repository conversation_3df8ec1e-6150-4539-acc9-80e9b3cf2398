# frozen_string_literal: true

module V1
  module Settings
    module Operation
      class InteractionHistorySettingsController < V1::Settings::BaseController
        before_action :validate_external_storage, only: :update
        before_action :validate_session_summarization, only: :update

        def show
          render_setting
        end

        def update
          settings = current_company.settings.interaction_history
          settings.update(interaction_history_params)
          current_company.save!
          render_setting
        end

        private

        def render_setting
          render json: current_company.settings.interaction_history.attributes
        end

        def interaction_history_params
          params.require(:interaction_history).permit(
            :data_retrieval_day,
            call: [:session_summary],
            chat: [:session_summary]
          )
        end

        def validate_external_storage
          return if current_company.external_storage_settings.external_storage_enabled

          raise ServiceException, 'External Storage required to enable Interaction History features'
        end

        def validate_session_summarization
          [:call, :chat].each do |channel|
            next unless interaction_history_params[channel][:session_summary].present?

            session_summarization = current_company.public_send("#{channel}_settings")
                                                   .agent_assist.session_summarization

            unless session_summarization.enabled
              raise ServiceException,
                    'Agent Assist Session Summarization required to enable Session Summary'
            end
          end
        end
      end
    end
  end
end

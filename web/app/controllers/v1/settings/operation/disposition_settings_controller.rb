# frozen_string_literal: true

class V1::Settings::Operation::DispositionSettingsController < ApplicationController
  wrap_parameters :disposition_settings
  before_action :authorize_disposition_settings

  def show
    render json: disposition_settings
  end

  def update
    update_disposition_settings!
    render json: disposition_settings
  end

  private

  def authorize_disposition_settings
    authorize :disposition_settings
  end

  def disposition_settings
    current_company.settings.disposition
  end

  def update_disposition_settings!
    safe_company_update { |company| company.settings.disposition.update(permitted_params) }
  end

  def permitted_params
    params.require(:disposition_settings).permit(DispositionSettingsPolicy.permitted_attributes)
  end
end

# frozen_string_literal: true

class V1::Settings::Operation::WrapUpExceededSettingsController < ApplicationController
  before_action :authorize_wrap_up_exceeded_settings

  def show
    render_wrap_up_exceeded_settings
  end

  def update
    safe_company_update { |company| company.settings.wrap_up_exceeded.update(permitted_params) }
    render_wrap_up_exceeded_settings
  end

  private

  def render_wrap_up_exceeded_settings
    render json: current_company.settings.wrap_up_exceeded
  end

  def authorize_wrap_up_exceeded_settings
    authorize [:settings, :operation, :wrap_up_exceeded_settings]
  end

  def permitted_params
    params.permit(Settings::Operation::WrapUpExceededSettingsPolicy.permitted_attributes)
  end
end


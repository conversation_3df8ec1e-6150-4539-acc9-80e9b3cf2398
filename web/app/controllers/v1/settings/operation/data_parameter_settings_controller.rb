# frozen_string_literal: true

module V1
  module Settings
    module Operation
      class DataParameterSettingsController < V1::Settings::BaseController
        wrap_parameters :data_parameters, include: [:voice]

        # GET /v1/settings/operation/data_parameters
        def show
          render json: data_parameter_settings
        end

        # PUT|PATCH /v1/settings/operation/data_parameters
        def update
          permitted_params = params.permit(
            voice: [
              :capture_inbound_sip_headers,
              { sip_header_caller_id: [
                :enabled,
                :field_name
              ] },
              :sip_header_in_crm_record,
              :sip_header_in_metadata
            ],
            sdk_custom_data: [
              send_sdk_custom_data_to_metadata_file: [
                send_signed_mobile_to_metadata: [:enabled],
                send_unsigned_mobile_to_metadata: [:enabled],
                send_signed_web_to_metadata: [:enabled],
                send_unsigned_web_to_metadata: [:enabled],
              ],
              send_sdk_custom_data_to_crm: [
                send_signed_mobile_to_crm: [:enabled],
                send_unsigned_mobile_to_crm: [:enabled],
                send_signed_web_to_crm: [:enabled],
                send_unsigned_web_to_crm: [:enabled],
              ],
              display_in_agent_adapter: [
                display_signed_mobile: [:enabled],
                display_unsigned_mobile: [:enabled],
                display_signed_web: [:enabled],
                display_unsigned_web: [:enabled]
              ]
            ]
          )

          current_company.settings.data_parameters.update(permitted_params)
          current_company.save!

          render json: data_parameter_settings
        end

        private

        def update_voice_data_parameter_settings(voice_data_parameters)
          Company.current.ivr_settings.data_parameters.update(voice_data_parameters)
          Company.current.save!
        end

        def data_parameter_settings
          current_company.settings.data_parameters
        end
      end
    end
  end
end

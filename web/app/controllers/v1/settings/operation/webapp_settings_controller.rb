class V1::Settings::Operation::WebappSettingsController < V1::Settings::BaseController
  wrap_parameters :webapp_setting, include: [:idle_timeout]

  def show
    render_setting
  end

  def update
    settings_params = params.require(:webapp_setting).permit(permitted_param_names)
    current_company.settings.update(webapp_idle_timeout: settings_params[:idle_timeout])
    current_company.save!
    render_setting
  end

  private

  def authorize_setting
    authorize %i[settings operation webapp_settings]
  end

  def render_setting
    webapp_setting = {
      idle_timeout: current_company.settings.webapp_idle_timeout
    }
    render json: webapp_setting
  end

  def permitted_param_names
    [:idle_timeout]
  end
end

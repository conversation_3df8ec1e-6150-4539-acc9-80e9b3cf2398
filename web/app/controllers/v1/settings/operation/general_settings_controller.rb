# frozen_string_literal: true

# Operation general settings controller (/v1/settings/operation/general)
class V1::Settings::Operation::GeneralSettingsController < V1::Settings::BaseController
  PERMITTED_PARAMS = {
    webapp_setting: [
      :idle_timeout
    ],
    queue_setting: [
      :queue_allocation_timeout,
      :queue_cascade_interval,
      :use_queue_priority
    ],
    customer_abandoned_call_setting: [
      :service_level_abandon_time_threshold,
      :hide_eu_in_menu_abandoned
    ],
    customer_abandoned_chat_setting: [
      :service_level_abandon_time_threshold
    ],
    ticket_creation_setting: [
      :create_new_ticket_always,
      :append_closed_ticket,
      :append_closed_ticket_days,
      :create_call_ticket_after_connected,
      :allow_use_entered_number_to_make_call,
      :abandoned_call_ticket,
      :abandoned_call_ticket_with_wait_time_sms,
      :abandoned_chat_ticket,
      :abandoned_chat_ticket_with_few_messages,
      :abandoned_chat_ticket_message_threshold,
      :delay_chat_ticket_consumer_messages,
      :delay_chat_ticket_consumer_message_threshold,
      :provided_ticket_creation_method,
      :provided_ticket_api_scheduled_call_method_enabled,
      :provided_ticket_api_scheduled_call_method,
      :click_to_call_ticket_creation_method
    ],
    multiple_account_cases_setting: [
      :enabled_multi_cases_detect_outbound_calls,
      :enabled_create_new_case_option_outbound_calls,
      :enable_no_case_option_outbound_calls,
      :enable_prompt_account_record_selection_inbound_calls,
      :enable_new_account_creation_inbound_calls,
      :enable_no_case_option_inbound_calls,
      :inbound_calls_auto_select_account_timing
    ],
    collect_customer_email_setting: [
      :enable_collect_email_for_call,
      :collect_email_for_call_automatically,
      :enable_collect_email_for_chat,
      :collect_email_for_chat_automatically
    ],
    direct_access_point_setting: [
      :enable_user_segment_direct_access_point
    ],
    agent_login_status_setting: [
      :default_login_agent_status,
      :agent_stops_inquiries_enabled,
      :allow_call_transfer_unavailable_agent,
      :allow_chat_transfer_unavailable_agent,
      :agent_status_breakthrough_enabled,
      :global_user_status_list_id,
      :status_selection_allow_unavailable,
      { availability_preference: [
        :forward_calls_to_phone_enabled,
        :availability_filter_enabled
      ] }
    ],
    crm_operations_setting: [
      :pop_up_object_outbound_call,
      :skip_account_creation,
      :skip_account_lookup,
      :skip_record_creation,
      :push_session_data_to_account,
      comment_creation: [
        :recording_link,
        :chat_link,
        :cobrowse_recording_link
      ]
    ],
    transfer_history_setting: [
      :display_transfer_history_in_adapter
    ]
  }.freeze

  wrap_parameters :general_setting, include: PERMITTED_PARAMS.keys

  def show
    render_setting
  end

  def update
    webapp_setting_prams = permitted_webapp_setting_params
    update_webapp_setting(webapp_setting_prams)

    queue_setting_params = permitted_queue_setting_params
    update_queue_setting(queue_setting_params)

    customer_abandoned_call_setting_params = permitted_customer_abandoned_call_setting_params
    update_customer_abandoned_call_setting(customer_abandoned_call_setting_params)

    customer_abandoned_chat_setting_params = permitted_customer_abandoned_chat_setting_params
    update_customer_abandoned_chat_setting(customer_abandoned_chat_setting_params)

    ticket_creation_setting_params = normalize_ticket_creation_setting_params(permitted_ticket_creation_setting_params)
    changed_crm_settings = update_ticket_creation_setting(ticket_creation_setting_params)
    multiple_account_cases_setting_params = permitted_multiple_account_cases_setting_params
    changed_crm_settings = update_multiple_account_cases_setting(multiple_account_cases_setting_params) || changed_crm_settings
    comment_creation_params = permitted_comment_creation_params
    changed_crm_settings = update_comment_creation_setting(comment_creation_params) || changed_crm_settings

    collect_email_setting_params = permitted_collect_customer_email_setting_params
    update_collect_customer_email_setting(collect_email_setting_params)

    direct_access_point_setting_params = permitted_direct_access_point_setting_params
    update_direct_access_point_setting(direct_access_point_setting_params)

    agent_login_status_params = permitted_agent_login_status_setting_params
    update_agent_login_status_setting(agent_login_status_params)

    crm_operations_setting_params = permitted_crm_operations_setting_params
    changed_crm_settings = update_crm_operations_setting(crm_operations_setting_params) || changed_crm_settings

    update_transfer_history_setting(permitted_transfer_history_setting_params)

    save_setting

    # update redis
    current_company.update_crm_settings if changed_crm_settings

    render_setting
  end

  def update_global_avatar
    global_avatar_params = params.require(:global_avatar).permit(:data_uri, :force_global_avatar)

    # empty or invalid data_uri will be ignored silently
    current_company.company_global_avatar_data_uri = global_avatar_params[:data_uri]

    unless global_avatar_params[:force_global_avatar].nil?
      current_company.settings.update({ force_global_avatar: global_avatar_params[:force_global_avatar] })
    end
    avatar_changed = current_company.global_avatar_changed? || current_company.settings_changed?
    current_company.save!
    GlobalAvatarSyncWorker.perform_async if avatar_changed
    render_setting
  end

  private

  def authorize_setting
    authorize [:settings, :operation, :operation_management]
  end

  def render_setting
    render json: Settings::Operation::GeneralSettingsRepresenter.for_user(current_user).new(
      OpenStruct.new(
        crm_type: current_company.crm_type,
        queue_setting:,
        webapp_setting:,
        customer_abandoned_call_setting:,
        customer_abandoned_chat_setting:,
        ticket_creation_setting:,
        multiple_account_cases_setting:,
        collect_customer_email_setting:,
        direct_access_point_setting:,
        agent_login_status_setting:,
        multiple_mobile_apps_setting:,
        company_global_avatar: default_avatar_setting,
        crm_operations_setting:,
        external_storage_enabled: current_company.external_storage_settings.external_storage_enabled,
        transfer_history_setting:
      )
    )
  end

  def webapp_setting
    { idle_timeout: current_company.settings.webapp_idle_timeout }
  end

  def permitted_webapp_setting_params
    general_setting_params.fetch(:webapp_setting, {})
  end

  def update_webapp_setting(webapp_setting)
    settings = {
      webapp_idle_timeout: webapp_setting[:idle_timeout]
    }.compact

    current_company.settings.update(settings) if settings.present?
  end

  def queue_setting
    {
      queue_allocation_timeout: current_company.settings.queue_allocation_timeout,
      queue_cascade_interval: current_company.settings.queue_cascade_interval,
      use_queue_priority: current_company.settings.use_queue_priority
    }
  end

  def permitted_queue_setting_params
    general_setting_params.fetch(:queue_setting, {})
  end

  def update_queue_setting(queue_setting)
    queue_setting = queue_setting.reject { |_, v| v.nil? }
    current_company.settings.update(queue_setting) if queue_setting.present?
  end

  def customer_abandoned_call_setting
    {
      service_level_abandon_time_threshold: current_company.call_settings.target_metric.service_level_abandon_time_threshold,
      hide_eu_in_menu_abandoned: current_company.stats_settings.calls.hide_eu_in_menu_abandoned
    }
  end

  def customer_abandoned_chat_setting
    {
      service_level_abandon_time_threshold: current_company.chat_settings.target_metric.service_level_abandon_time_threshold
    }
  end

  def permitted_customer_abandoned_call_setting_params
    general_setting_params.fetch(:customer_abandoned_call_setting, {})
  end

  def permitted_customer_abandoned_chat_setting_params
    general_setting_params.fetch(:customer_abandoned_chat_setting, {})
  end

  def update_customer_abandoned_call_setting(customer_abandoned_call_setting)
    call_target_metric = {
      service_level_abandon_time_threshold: customer_abandoned_call_setting[:service_level_abandon_time_threshold]
    }.compact

    current_company.call_settings.update(target_metric: call_target_metric) if call_target_metric.present?

    stats_calls = {
      hide_eu_in_menu_abandoned: customer_abandoned_call_setting[:hide_eu_in_menu_abandoned]
    }.compact

    current_company.stats_settings.update(calls: stats_calls) if stats_calls.present?
  end

  def update_customer_abandoned_chat_setting(customer_abandoned_chat_setting)
    chat_target_metric = {
      service_level_abandon_time_threshold: customer_abandoned_chat_setting[:service_level_abandon_time_threshold]
    }.compact

    current_company.chat_settings.update(target_metric: chat_target_metric) if chat_target_metric.present?
  end

  def crm_operations_setting
    {
      pop_up_object_outbound_call: current_company.crm_settings.pop_up_object_outbound_call,
      skip_account_creation: current_company.crm_settings.skip_account_creation,
      skip_account_lookup: current_company.crm_settings.skip_account_lookup,
      skip_record_creation: current_company.crm_settings.skip_record_creation,
      push_session_data_to_account: current_company.crm_settings.push_session_data_to_account,
      comment_creation: current_company.crm_settings.comment_creation.attributes
    }
  end

  def ticket_creation_setting
    {
      create_new_ticket_always: current_company.crm_settings.create_new_ticket_always?,
      append_closed_ticket: current_company.crm_settings.append_closed_ticket,
      append_closed_ticket_days: current_company.crm_settings.append_closed_ticket_days,
      create_call_ticket_after_connected: current_company.crm_settings.create_call_ticket_after_connected,
      allow_use_entered_number_to_make_call: current_company.crm_settings.allow_use_entered_number_to_make_call,
      abandoned_call_ticket: current_company.call_settings.abandoned_call_ticket,
      abandoned_call_ticket_with_wait_time_sms: current_company.call_settings.abandoned_call_ticket_with_wait_time_sms,
      abandoned_chat_ticket: current_company.chat_settings.abandoned_chat_ticket,
      abandoned_chat_ticket_with_few_messages: current_company.chat_settings.abandoned_chat_ticket_with_few_messages,
      abandoned_chat_ticket_message_threshold: current_company.chat_settings.abandoned_chat_ticket_message_threshold,
      delay_chat_ticket_consumer_messages: current_company.chat_settings.delay_chat_ticket_consumer_messages,
      delay_chat_ticket_consumer_message_threshold: current_company.chat_settings.delay_chat_ticket_consumer_message_threshold,
      provided_ticket_creation_method: current_company.crm_settings.provided_ticket_creation_method,
      provided_ticket_api_scheduled_call_method_enabled: current_company.crm_settings.provided_ticket_api_scheduled_call_method_enabled,
      provided_ticket_api_scheduled_call_method: current_company.crm_settings.provided_ticket_api_scheduled_call_method,
      click_to_call_ticket_creation_method: current_company.crm_settings.click_to_call_ticket_creation_method
    }
  end

  def multiple_account_cases_setting
    {
      enabled_multi_cases_detect_outbound_calls: current_company.crm_settings.enabled_multi_cases_detect_outbound_calls,
      enabled_create_new_case_option_outbound_calls: current_company.crm_settings.enabled_create_new_case_option_outbound_calls,
      enable_no_case_option_outbound_calls: current_company.crm_settings.enable_no_case_option_outbound_calls,
      enable_prompt_account_record_selection_inbound_calls: current_company.crm_settings.enable_prompt_account_record_selection_inbound_calls,
      enable_new_account_creation_inbound_calls: current_company.crm_settings.enable_new_account_creation_inbound_calls,
      enable_no_case_option_inbound_calls: current_company.crm_settings.enable_no_case_option_inbound_calls,
      inbound_calls_auto_select_account_timing: current_company.crm_settings.inbound_calls_auto_select_account_timing
    }
  end

  def permitted_ticket_creation_setting_params
    general_setting_params.fetch(:ticket_creation_setting, {})
  end

  def normalize_ticket_creation_setting_params(params)
    threshold = params[:delay_chat_ticket_consumer_message_threshold]
    if threshold.present? && (threshold.to_i < 1 || threshold.to_i > 10)
      params[:delay_chat_ticket_consumer_message_threshold] = 1
    end
    params
  end

  def permitted_multiple_account_cases_setting_params
    general_setting_params.fetch(:multiple_account_cases_setting, {})
  end

  def permitted_comment_creation_params
    general_setting_params.fetch(:crm_operations_setting, {}).fetch(:comment_creation, {})
  end

  def update_crm_operations_setting(crm_operations_setting)
    return false unless [:custom, :salesforce, :zendesk, :servicenow].include?(current_company.crm_type)
    changed_crm_settings = false

    disable_flexible_inbound = false

    if current_company.crm_type == :salesforce
      if crm_operations_setting[:skip_account_creation]
        disable_flexible_inbound = true
      elsif crm_operations_setting[:skip_record_creation] && (current_company.crm_settings.end_user_direct_unattached_task_object_lookup || current_company.crm_settings.end_user_no_data_mapping)
        disable_flexible_inbound = true
      end
    elsif crm_operations_setting[:skip_account_creation] || crm_operations_setting[:skip_record_creation]
      disable_flexible_inbound = true
    end

    crm_setting = {
      pop_up_object_outbound_call: crm_operations_setting[:pop_up_object_outbound_call],
      skip_account_creation: crm_operations_setting[:skip_account_creation],
      skip_account_lookup: crm_operations_setting[:skip_account_lookup],
      skip_record_creation: crm_operations_setting[:skip_record_creation],
      push_session_data_to_account: crm_operations_setting[:push_session_data_to_account],
      enable_prompt_account_record_selection_inbound_calls: disable_flexible_inbound ? false : nil
    }.compact

    if crm_setting.present?
      current_company.crm_settings.update(crm_setting)
      changed_crm_settings = true
    end

    changed_crm_settings
  end

  # Returns boolean indicating if crm_settings has been changed that will be used to update redis
  def update_ticket_creation_setting(ticket_creation_setting)
    changed_crm_settings = false
    unless ticket_creation_setting[:create_new_ticket_always].nil?
      current_company.crm_settings.update_create_new_ticket_always(ticket_creation_setting[:create_new_ticket_always],
                                                                   current_company.crm_type)
      changed_crm_settings = true
    end

    if current_company.crm_settings.create_new_ticket_always
      current_company.crm_settings.append_closed_ticket = false
      changed_crm_settings = true
    end

    crm_setting = {
      create_call_ticket_after_connected: ticket_creation_setting[:create_call_ticket_after_connected],
      allow_use_entered_number_to_make_call: ticket_creation_setting[:allow_use_entered_number_to_make_call],
      provided_ticket_creation_method: ticket_creation_setting[:provided_ticket_creation_method],
      provided_ticket_api_scheduled_call_method_enabled: ticket_creation_setting[:provided_ticket_api_scheduled_call_method_enabled],
      provided_ticket_api_scheduled_call_method: ticket_creation_setting[:provided_ticket_api_scheduled_call_method],
      click_to_call_ticket_creation_method: ticket_creation_setting[:click_to_call_ticket_creation_method],
      append_closed_ticket: ticket_creation_setting[:append_closed_ticket],
      append_closed_ticket_days: ticket_creation_setting[:append_closed_ticket_days]
    }.compact
    if crm_setting.present?
      current_company.crm_settings.update(crm_setting)
      changed_crm_settings = true
    end

    call_setting = {
      abandoned_call_ticket: ticket_creation_setting[:abandoned_call_ticket],
      abandoned_call_ticket_with_wait_time_sms: ticket_creation_setting[:abandoned_call_ticket_with_wait_time_sms]
    }.compact
    current_company.call_settings.update(call_setting) if call_setting.present?

    chat_setting = {
      abandoned_chat_ticket: ticket_creation_setting[:abandoned_chat_ticket],
      abandoned_chat_ticket_with_few_messages: ticket_creation_setting[:abandoned_chat_ticket_with_few_messages],
      abandoned_chat_ticket_message_threshold: ticket_creation_setting[:abandoned_chat_ticket_message_threshold],
      delay_chat_ticket_consumer_messages: ticket_creation_setting[:delay_chat_ticket_consumer_messages],
      delay_chat_ticket_consumer_message_threshold: ticket_creation_setting[:delay_chat_ticket_consumer_message_threshold]
    }.compact
    current_company.chat_settings.update(chat_setting) if chat_setting.present?

    changed_crm_settings
  end

  # Returns boolean indicating if crm_settings has been changed that will be used to update redis
  def update_multiple_account_cases_setting(multiple_account_cases_setting)
    changed_crm_settings = false

    crm_setting = {
      enabled_multi_cases_detect_outbound_calls: multiple_account_cases_setting[:enabled_multi_cases_detect_outbound_calls],
      enabled_create_new_case_option_outbound_calls: multiple_account_cases_setting[:enabled_create_new_case_option_outbound_calls],
      enable_no_case_option_outbound_calls: multiple_account_cases_setting[:enable_no_case_option_outbound_calls],
      enable_prompt_account_record_selection_inbound_calls: multiple_account_cases_setting[:enable_prompt_account_record_selection_inbound_calls],
      enable_new_account_creation_inbound_calls: multiple_account_cases_setting[:enable_new_account_creation_inbound_calls],
      enable_no_case_option_inbound_calls: multiple_account_cases_setting[:enable_no_case_option_inbound_calls],
      inbound_calls_auto_select_account_timing: multiple_account_cases_setting[:inbound_calls_auto_select_account_timing]
    }.compact
    if crm_setting.present?
      current_company.crm_settings.update(crm_setting)
      changed_crm_settings = true
    end

    changed_crm_settings
  end

  def update_comment_creation_setting(comment_creation_setting)
    comment_creation = {
      recording_link: comment_creation_setting[:recording_link],
      chat_link: comment_creation_setting[:chat_link],
      cobrowse_recording_link: comment_creation_setting[:cobrowse_recording_link]
    }.compact

    if comment_creation.present?
      current_company.crm_settings.update({ comment_creation: comment_creation })
      true
    else
      false
    end
  end

  def collect_customer_email_setting
    {
      enable_collect_email_for_call: current_company.call_settings.collect_email.enabled,
      collect_email_for_call_automatically: current_company.call_settings.collect_email.for_all_queues,
      enable_collect_email_for_chat: current_company.chat_settings.collect_email.enabled,
      collect_email_for_chat_automatically: current_company.chat_settings.collect_email.for_all_queues
    }
  end

  def permitted_collect_customer_email_setting_params
    general_setting_params.fetch(:collect_customer_email_setting, {})
  end

  def update_collect_customer_email_setting(collect_email_setting_params)
    collect_email_for_call = {
      enabled: collect_email_setting_params[:enable_collect_email_for_call],
      for_all_queues: collect_email_setting_params[:collect_email_for_call_automatically]
    }.compact
    current_company.call_settings.update(collect_email: collect_email_for_call) if collect_email_for_call.present?

    collect_email_for_chat = {
      enabled: collect_email_setting_params[:enable_collect_email_for_chat],
      for_all_queues: collect_email_setting_params[:collect_email_for_chat_automatically]
    }.compact
    current_company.chat_settings.update(collect_email: collect_email_for_chat) if collect_email_for_chat.present?
  end

  def direct_access_point_setting
    {
      enable_user_segment_direct_access_point: current_company.crm_settings.access_allowed
    }
  end

  def permitted_direct_access_point_setting_params
    general_setting_params.fetch(:direct_access_point_setting, {})
  end

  def permitted_crm_operations_setting_params
    general_setting_params.fetch(:crm_operations_setting, {})
  end

  def update_direct_access_point_setting(direct_access_point_setting)
    crm_setting = {
      access_allowed: direct_access_point_setting[:enable_user_segment_direct_access_point]
    }.compact

    current_company.crm_settings.update(crm_setting) if crm_setting.present?
  end

  def agent_login_status_setting
    {
      default_login_agent_status: current_company.settings.default_login_agent_status,
      agent_stops_inquiries_enabled: current_company.settings.agent_stops_inquiries_enabled,
      allow_call_transfer_unavailable_agent: current_company.settings.allow_call_transfer_unavailable_agent,
      allow_chat_transfer_unavailable_agent: current_company.settings.allow_chat_transfer_unavailable_agent,
      availability_preference: {
        forward_calls_to_phone_enabled: current_company.settings.availability_preference.forward_calls_to_phone_enabled,
        availability_filter_enabled: current_company.settings.availability_preference.availability_filter_enabled
      },
      agent_status_breakthrough_enabled: current_company.settings.agent_status_breakthrough_enabled,
      global_user_status_list_id: current_company.settings.global_user_status_list_id,
      status_selection_allow_unavailable: current_company.settings.status_selection_allow_unavailable
    }
  end

  def permitted_agent_login_status_setting_params
    general_setting_params.fetch(:agent_login_status_setting, {})
  end

  def update_agent_login_status_setting(agent_login_status_setting)
    return if agent_login_status_setting.blank?

    default_login_status = agent_login_status_setting.fetch(:default_login_agent_status, nil)
    unless default_login_status.nil? || UserStatusService.can_set?(user: current_user, status: default_login_status)
      raise ServiceException, "User cannot set default login agent status to #{default_login_status}"
    end

    setting = {
      default_login_agent_status: agent_login_status_setting[:default_login_agent_status],
      agent_stops_inquiries_enabled: agent_login_status_setting[:agent_stops_inquiries_enabled],
      allow_call_transfer_unavailable_agent: agent_login_status_setting[:allow_call_transfer_unavailable_agent],
      allow_chat_transfer_unavailable_agent: agent_login_status_setting[:allow_chat_transfer_unavailable_agent],
      availability_preference: {
        forward_calls_to_phone_enabled: agent_login_status_setting[:availability_preference][:forward_calls_to_phone_enabled],
        availability_filter_enabled: agent_login_status_setting[:availability_preference][:availability_filter_enabled]
      },
      agent_status_breakthrough_enabled: agent_login_status_setting[:agent_status_breakthrough_enabled],
      status_selection_allow_unavailable: agent_login_status_setting[:status_selection_allow_unavailable]
    }

    global_user_status_list_id = agent_login_status_setting[:global_user_status_list_id]
    global_user_status_list_updated = false

    if global_user_status_list_id_valid?(global_user_status_list_id)
      setting[:global_user_status_list_id] = global_user_status_list_id
      global_user_status_list_updated = true
    end

    current_company.settings.update(setting)
    if global_user_status_list_updated
      UserStatusListService.global_user_status_list_id_update_callbacks(global_user_status_list_id)
    end
  end

  def multiple_mobile_apps_setting
    {
      use_multiple_mobile_apps: current_company.settings.use_multiple_mobile_apps
    }
  end

  def default_avatar_setting
    {
      default_avatar_url: current_company.company_global_avatar_url,
      force_global_avatar: current_company.settings.force_global_avatar
    }
  end

  def transfer_history_setting
    {
      display_transfer_history_in_adapter: current_company.settings.display_transfer_history_in_adapter
    }
  end

  def permitted_transfer_history_setting_params
    general_setting_params.fetch(:transfer_history_setting, {})
  end

  def update_transfer_history_setting(transfer_history_setting)
    return if transfer_history_setting.blank?

    display_transfer_history_in_adapter = transfer_history_setting[:display_transfer_history_in_adapter]
    current_company.settings.update(display_transfer_history_in_adapter: !!display_transfer_history_in_adapter)
  end

   # global user_status_list can be nil, all_statuses_list, or any valid id but not zero
  def global_user_status_list_id_valid?(global_user_status_list_id)
    return true if global_user_status_list_id.nil?
    return true if global_user_status_list_id == UserStatusList.all_statuses_list.id
    return false if global_user_status_list_id.to_i.zero? # to_i turns nil to zero, it must be after nil check
    return true if UserStatusListService.get_by_id(global_user_status_list_id)

    false
  end

  def save_setting
    current_company.save!
  end

  def general_setting_params
    @general_setting_params ||=
      params.permit(general_setting: PERMITTED_PARAMS).require(:general_setting)
  end
end

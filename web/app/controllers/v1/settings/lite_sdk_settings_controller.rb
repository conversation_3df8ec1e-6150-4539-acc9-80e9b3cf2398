class V1::Settings::LiteSdkSettingsController < V1::Settings::BaseController
  wrap_parameters :lite_sdk_setting, include: [:enable_setup, :enable_in_app, :enable_pro_setup]

  def show
    render json: current_company.lite_sdk_settings
  end

  def update
    setting_params = params.require(:lite_sdk_setting).permit(
        :enable_setup,
        :enable_in_app,
        :enable_pro_setup
        )

    lite_sdk_setting = current_company.lite_sdk_settings
    lite_sdk_setting.update(setting_params)
    current_company.save!

    render json: current_company.lite_sdk_settings
  end

end
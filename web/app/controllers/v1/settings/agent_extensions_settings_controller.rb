# frozen_string_literal: true

class V1::Settings::AgentExtensionsSettingsController < ApplicationController
  before_action :authorize_extensions_settings

  # GET /v1/settings/agent_extensions
  def show
    render_agent_extensions_setting
  end

  # PUT /v1/settings/agent_extensions
  # PATCH /v1/settings/agent_extensions
  def update
    SettingsService::ExtensionsSettingsService.new.update_agent_extension_setting(permitted_params)

    render_agent_extensions_setting
  end

  private

  def render_agent_extensions_setting
    render json: AgentExtensionsSettingRepresenter.new(current_company.extensions_settings)
  end

  def authorize_extensions_settings
    authorize [:settings, :agent_extensions_settings]
  end

  def permitted_params
    params.permit(Settings::AgentExtensionsSettingsPolicy.permitted_attributes).to_h
  end
end

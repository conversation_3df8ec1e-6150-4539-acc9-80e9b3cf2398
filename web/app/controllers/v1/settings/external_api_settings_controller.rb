class V1::Settings::ExternalApiSettingsController < V1::Settings::BaseController
  wrap_parameters :settings, include: [:request_url, :username, :password, :auth_method, :custom_headers,
                                       :timeout, :phone_number_format, :request_parameter, :request_method, :crm_comment_keyvalue, :crm_comment_json,
                                       :data_parameters_enabled, :data_records, :data_parameters]
  before_action :request_validation, only: :update

  def show
    render_setting
  end

  def update
    settings_params = params.require(:settings).permit(permitted_param_names)

    if settings_params[:auth_method] == 'oauth'
      unless current_company.crm_type == :salesforce
      raise ServiceException, 'API DAP must use basic authentication if Salesforce is not the CRM being used.'
    end

    end

    if settings_params[:username].blank? || settings_params[:password].blank?
      settings_params[:username] = settings.username
      settings_params[:password] = settings.password
    end

    settings_params[:custom_headers] = settings_params[:custom_headers].blank? ? {} : JSON.parse(settings_params[:custom_headers])

    settings_params[:data_parameters] = extract_data_parameters_attrs!(settings_params)

    current_company.external_api_settings = settings_params
    current_company.save!

    status_code = 200
    if settings_params[:auth_method] == 'oauth'
      # If there is no oauth token exists in UJET system, we need to display warning in admin portal.
      # Returning 202 instead of 200.
      # Frontend will handle this status
      status_code = 202 unless CRM::Credential.does_exist?
    end
    render_setting status_code: status_code
  end

  private

  def request_validation
    settings_params = params.require(:settings).permit(permitted_param_names)

    unless settings_params[:request_url].present? && NetworkingService.url_resolves_to_non_prohibited_addresses?(settings_params[:request_url])
      render json: { message: 'Request URL must resolve to routable IP address.' }, status: :bad_request
    end

    duplicate_parameters = settings_params[:data_parameters].filter do |parameter|
      parameter[:field] == settings_params[:request_parameter]
    end

    if duplicate_parameters.present?
      render json: { message: 'Parameter field name already exists.' },
             status: :bad_request
    end
  end

  def authorize_setting
    authorize [:settings, :external_api_setting]
  end

  def settings
    current_company.external_api_settings
  end

  def render_setting(status_code: 200)
    render json: Settings::ExternalApiSettingRepresenter.new(settings), status: status_code
  end

  def permitted_param_names
    [:request_url, :username, :password, :auth_method, :timeout, :phone_number_format, :crm_comment_keyvalue,
     :crm_comment_json, :custom_headers, :request_parameter, :request_method,
     :data_parameters_enabled,
     { data_records: {},
       data_parameters: [:field, :type, :value, :source, :source_field, :required] }]
  end

  def extract_data_parameters_attrs!(attrs)
    {
      enabled: attrs.delete(:data_parameters_enabled),
      parameters: attrs.delete(:data_parameters),
      records: attrs.delete(:data_records)
    }.tap(&:compact!)
  end
end

# frozen_string_literal: true

# rubocop:disable Style/ClassAndModuleChildren
class V1::Settings::Developer::ApiUsersController < ApplicationController
  # rubocop:enable Style/ClassAndModuleChildren
  wrap_parameters :api_user, include: [:name, :deleted_at, :id]

  before_action :require_api_user, only: [:show, :update, :destroy, :regenerate_token]
  before_action :authorize_api_user

  # GET /v1/settings/developer/api_users
  def index
    render json: ::Settings::Developer::ApiUserRepresenter.for_collection.prepare(::ApiUser.with_deleted.all)
  end

  # GET /v1/settings/developer/api_users/:api_user_id
  def show
    params.require(:api_user_id)
    render json: ::Settings::Developer::ApiUserRepresenter.prepare(@api_user)
  end

  # PATCH /v1/settings/developer/api_users/:api_user_id
  def update
    params.require(:api_user_id)
    permitted_params = params.permit(:enabled, :name)
    @api_user.update!(permitted_params)
    render json: ::Settings::Developer::ApiUserRepresenter.prepare(@api_user)
  end

  # PATCH /v1/settings/developer/api_users/:api_user_id/regenerate_token
  def regenerate_token
    params.permit(:api_user_id)
    @api_user.regenerate_token
    render json: ::Settings::Developer::ApiUserRepresenter.prepare(@api_user)
  end

  # POST /v1/settings/developer/api_users
  def create
    params.require(:name)
    permitted_params = params.permit(:name)
    @api_user = ApiUser.create!(permitted_params)
    render json: ::Settings::Developer::ApiUserRepresenter.prepare(@api_user)
  end

  protected

  def policy_class
    ::Settings::Developer::ApiUsersPolicy
  end

  private

  def require_api_user
    @api_user = ::ApiUser.with_deleted.find(params[:api_user_id])
  end

  def authorize_api_user
    authorize @api_user || ::ApiUser, policy_class: policy_class
  end
end

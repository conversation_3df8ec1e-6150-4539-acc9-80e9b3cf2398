# frozen_string_literal: true

module V1
  module Settings
    module DataTransfer
      class VersionHistoriesController < ApplicationController
        include ValidateExportImportEnabled
        include SettingVersionHistorySuccessHandling
        include SettingVersionHistoryErrorHandling

        before_action :find_version_history, only: [:update, :download, :destroy, :show, :rollback]
        before_action :authorize_ujet_settings_version_history

        def index
          version_histories = UjetSettingsVersionHistory.filter_by_displayable.limited_revision_sort.includes(:user)
                                                        .page(params[:page]).per(params[:per_page])
          add_pagination(version_histories)
          render_success_response({ version_histories: version_histories_representer(version_histories) })
        end

        def create
          exporter = ::Settings::DataTransfer::ExportService.new(
            current_user:,
            params: version_history_permitted_params,
            data: { status: :auto_saved }
          )
          exporter.add_queue_exporting

          render_success_response(version_history: UjetSettingsVersionHistoryRepresenter.new(exporter.version_history))
        end

        def restore
          service = ::Settings::DataTransfer::VersionHistoryService.new(current_user:, params:)
          service.call

          render_success_response({ version_history: UjetSettingsVersionHistoryRepresenter.new(service.version_history) })
        end

        def update
          @version_history.update!(permitted_params)
          render_success_response({ version_history: UjetSettingsVersionHistoryRepresenter.new(@version_history) })
        end

        def download
          file = @version_history.settings_data_file.file
          if file.exists?
            send_data file.read, filename: file.filename, type: file.content_type
          else
            render_error('Download Failed', :bad_request)
          end
        end

        def destroy
          if @version_history.destroy
            head :ok
          else
            render_error('Destroy Failed', :bad_request)
          end
        end

        def show
          render_success_response({ version_history: UjetSettingsVersionHistoryRepresenter.new(@version_history) })
        end

        def last_import
          version_history = UjetSettingsVersionHistory.not_exported.finished_or_failed_job_status.where(user_id: current_user.id).last
          render_success_response({ version_history: version_history ? UjetSettingsVersionHistoryRepresenter.new(version_history) : nil })
        end

        def rollback
          service = ::Settings::DataTransfer::ManagingQueueBackupService.new(@version_history)

          import_handler = ::Settings::DataTransfer::Imports::ImportHandler.new(
            params: {},
            current_user:,
            version_history: @version_history
          )

          setting_data = import_handler.send(:read_file_content)
          imported_company_settings = setting_data.dig(:data, :company_settings) || {}

          service.rollback(imported_company_settings)
          render_success_response({ version_history: UjetSettingsVersionHistoryRepresenter.new(service.version_history) })
        end

        private

        def authorize_ujet_settings_version_history
          authorize @version_history || UjetSettingsVersionHistory
        end

        def find_version_history
          @version_history = UjetSettingsVersionHistory.find(params[:id])
        end

        def version_histories_representer(collection)
          UjetSettingsVersionHistoryRepresenter.for_collection.prepare(collection)
        end

        def permitted_params
          params.require(:version_history).permit(:name)
        end

        def version_history_permitted_params
          params.require(:export).permit(default_export_filters).to_h
        end

        def default_export_filters = ::Settings::DataTransfer::Constants::SETTING_FILTERS
      end
    end
  end
end

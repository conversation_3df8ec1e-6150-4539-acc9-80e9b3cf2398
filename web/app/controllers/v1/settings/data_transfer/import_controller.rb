# frozen_string_literal: true

module V1
  module Settings
    module DataTransfer
      class ImportController < ApplicationController
        include ValidateExportImportEnabled
        include SettingVersionHistorySuccessHandling
        include SettingVersionHistoryErrorHandling

        before_action :authorize_import_setting

        rescue_from ::Settings::DataTransfer::ImportService::ImportError, with: :handle_import_error

        # POST /v1/settings/data_transfer/import/upload
        def upload
          service = ::Settings::DataTransfer::ImportService.new(params:, current_user:)
          service.upload
          render json: service.response, status: 200
        end

        # POST /v1/settings/data_transfer/import
        def create
          init_import = ::Settings::DataTransfer::ImportService.new(
            current_user:,
            params: permitted_params.to_h
          )
          init_import.add_queue

          render_success_response(version_history: UjetSettingsVersionHistoryRepresenter.new(init_import.version_history))
        end

        private

        def policy_class = ::Settings::DataTransfer::ImportPolicy

        def authorize_import_setting = authorize(:import, policy_class:)

        def handle_import_error
          render_error('Import error', :unprocessable_entity)
        end

        def permitted_params
          params.require(:config).permit(
            :version_history_id, setting: [
              :chat_shortcuts, :contact_lists, { channels: default_channel_setting_filters }
            ]
          )
        end

        def default_channel_setting_filters = ::Settings::DataTransfer::Constants::CHANNEL_SETTING_FILTERS
      end
    end
  end
end

# frozen_string_literal: true

module V1
  module Settings
    module DataTransfer
      class ExportController < ApplicationController
        include ValidateExportImportEnabled
        include SettingVersionHistorySuccessHandling
        include SettingVersionHistoryErrorHandling

        before_action :authorize_export_setting

        # POST /v1/settings/data_transfer/export
        def create
          exporter = ::Settings::DataTransfer::ExportService.new(
            current_user:,
            params: permitted_params.to_h,
            data: { status: :exported }
          )
          exporter.add_queue_exporting

          render_success_response(version_history: UjetSettingsVersionHistoryRepresenter.new(exporter.version_history))
        end

        # GET /v1/settings/data_transfer/export_pref
        def export_pref
          export_settings = ::Settings::DataTransfer::ExportService.new(current_user:).export_settings
          render_success_response(export_settings)
        end

        private

        def policy_class = ::Settings::DataTransfer::ExportPolicy

        def authorize_export_setting = authorize(:export, policy_class:)

        def permitted_params = params.require(:export).permit(default_export_filters)

        def default_export_filters = ::Settings::DataTransfer::Constants::SETTING_FILTERS
      end
    end
  end
end

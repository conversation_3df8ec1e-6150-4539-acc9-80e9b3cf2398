# frozen_string_literal: true

class V1::Settings::CompanySettingsController < V1::Settings::BaseController
  before_action :authorize_setting
  wrap_parameters exclude: []

  def update
    company = current_company
    updated = CompanyService.bulk_update(company, permitted_params.to_h)

    # even if update succeeds, we want to let the user know about any keys in their update request that don't exist on
    # setting objects to give them opportunity to fix typos and resubmit
    response = { invalid_keys: invalid_keys }
    response[:errors] = company.errors.full_messages unless updated
    status = updated ? :ok : :bad_request

    render json: response, status: status
  end

  private

  def authorize_setting
    authorize [:settings, :company_setting]
  end

  def permitted_params
    @permitted_params ||=
      begin
        # current whitelist columns are settings and *_settings fields
        columns = Company.column_names.select { |key| key.ends_with?('_settings') }.map(&:to_sym)
        columns << :settings

        permitted = columns.map do |column|
          setting = current_company[column]

          # skip deprecated setting fields which still exist in db but their SettingObject class no longer exists
          next unless setting.is_a?(SettingObject)

          # recurse through setting object to allow properties that are on the setting class
          { column => recursive_permitted_attributes(setting.attributes) }
        end

        params.require(:company_setting).permit(*permitted.compact)
      end
  end

  def recursive_permitted_attributes(setting_obj)
    permitted = []

    setting_obj.each do |key, value|
      permitted << if value.is_a?(Hash)
                     { key => recursive_permitted_attributes(value) }
                   else
                     key
                   end
    end

    permitted
  end

  def invalid_keys
    everything = params[:company_setting].to_unsafe_h
    permitted = permitted_params.to_h

    recursive_invalid_keys(everything, permitted)
  end

  def recursive_invalid_keys(everything, permitted, path = '')
    keys = []

    everything.each do |key, value|
      if permitted.try(:key?, key)
        # if valid key has a hash value, recurse through next level of keys for validity
        keys.concat(recursive_invalid_keys(value, permitted[key], "#{path}#{key.to_s}.")) if value.is_a?(Hash)
      else
        keys << path + key.to_s
      end
    end

    keys
  end
end

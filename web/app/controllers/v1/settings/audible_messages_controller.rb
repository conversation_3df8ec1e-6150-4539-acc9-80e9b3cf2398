class V1::Settings::AudibleMessagesController < ApplicationController
  wrap_parameters :messages

  before_action :require_lang
  before_action :require_menu,
                only: [:index_queue, :index_inherited, :update_queue, :index_queue_redirection_group_message,
                       :update_queue_redirection_group_message]
  before_action :require_redirection_group,
                only: [:index_queue_redirection_group_message, :index_global_redirection_group_message,
                       :update_queue_redirection_group_message, :update_global_redirection_group_message]
  before_action :require_native_campaign, only: [:index_native_campaign, :update_native_campaign]
  before_action :authorize_message

  def index_global
    section = params.permit(:section).fetch(:section, 'default')
    supports_rtf = ActiveModel::Type::Boolean.new.cast(params.permit(:supportsRTF)[:supportsRTF])
    finder = Audible::MessageFinder.new(menu_id: nil, client_app_id: client_app_id, lang: @lang)
    @messages = finder.all_by_section(section)

    message_filter_rtf unless supports_rtf
    @messages.compact!

    render_messages
  end

  def update_global
    updates = message_updates
    updater = Audible::MessageUpdater.new(menu_id: nil, client_app_id: client_app_id, lang: @lang)
    @messages = updater.update(updates)
    @messages.compact!

    render_messages

  rescue Audible::MessageUpdater::MessageNotFound => e
    render json: {message: e.message}, status: :bad_request
  rescue Audible::MessageUpdater::MessageInvalid => e
    render json: {message: e.message}, status: :bad_request
  end

  # GET /v1/audible_messages/queue/:lang/:menu_id
  def index_queue
    section = params.permit(:section).fetch(:section, 'default')

    finder = Audible::MessageFinder.new(menu_id: @menu.id, lang: @lang, client_app_id: client_app_id)
    @messages = finder.all_by_section(section)
    @messages.compact!

    body_content = @messages['email.auto_response.message']&.text
    return render_messages unless body_content.present?

    html_content = Nokogiri::HTML(body_content)
    data_id_list = html_content.css('img[data-id]').map { |img| img['data-id'] }
    return render_messages unless data_id_list.present?

    email_channel_setting_id = Menu::EmailChannelSetting.where(menu_id: @menu.id, lang: @lang).first&.id
    return render json: { message: "Email Channel Setting not found for menu_id: #{@menu.id}, lang: #{@lang}" }, status: :not_found if email_channel_setting_id.nil?

    email_support_common_service = EmailAdapter::EmailSupportCommonService.new(email_channel_setting_id:)
    @messages['email.auto_response.message']&.text = email_support_common_service.assemble_auto_resonse_email_body_message(body_content)

    render_messages
  end

  # PATCH /v1/audible_messages/queue/:lang/:menu_id
  def update_queue
    updates = message_updates
    updater = Audible::MessageUpdater.new(menu_id: @menu.id, lang: @lang, client_app_id: client_app_id)
    @messages = updater.update(updates)
    @messages.compact! unless params[:queue_audible_message] == 'true'

    render_messages

  rescue Audible::MessageUpdater::MessageNotFound => e
    render json: {message: e.message}, status: :bad_request
  rescue Audible::MessageUpdater::MessageInvalid => e
    render json: {message: e.message}, status: :bad_request
  end

  # GET /v1/audible_messages/campaigns/:lang/campaign/:campaign_id
  def index_native_campaign
    section = :campaign

    finder = Audible::MessageFinder.new(menu_id: @native_campaign.id, lang: @lang, client_app_id: client_app_id)
    @messages = finder.all_by_section(section)
    @messages.compact!

    render_messages
  end

  # PATCH /v1/audible_messages/campaigns/:lang/campaign/:campaign_id
  def update_native_campaign
    updates = message_updates
    updater = Audible::MessageUpdater.new(menu_id: @native_campaign.id, lang: @lang, client_app_id: client_app_id)
    @messages = updater.update(updates)
    @messages.compact!

    render_messages

  rescue Audible::MessageUpdater::MessageNotFound => e
    render json: {message: e.message}, status: :bad_request
  rescue Audible::MessageUpdater::MessageInvalid => e
    render json: {message: e.message}, status: :bad_request
  end

  # GET /v1/audible_messages/campaigns/:en/global
  def index_native_campaign_global
    # FIXME We will need to migrate all operation_management_setting section to
    # a more meaningful section
    # TB-552: https://ujetcs.atlassian.net/browse/TB-552
    section = 'operation_management_setting'
    finder = Audible::MessageFinder.new(menu_id: nil, client_app_id: client_app_id, lang: @lang)
    @messages = finder.all_by_section(section)
    @messages.compact!

    render_messages
  end

  # PATCH /v1/audible_messages/campaigns/:en/global
  def update_native_campaign_global
    # FIXME We will need to migrate all operation_management_setting section to
    # a more meaningful section
    # TB-552: https://ujetcs.atlassian.net/browse/TB-552

    # Reject if the messages doesn't exclusively include "native_campaign.abandoned_call_message"
    messages_param = params[:messages]

    is_settings_valid = messages_param.key?('native_campaign.abandoned_call_message') &&
                        messages_param.keys.count == 1 # ActionController::Parameters does not have #count

    unless is_settings_valid
      return render json: { message: "Parameter validation failed: #{messages_param.keys}" },
                    status: :bad_request
    end

    update_global
  end

  # GET /v1/settings/audible_messages/inherited/:lang/:menu_id
  def index_inherited
    @messages = Audible::MessageFinder.find_all_inherited_audibles_for(
      menu_id: @menu.id, lang: @lang, skip_current_menu: params[:skip_current_menu]
    )
    @messages.compact!

    include_menu_names = Audible::MessageSetting.new(lang: @lang).include_menu_names(messages: @messages)
    render json: Audible::MessagesRepresenter.new(@messages).to_json(
      user_options: {
        include_menu_names:
      }
    )
  end

  # GET /v1/audible_messages/queue/:lang/:menu_id/redirection_group/:redirection_id
  def index_queue_redirection_group_message
    get_redirection_group_message(menu_id: @menu.id)
  end

  # GET v1/audible_messages/global/:lang/redirection_group/:redirection_group_id
  def index_global_redirection_group_message
    get_redirection_group_message(menu_id: nil)
  end

  # PATCH /v1/audible_messages/queue/:lang/:menu_id/redirection_group/:redirection_id
  def update_queue_redirection_group_message
    update_redirection_group_message(menu_id: @menu.id)
  end

  # PATCH /v1/audible_messages/global/:lang/redirection_group/:redirection_group_id
  def update_global_redirection_group_message
    update_redirection_group_message(menu_id: nil)
  end

  private

  def update_redirection_group_message(menu_id:)
    updates = message_updates
    updater = Audible::MessageUpdater.new(menu_id: menu_id, client_app_id: client_app_id, lang: @lang, redirection_group_id: @redirection_group.id)
    @messages = updater.update(updates)
    @messages.compact!
    render_messages

  rescue Audible::MessageUpdater::MessageNotFound, Audible::MessageUpdater::MessageInvalid => e
    render json: { message: e.message }, status: :bad_request
  end

  def get_redirection_group_message(menu_id:)
    section = params.permit(:section).fetch(:section, 'default')
    finder = Audible::MessageFinder.new(menu_id: menu_id, lang: @lang, redirection_group_id: @redirection_group.id, client_app_id: client_app_id)
    @messages = finder.all_by_section(section)
    @messages.compact!

    render_messages
  end

  def require_lang
    @lang = params.permit(:lang).require(:lang)
    @language = Company.current.languages.find_by!(lang: @lang)
  end

  def require_menu
    @menu_id = params.permit(:menu_id).require(:menu_id)
    @menu = Menu::Base.find(@menu_id)
  end

  def require_redirection_group
    redirection_group_id = params.permit(:redirection_group_id).require(:redirection_group_id)
    @redirection_group = Menu::QueueRedirectionGroup.find(redirection_group_id)
  end

  def require_native_campaign
    @native_campaign_id = params.permit(:native_campaign_id).require(:native_campaign_id)
    @native_campaign = ::NativePowerDial::Campaign.find @native_campaign_id
  end

  def message_updates
    params.fetch(:messages, {}).tap do |updates|
      updates.each do |key, attributes|
        updates[key] = attributes.permit(
          :type, :text, :audio, :audio_url, :remote_audio_url, :remove_audio, :restore_default, :redirection_group_id
        )
      end
    end.to_unsafe_h
  end

  def message_filter_rtf
    desired_prefixes = ['mobile_sdk.', 'chat.']
    @messages.each do |key, value|
      desired_prefixes.any? { |prefix| key.start_with?(prefix) } &&
        value.text = strip_markdown(value.text)
    end

    @messages
  end

  def strip_markdown(text)
    renderer = Redcarpet::Markdown.new(ChatService::Markdown::TextRenderer)
    renderer.render(text).gsub(/(:\w+:)|[\p{Emoji}]/, '')
  end

  def client_app_id
    params[:client_app_id].presence
  end

  def authorize_message
    authorize Audible::Message
  end

  def render_messages
    render json: Audible::MessagesRepresenter.new(@messages)
  end
end

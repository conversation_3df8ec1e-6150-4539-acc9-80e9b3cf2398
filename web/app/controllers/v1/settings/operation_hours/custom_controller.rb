class V1::Settings::OperationHours::CustomController < ApplicationController
  wrap_parameters :operation_hour, include: [
    :name, :time_zone, :all_day, :hours_of_operation, :holidays, :custom_holidays, :lock_version
  ]

  before_action :require_operation_hour, only: [:show, :update, :destroy]
  before_action :authorize_operation_hour

  def index
    @operation_hours = ::OperationHours::OperationHour.custom.all
    render json: ::OperationHours::OperationHourRepresenter.for_collection.prepare(@operation_hours)
  end

  def create
    @operation_hour = ::OperationHours::OperationHour.create!(create_params)

    render_operation_hour
  end

  def show
    render_operation_hour
  end

  def update
    @operation_hour.update!(update_params)

    render_operation_hour

  rescue ActiveRecord::StaleObjectError
    @operation_hour.reload
    message = "Another user has updated that record since you accessed."
    representer = ::OperationHours::OperationHourRepresenter.new(@operation_hour)
    render json: {message: message, record: representer}, status: :conflict
  end

  def destroy
    @operation_hour.destroy!
    render_operation_hour
  end


  private

  def require_operation_hour
    @operation_hour = ::OperationHours::OperationHour.custom.find(params[:id])
  end

  def authorize_operation_hour
    authorize @operation_hour || ::OperationHours::OperationHour
  end

  def render_operation_hour
    render json: ::OperationHours::OperationHourRepresenter.new(@operation_hour)
  end

  def create_params
    update_params.except(:lock_version)
  end

  HOURS_OF_OPERATION_PARAMS = [:sun, :mon, :tue, :wed, :thu, :fri, :sat].each_with_object({}) do |wday, hash|
    hash[wday] = [:operating, :start_time, :end_time]
  end

  def update_params
    operation_hour_params = params.require(:operation_hour)

    # to allow making holidays empty, nil should be replaced with an empty array before applying
    # strong parameter validation.
    if operation_hour_params.has_key?(:holidays)
      operation_hour_params[:holidays] ||= []
    end

    if operation_hour_params.has_key?(:custom_holidays)
      operation_hour_params[:custom_holidays] ||= []
    end

    operation_hour_params.permit(:name, :time_zone, :all_day, :lock_version,
      hours_of_operation: HOURS_OF_OPERATION_PARAMS, holidays: [], custom_holidays: [])
  end
end

class V1::Settings::OperationHours::AssignmentsController < ApplicationController
  wrap_parameters false

  before_action :require_operation_hour, only: [:show, :update]
  before_action :authorize_operation_hour

  # backward compatiblity with 1.64 client, can be removed later
  before_action :ensure_assignment_channels, only: [:update]

  # render all assignments of all custom operation hours
  def show_all
    @assignments = ::OperationHours::OperationHourAssignment.where.not(operation_hour_id: nil)
                                                            .include_team.include_user.all
    render_assignments
  end

  # render assignments of an operation hour
  def show
    @assignments = @operation_hour.assignments
    render_assignments
  end

  # update assignments of an operation hour
  def update
    service = ::OperationHours::OperationHourAssignmentService.new
    if service.replace_assignments(operation_hour: @operation_hour,
                                   new_assignments_params: assignments_params.map(&:to_h))
      @assignments = @operation_hour.assignments.reload
      render_assignments
    else
      # assignment parameters validation failed
      error_message = service.errors.first
      render json: {message: error_message}, status: :bad_request
    end
  end


  private

  def require_operation_hour
    @operation_hour = ::OperationHours::OperationHour.custom.find(params[:operation_hour_id])
  end

  def authorize_operation_hour
    authorize @operation_hour || ::OperationHours::OperationHour
  end

  def render_assignments
    render json: ::OperationHours::OperationHourAssignmentRepresenter.for_collection.prepare(@assignments)
  end

  def assignments_params
    @assignments_params ||= begin
                              permitted_params = params.permit(_json: [:menu_type, :lang, :menu_id, :channel])
                              permitted_params[:_json] || []
                            end
  end

  def ensure_assignment_channels
    original_params = assignments_params
    return unless original_params.any? { |assignment| assignment[:channel].blank? }

    @assignments_params = original_params.each_with_object([]) do |assignment, array|
      next array << assignment if assignment[:channel].present?

      Menu.supporting_channel_types(assignment[:menu_type]).each do |channel|
        cloned = assignment.clone
        cloned[:channel] = channel.to_s
        array << cloned
      end

      array
    end
  end
end

class V1::Settings::FormSettingsController < V1::Settings::BaseController
  before_action :set_form_setting
  before_action :authorize_setting

  # GET /v1/settings/form
  def show
    render_setting
  end

  # PATCH /v1/settings/form
  def update
    @form_setting.update(form_setting_params)
    current_company.save!

    render_setting
  end

  private

  def set_form_setting
    @form_setting = current_company.form_settings
  end

  def render_setting
    render json: Settings::FormSettingRepresenter.new(@form_setting)
  end

  def form_setting_params
    params.permit(
      :enabled,
      { response_destination: [:post_in_crm, :post_in_metadata] },
      { browser_forms: [:shared_secret] }
    )
  end

  def authorize_setting
    authorize @form_setting, policy_class: FormPolicy
  end
end

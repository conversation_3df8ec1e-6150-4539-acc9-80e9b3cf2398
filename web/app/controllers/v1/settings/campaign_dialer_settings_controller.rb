# frozen_string_literal: true

class V1::Settings::CampaignDialerSettingsController < ApplicationController
  wrap_parameters :campaign_dialer_settings
  before_action :authorize_campaign_dialer_settings
  before_action :validate_campaign_dialer_settings_parameters, only: [:update]

  def show
    render json: campaign_dialer_settings
  end

  def update_dialer_settings
    update_dialer_setting_fields!
    render json: campaign_dialer_settings
  end

  def update
    update_campaign_mode_fields!
    render json: campaign_dialer_settings
  end

  def general_settings
    render json: {
      live_caller_detection_available: current_company.call_settings.primary_voip_provider == 'twilio'
    }
  end

  private

  def authorize_campaign_dialer_settings
    authorize :campaign_dialer_settings
  end

  def campaign_dialer_settings
    current_company.settings.campaign_dialer
  end

  def update_dialer_setting_fields!
    safe_company_update do |company|
      company.settings.campaign_dialer.byoc = permitted_params[:byoc]
      company.settings.campaign_dialer.brand_name = permitted_params[:brand_name]
    end
  end

  def update_campaign_mode_fields!
    safe_company_update do |company|
      existing_settings = company.settings.campaign_dialer.attributes
      updated_settings = permitted_params.except(:byoc, :brand_name)
      merged_settings = existing_settings.merge(updated_settings)
      company.settings.campaign_dialer = Settings::CampaignDialerSetting.new(merged_settings)
    end
  end

  def validate_campaign_dialer_settings_parameters
    validate_dialing_mode_parameters
    validate_contact_providers_parameters
  end

  def validate_dialing_mode_parameters
    dialing_mode_params = permitted_params.dig(:dialing_mode)
    return if dialing_mode_params.blank?

    overdial_adjustment_multiplier = dialing_mode_params.dig(:overdial_adjustment_multiplier)&.to_i
    if overdial_adjustment_multiplier.present? &&
       (overdial_adjustment_multiplier < 1 || overdial_adjustment_multiplier > 10)
      raise ServiceException, 'Overdial adjustment multiplier should be between 1 to 10'
    end

    # Validate live caller settings
    live_caller_settings_params = dialing_mode_params.dig(:live_caller_detection_settings)
    return if live_caller_settings_params.blank?

    detection_timeout = live_caller_settings_params.dig(:detection_timeout)&.to_i
    if detection_timeout.present? && (detection_timeout < 3 || detection_timeout > 59)
      raise ServiceException, 'Detection timeout should be between 3 to 59'
    end

    speech_threshold = live_caller_settings_params.dig(:speech_threshold)&.to_i
    if speech_threshold.present? && (speech_threshold < 1000 || speech_threshold > 6000)
      raise ServiceException, 'Speech threshold should be between 1000 to 6000'
    end


    speech_end_threshold = live_caller_settings_params.dig(:speech_end_threshold)&.to_i
    if speech_end_threshold.present? && (speech_end_threshold < 500 || speech_end_threshold > 5000)
      raise ServiceException, 'Speech end threshold should be between 500 to 5000'
    end

    silence_timeout = live_caller_settings_params.dig(:silence_timeout)&.to_i
    if silence_timeout.present? && (silence_timeout < 2000 || silence_timeout > 10000)
      raise ServiceException, 'Silence timeout should be between 2000 to 10000'
    end
  end

  def validate_contact_providers_parameters
    # Validate Authority configurations
    authority_params = permitted_params.dig(:contact_providers, :authority)
    return if authority_params.blank?

    # URL checker
    url_regexp = /^(http|https):\/\/[a-z0-9]+([\-\.]{1}[a-z0-9]+)*\.[a-z]{2,5}(:[0-9]{1,5})?(\/.*)?$/ix

    authority_base_url = authority_params.dig(:base_url)
    if authority_base_url.present? && (authority_base_url =~ url_regexp).blank?
      raise ServiceException, 'contact_providers.authority.base_url is invalid'
    end

    authority_oauth_url = authority_params.dig(:oauth_url)
    if authority_oauth_url.present? && (authority_oauth_url =~ url_regexp).blank?
      raise ServiceException, 'contact_providers.authority.oauth_url is invalid'
    end
  end

  def permitted_params
    params.require(:campaign_dialer_settings).permit(CampaignDialerSettingsPolicy.permitted_attributes)
  end
end


# frozen_string_literal: true

module V1
  module Settings
    class DefaultLayoutSettingsController < ApplicationController
      before_action :require_menu
      before_action :authorize_agent_desktop_default_layouts
      before_action :require_menu_settings, only: [:show, :reset]

      def show
        default_layout_settings = AgentDesktopLayouts::QueueDefaultLayoutSettingService.default_layout_setting_by_specific_queue(
          menu_id:  @menu_id, lang: @lang
        )

        render_default_layout_settings(default_layout_settings)
      end

      def update
        default_layout_settings = AgentDesktopLayouts::QueueDefaultLayoutSettingService.update_or_create_default_layout_settings(shared_params)

        render_default_layout_settings(default_layout_settings)
      end

      def reset
        default_layout_settings = AgentDesktopLayouts::QueueDefaultLayoutSettingService.reset_default_layout_settings(
          menu_id:  @menu_id, lang: @lang
        )

        render_default_layout_settings(default_layout_settings)
      end

      private

      def menu_setting_params
        params.permit(:menu_id, :lang)
      end

      def shared_params
        params.permit(:menu_id, :lang, configuration: [:call, :chat, :voicemail, :email])
      end

      def render_default_layout_settings(default_layout_settings)
        render json: AgentDesktopLayouts::QueueDefaultLayoutSettingRepresenter.new(OpenStruct.new(default_layout_settings))
      end

      def require_menu
        ::Menu::Base.find(params[:menu_id])
      end

      def require_menu_settings
        @menu_id = menu_setting_params[:menu_id]
        @lang = menu_setting_params[:lang]
      end

      def authorize_agent_desktop_default_layouts
        # only the admin is allowed to use API
        authorize :agent_desktop_default_layout
      end
    end
  end
end

class V1::Settings::SfdcUserStatusInheritanceSettingsController < V1::Settings::BaseController
  wrap_parameters :sfdc_user_status_inheritance,
    include: [:enabled, :apply_at_login, :next_user_status_option, :default_user_status_name]

  def show
    render_settings
  end

  def update
    update_params = permitted_params.clone
    if update_params[:default_user_status_name].present?
      default_user_status_name = update_params.delete(:default_user_status_name)
      default_user_status_id = UserStatus.get_by_name(default_user_status_name)&.id
      update_params[:default_user_status_id] = default_user_status_id
    end

    settings.update(update_params)

    current_company.save!

    render_settings
  end


  private

  def authorize_setting
    authorize [:settings, :sfdc_user_status_inheritance_setting]
  end

  def render_settings
    render json: {
      enabled: settings.enabled,
      apply_at_login: settings.apply_at_login,
      next_user_status_option: settings.next_user_status_option,
      default_user_status_name: UserStatus.get(settings.default_user_status_id)&.name
    }
  end

  def settings
    @settings ||= current_company.crm_settings.sfdc_user_status_inheritance
  end

  def permitted_params
    params.require(:sfdc_user_status_inheritance)
      .permit(:enabled, :apply_at_login, :next_user_status_option, :default_user_status_name)
  end
end

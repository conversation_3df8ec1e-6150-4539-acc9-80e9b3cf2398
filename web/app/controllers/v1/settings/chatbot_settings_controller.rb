# frozen_string_literal: true

class V1::Settings::ChatbotSettingsController < V1::Settings::BaseController
  wrap_parameters :chatbot_setting, include: %i[use_chatbot]

  # note that this only controls enabling chatbot, the other chatbot-related settings are controlled by
  # V1::Settings::Operation::ChatbotSettingsController

  def show
    render_setting
  end

  def update
    Chatbot::SettingsUpdater.new(current_company, permitted_chatbot_setting_params, current_user).call

    render_setting
  end

  private

  def authorize_setting
    authorize current_company, policy_class: Chatbot::SettingPolicy
  end

  def render_setting
    render json: {
      chatbot_setting: {
        use_chatbot: current_company.settings.use_chatbot,
        use_voicebot: current_company.settings.use_voicebot
      }
    }
  end

  def permitted_chatbot_setting_params
    @chatbot_setting_params ||= params.require(:chatbot_setting).permit(:use_chatbot, :use_voicebot)
  end
end

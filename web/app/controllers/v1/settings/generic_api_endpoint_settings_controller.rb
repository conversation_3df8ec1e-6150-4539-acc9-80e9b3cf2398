class V1::Settings::GenericApiEndpointSettingsController < V1::Settings::BaseController
  before_action :require_endpoint
  before_action :check_crm_type

  def show
    render_setting
  end

  def update
    permitted_params = [:request_url,
                        :request_method,
                        :request_data_format,
                        :request_data_container_name,
                        :request_data_container_type,
                        :request_parameters,
                        :custom_id_location,
                        :response_data_location]
    permitted_params |=
      case @endpoint
      when 'contact_upload_file_endpoint', 'case_upload_file_endpoint'
        [:upload_type,
         :file_type_call_recordings,
         :file_type_call_transcripts,
         :file_type_chat_transcripts,
         :file_type_voicemails,
         :file_type_photos,
         :file_type_videos,
         :file_type_documents,
         :file_type_audios,
         :file_type_session_metadata_file,
         :file_type_cobrowse_history,
         :file_post_comment,
         :file_download_type,
         :file_download_link_location,
         :file_download_id_location,
         :file_download_build_url
        ]
      when 'contact_comment_endpoint', 'case_comment_endpoint'
        [:comment_use_html]
      when 'contact_metadata_endpoint', 'case_metadata_endpoint'
        [:response_data_parser]
      else
        []
      end
    data = params.permit(permitted_params)
    current_company.crm_settings.custom_crm.update(@endpoint => data)
    current_company.save
    current_company.update_crm_settings
    render_setting
  end

  def test_endpoint
    data = {
      request_url: params[:request_url],
      request_method: params[:request_method],
      request_parameters: params[:request_parameters],
      endpoint: params[:endpoint],
      upload_type: params[:upload_type],
      file_type: params[:file_type],
      request_data_format: params[:request_data_format],
      request_data_container_name: params[:request_data_container_name],
      request_data_container_type: params[:request_data_container_type]
    }
    render json: ::CRM::Client::Custom.test_endpoint(params: data)
  end

  private

  def authorize_setting
    authorize :generic_api_endpoint_setting
  end

  def render_setting
    output = {}
    output[@endpoint] = current_company.crm_settings.custom_crm.send(@endpoint)
    render json: output
  end

  def require_endpoint
    @endpoint = params.permit(:endpoint).require(:endpoint)
    Settings::CrmSetting.validate_generic_api_endpoint_name!(endpoint: @endpoint)
  end

  def check_crm_type
    crm_type = current_company.crm_type
    raise ServiceException, "CRM Type is not custom, it is instead #{crm_type}" unless crm_type == :custom
  end
end

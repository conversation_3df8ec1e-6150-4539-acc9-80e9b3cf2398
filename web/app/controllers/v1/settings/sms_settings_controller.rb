class V1::Settings::SmsSettingsController < V1::Settings::BaseController
  wrap_parameters :sms_setting, include: Settings::SmsSetting.attribute_names

  def show
    render_setting
  end

  def update
    setting_params = params.require(:sms_setting).permit(self.class.permit_filters)
    sms_setting = current_company.sms_settings
    channel_disabled = setting_params.include?(:enabled) && !setting_params[:enabled]
    sms_setting.update(setting_params)
    current_company.save!
    if channel_disabled
      SystemMailer.channel_deactivate_notification("SMS", current_user).deliver_later
    end
    render_setting
  end

  def activate
    phone_number = params.require(:phone_number)
    messaging_provider = Messaging::Provider::Factory.create_provider(number: phone_number, comm: nil)
    messaging_provider.activate_message_service(phone_number: phone_number, usage: :default)

    render_setting
  end

  def activate_chat
    phone_number = params.require(:phone_number)
    messaging_provider = Messaging::Provider::Factory.create_provider(number: phone_number, comm: nil)
    messaging_provider.activate_message_service(phone_number: phone_number, usage: :sms_chat)

    render_setting
  end

  private

  def render_setting
    sms_setting = current_company.sms_settings
    render json: sms_setting.attributes
  end

  def authorize_setting
    authorize %i[settings sms_setting]
  end

  def self.permit_filters
    @permit_filters ||= setting_object_permit_filters(Settings::SmsSetting, except: [:service_sid])
  end
end

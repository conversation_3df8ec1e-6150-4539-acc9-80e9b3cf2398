class V1::Settings::IvrSettingsController < V1::Settings::BaseController
  wrap_parameters :ivr_setting, include: [
    :enabled,
    :deflection,
    :option_zero,
    :custom_callback,
    :no_user_input,
    :csat
  ]

  def show
    render_setting
  end

  def update
    setting_params = params.require(:ivr_setting).permit(*permitted_attributes)
    safe_company_update do |company|
      SettingsService::IvrSettingsUpdater.new(company).update(setting_params)
    end

    render_setting
  end

  private

  def permitted_attributes
    [
      :enabled,
      {
        deflection: {
          presession: [:enabled],
          after_hours: after_hours_deflection_attributes,
          over_capacity: over_capacity_deflection_attributes,
          conditional_over_capacity: [
            :enabled,
            :type,
            {
              ewt_under: conditional_over_capacity_deflection_attributes.push(:min),
              ewt_between: conditional_over_capacity_deflection_attributes.push(:min, :max),
              ewt_over: conditional_over_capacity_deflection_attributes.push(:max),
              ewt_exceeds_hoop: conditional_over_capacity_deflection_attributes,
              weekdays: [
                :configured,
                {
                  deflections: conditional_over_capacity_deflection_attributes.push(time_range: [:start_time, :end_time])
                }
              ],
              weekends: [
                :configured,
                {
                  deflections: conditional_over_capacity_deflection_attributes.push(time_range: [:start_time, :end_time])
                }
              ]
            }
          ]
        },
        option_zero: [:action, :queue_level_enabled, to_queues: [Language.all_codes_to_sym]],
        custom_callback: [:enabled, :max_retry_limit],
        no_user_input: [:play_menu_count, :option, menu_ids: [Language.all_codes_to_sym]],
        csat: [:enabled, :precall_noti_enabled]
      }
    ]
  end

  def after_hours_deflection_attributes
    [
      :phone,
      :voicemail,
      :message_only,
      :phone_number,
      {
        queue: deflection_queue_attributes
      },
      :disable_queue_greeting,
      :option,
      contact: deflection_contact_attributes,
      sip: deflection_sip_attributes
    ]
  end

  def over_capacity_deflection_attributes
    [
      :enabled,
      :phone,
      :voicemail,
      :limited_voicemail_scope,
      :limited_voicemail,
      :limited_voicemail_per_queue,
      :callback,
      :limited_callback_scope,
      :limited_callback,
      :limited_callback_per_queue,
      :wait,
      :phone_number,
      {
        queue: deflection_queue_attributes
      },
      :option,
      contact: deflection_contact_attributes,
      sip: deflection_sip_attributes
    ]
  end

  def conditional_over_capacity_deflection_attributes
    [
      :configured,
      :enabled,
      :phone,
      :voicemail,
      :limited_voicemail,
      :callback,
      :limited_callback,
      :wait,
      :phone_number,
      {
        queue: deflection_queue_attributes
      },
      :option,
      contact: deflection_contact_attributes,
      sip: deflection_sip_attributes
    ]
  end

  def deflection_queue_attributes
    [:enabled, :menu_id, :lang, :twenty_four_hours]
  end

  def deflection_sip_attributes
    [
      :enabled,
      :uri,
      :use_refer,
      :data_parameters_enabled,
      :data_parameters,
      :sip_option,
      :contact_id,
      :address_id,
      {
        data_records: [
          :in_metadata,
          :in_crm_record
        ]
      }
    ]
  end

  def deflection_contact_attributes
    [
      :contact_id,
      :contact_list_id,
      :contact_name
    ]
  end

  def render_setting
    ivr_settings = current_company.ivr_settings
    render json: Settings::IvrSettingRepresenter.new(ivr_settings)
  end
end

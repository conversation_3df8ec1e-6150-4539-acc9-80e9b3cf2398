class V1::Settings::LoginPolicySettingsController < V1::Settings::BaseController
  wrap_parameters :settings, include: [:policy, :domain, :sso_enabled, :password_auth_enabled, :client_id,
                                       :saml_idp_issuer, :saml_idp_login_url, :saml_idp_email_mapping, :saml_idp_cert,
                                       :saml_idp_logout_url, :saml_authn_context]

  def show
    render_setting
  end

  def update
    settings_params = if settings.saml_forced
                        # SAML is forced, only allow cert to be updated
                        params.require(:settings).permit([:saml_idp_cert])
                      else
                        params.require(:settings).permit(permitted_param_names)
                      end

    current_company.login_policy_settings = settings_params
    current_company.save!

    render_setting
  end

  private

  def authorize_setting
    authorize [:settings, :login_policy_setting]
  end

  def settings
    current_company.login_policy_settings
  end

  def render_setting
    render json: settings
  end

  def permitted_param_names
    [:policy, :domain, :client_id, :sso_enabled, :password_auth_enabled,
     :saml_idp_issuer, :saml_idp_login_url, :saml_idp_email_mapping, :saml_idp_cert, :saml_idp_logout_url, :saml_authn_context]
  end
end

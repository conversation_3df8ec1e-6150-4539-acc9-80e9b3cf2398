class V1::Settings::TurnServicesController < V1::Settings::BaseController
  wrap_parameters :call_setting, include: [
    :turn_service
  ]

  def update
    setting_params = params.require(:call_setting).permit(policy(Settings::CallSetting).permitted_attributes)

    safe_company_update do |company|
      if setting_params.key?(:turn_service)
        call_setting = company.call_settings
        call_setting.turn_service = setting_params[:turn_service]
      end
    end

    render_setting
  end

  private

  def render_setting
    call_setting = current_company.call_settings
    render json: Settings::CallSettingRepresenter.for_user(current_user).new(call_setting)
  end
end

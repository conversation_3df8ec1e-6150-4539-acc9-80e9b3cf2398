# frozen_string_literal: true

class V1::Settings::EmailSettingsController < V1::Settings::BaseController
  wrap_parameters :email_setting, include: [
    :enabled,
    :crm_records,
    :session_status
  ]

  before_action :fetch_setting_params, only: [:update]

  def show
    render_setting
  end

  def update
    channel_disabled = permitted_params.include?(:enabled) && !permitted_params[:enabled]

    safe_company_update(perform_save: false) do |company|
      SettingsService::EmailSettingsUpdater.new(company, permitted_params, current_user).call
    end

    SystemMailer.channel_deactivate_notification('Email', current_user).deliver_later if channel_disabled

    render_setting
  end

  private

  def permitted_params
    @permitted_params ||= params.require(:email_setting).permit([:enabled, :auto_append_session_id, :crm_records => {}, :session_status => {}])
  end

  def authorize_setting
    authorize current_company, policy_class: Settings::EmailSettingPolicy
  end

  def fetch_setting_params
    @setting_params = params.require(:email_setting).permit([:enabled, :crm_records, :session_status])
  end

  def render_setting
    email_setting = current_company.email_settings
    render json: Settings::EmailSettingRepresenter.for_user(current_user).new(email_setting)
  end
end

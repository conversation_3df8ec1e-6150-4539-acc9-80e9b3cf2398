class V1::PaymentProvidersController < ApplicationController
  wrap_parameters include: [:name, :provider_type, :settings, :currencies, :enabled]

  before_action :require_provider, only: [:show, :update, :destroy]
  before_action :authorize_payment_provider

  def index
    @providers = PaymentProvider.page(params[:page]).per(params[:per_page])
    add_pagination(@providers)
    render json: PaymentProviderRepresenter.for_user(current_user).for_collection.prepare(@providers)
  end

  def show
    render_provider
  end

  def create
    @provider = PaymentProviderService.create(permitted_params)
    render_provider
  rescue ActiveRecord::RecordInvalid => error
    render json: {message: error.message}, status: :bad_request
  rescue ActiveRecord::RecordNotUnique
    render json: {message: "Name has already been taken"}, status: :bad_request
  end

  def update
    @provider = PaymentProviderService.update(@provider, permitted_params)
    render_provider
  rescue ActiveRecord::RecordInvalid => error
    render json: {message: error.message}, status: :bad_request
  rescue ActiveRecord::RecordNotUnique
    render json: {message: "Name has already been taken"}, status: :bad_request
  end

  def destroy
    PaymentProviderService.destroy(@provider)
    render_provider
  end

  private

  def authorize_payment_provider
    authorize @provider || PaymentProvider
  end

  def require_provider
    @provider = PaymentProvider.find(params[:id])
  end

  def permitted_params
    @permitted_params ||= params.require(:payment_provider).permit(:name, :provider_type, :api_key, :enabled, currencies: [:currency, merchant_settings: {}], settings: {})
  end

  def render_provider
    render json: PaymentProviderRepresenter.for_user(current_user).prepare(@provider)
  end
end

class V1::EmailAccounts<PERSON>ontroller < ApplicationController
  before_action :authorize_email_accounts
  before_action :require_email_account, except: [:create, :index, :assignable_email_accounts, :oauth_callback_end, :outbound_only_emails]
  before_action :check_valid_email_account, only: [:create, :update]
  UNAVAILABLE_ERROR_MESSAGE = { message: 'Email service is not available now. The email account settings are not verified. Please try again later.' }.freeze
  UNAVAILABLE_STATUS_CODE = 503
  def index
    email_accounts = EmailAccount.all
    render json: EmailAccountRepresenter.for_collection.prepare(email_accounts)
  end

  def show
    render_email_account_detail
  end

  def create
    params[:email_account][:password] = Ujet.encryption_service.encrypt(password: params[:email_account][:password])
    params.require(:email_account).merge!(
      {
        incoming_server_port: 993,
        sender_server_port: 587,
        status: @account_status,
        has_folder: @account_status == 'valid' ? params[:email_account][:has_folder] : false
      }
    )

    if params[:email_account][:email_service] == 'oauthIMAP'
      # Use a dummy email address for now since email_id is a unique index,
      # it will be updated when we get the access token.
      params[:email_account][:email_id] = "dummy_#{Time.now.to_i}.invalid"
    end

    @email_account = EmailAccount.create!(permitted_params)

    return render json: UNAVAILABLE_ERROR_MESSAGE, status: :service_unavailable if @email_service_unavailable

    render_email_account
  end

  def update
    params.require(:email_account).merge!({
                                            status: @account_status,
                                            has_folder: @account_status == 'valid' ? params[:email_account][:has_folder] : false
                                          })
    if params[:email_account][:password].blank?
      permit_params = permitted_params_without_password
    else
      params[:email_account][:password] = Ujet.encryption_service.encrypt(password: params[:email_account][:password])
      permit_params = permitted_params
    end
    @email_account.update!(permit_params)

    return render json: UNAVAILABLE_ERROR_MESSAGE, status: :service_unavailable if @email_service_unavailable

    render_email_account
  end

  def destroy
    @email_account.destroy!
    render_email_account
  end

  def assignable_email_accounts
    accounts = EmailAccount.valid.left_joins(:email_channel_setting)
                           .where(outbound_only: false, email_channel_setting: { id: [nil, params[:channel_id]] })
                           .select(:id, :email_id)
    render json: accounts.compact
  end

  def oauth_callback
    resp = SettingsService::EmailOauthSettingsService.new.oauth_callback(params[:code], @email_account)

    @account_status = 'valid'

    data = {
      id: @email_account.id,
      email_service: @email_account.email_service,
      email_id: resp['email_id'],
      incoming_server_host: @email_account.incoming_server_host,
      incoming_server_port: @email_account.incoming_server_port || 993,
      oauth_linked: true,
      email_oauth_settings: resp['email_oauth_settings'] || Company.current.email_settings.email_oauth_settings || nil
    }

    validate_email_account(data)

    @email_account.update!(
      email_id: resp['email_id'],
      oauth_linked: resp['oauth_linked'],
      status: @account_status
    )

    # Need valid URL with hash value to close OAuth popup window
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/email_accounts/oauth_callback_end#success=true",
                allow_other_host: true
  rescue Errno::ECONNREFUSED
    Rails.logger.warn { "Error in Email OAuth callback : #{UNAVAILABLE_ERROR_MESSAGE}" }
    text = URI.encode_www_form_component(UNAVAILABLE_ERROR_MESSAGE[:message])
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/email_accounts/oauth_callback_end#success=false&message=#{text}",
                allow_other_host: true
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.warn(error: e) { "Error Invalid: #{e.message}" }
    text = URI.encode_www_form_component(e.record.errors.full_messages.first)
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/email_accounts/oauth_callback_end#success=false&message=#{text}",
                allow_other_host: true
  rescue Exception => e
    Rails.logger.warn(error: e) { "Error in Email OAuth callback : #{e.message}" }
    text = URI.encode_www_form_component('Unable to authenticate user')
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/email_accounts/oauth_callback_end#success=false&message=#{text}",
                allow_other_host: true
  end

  def oauth_callback_end
    # Use to close OAuth popup window
    render plain: ''
  end

  def outbound_only_emails
    result = EmailAccount.where(outbound_only: true)
    render json: EmailAccountRepresenter.for_collection.prepare(result)
  end

  private

  def check_valid_email_account
    # Skip for OAuth
    if permitted_params[:email_service] == 'oauthIMAP'
      @account_status = action_name == 'create' ? 'invalid' : @email_account[:status]
      @email_service_unavailable = false
      return
    end

    @account_status = 'valid'
    @email_service_unavailable = false
    data = {
      email_service: permitted_params[:email_service],
      email_id: permitted_params[:email_id],
      password: permitted_params[:password] || Ujet.encryption_service.decrypt(@email_account[:password])[:password],
      incoming_server_host: permitted_params[:incoming_server_host],
      incoming_server_port: permitted_params[:incoming_server_port] || 993
    }

    validate_email_account(data)
  end

  def validate_email_account(data)
    status = EmailAdapter::ImapService.new.check_valid(data)['status']
    @account_status = 'invalid' unless status == 'valid'
  rescue Errno::ECONNREFUSED
    @account_status = 'invalid'
    @email_service_unavailable = true
  rescue StandardError => e
    Rails.logger.warn(error: e) { "Error when check imap connection : #{e.message}" }
    @email_service_unavailable = e.respond_to?(:http_code) && e.http_code == UNAVAILABLE_STATUS_CODE
    @account_status = 'invalid'
  end

  def authorize_email_accounts
    authorize :email_accounts || @email_account
  end

  def require_email_account
    id = id_from_state(params[:state]) if params[:state].present?

    id = params[:id] unless id.present?
    @email_account = EmailAccount.find(id)
  end

  def render_email_account
    render json: EmailAccountRepresenter.new(@email_account)
  end

  def render_email_account_detail
    render json: EmailAccountDetailRepresenter.new(@email_account)
  end

  def permitted_params
    params.require(:email_account).permit(
      :email_service,
      :username,
      :incoming_server_host,
      :incoming_server_port,
      :sender_server_host,
      :sender_server_port,
      :has_folder,
      :email_id,
      :password,
      :status,
      :append_to_sent_box,
      :outbound_only
    )
  end

  def permitted_params_without_password
    params.require(:email_account).permit(
      :email_service,
      :username,
      :incoming_server_host,
      :incoming_server_port,
      :sender_server_host,
      :sender_server_port,
      :has_folder,
      :email_id,
      :status,
      :append_to_sent_box,
      :outbound_only
    )
  end

  def id_from_state(state)
    state_parts = state.split('|')

    return unless state_parts.last.start_with?('email_account_id_')

    id = state_parts.last
    id.slice! 'email_account_id_'
    id
  end
end

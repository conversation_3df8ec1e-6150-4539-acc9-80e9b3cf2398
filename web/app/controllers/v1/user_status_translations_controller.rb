# frozen_string_literal: true

module V1
  class UserStatusTranslationsController < ApplicationController
    before_action :authorize_user_status, :validate_user_status_name
    before_action :set_user_status_translation, only: [:show, :update, :destroy]

    # POST /v1/user_statuses/:user_status_name/translations
    def create
      UserStatusTranslation.create!(permitted_params)
      render json: UserStatusRepresenter.new(@user_status), status: :created
    end

    # PATCH/PUT /v1/user_statuses/:user_status_name/translations/:lang
    def update
      @user_status_translation.update!(permitted_params)
      render json: UserStatusRepresenter.new(@user_status).to_json, status: :ok
    end

    # DELETE /v1/user_statuses/:user_status_name/translations/:lang
    def destroy
      render json: UserStatusTranslationRepresenter.new(@user_status_translation.destroy)
    end

    private

    def permitted_params
      params.permit(:translated_name, :lang).merge(user_status_id: @user_status.id)
    end

    def authorize_user_status
      authorize UserStatus
    end

    def validate_user_status_name
      @user_status = UserStatus.find_by(name: params[:user_status_name])
      render json: { error: 'User status not found' }, status: :not_found unless @user_status
    end

    def set_user_status_translation
      @user_status_translation = UserStatusTranslation.find_by(lang: params[:lang], user_status_id: @user_status.id)
      render json: { error: 'Translation not found' }, status: :not_found unless @user_status_translation
    end
  end
end

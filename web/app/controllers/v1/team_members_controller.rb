class V1::TeamMembersController < ApplicationController
  before_action :require_team, except: [:import_template]
  before_action :authorize_team

  def index
    users = TeamService.query_members(team: @team, **params.to_unsafe_h.symbolize_keys, query_args: filter_params.to_h)

    add_pagination(users)

    render json: UserRepresenter.for_collection.prepare(users).to_json(exclude_params)
  end

  def import_template
    data = TeamService.import_template

    send_data(data, filename: "import_team_members_template.csv", type: "text/csv")
  end

  def import
    file = params.require(:file)
    # assume that it is UTF8 to resolve encoding problems of unicode characters : UJET-5875
    data = file.read.force_encoding(Encoding::UTF_8)
    result = TeamService.import_members(@team, data, role: params[:role])

    render json: result
  end

  private

  def require_team
    @team = Team.find(params.require(:team_id))
  end

  def authorize_team
    authorize @team || Team, policy_class: TeamMembersPolicy
  end

  def filter_params
    params.permit(:page, :per_page, roles: [], permissions: [])
  end

  def exclude_params
    default_excludes = [
      :teams,
      :assignments,
      :stats,
      :company,
      :assigned_call,
      :team_assignments,
      :campaign,
      :invitation_url
    ]
    excludes = default_excludes.concat(Array(params[:exclude])).map(&:to_sym)
    { exclude: excludes }
  end
end

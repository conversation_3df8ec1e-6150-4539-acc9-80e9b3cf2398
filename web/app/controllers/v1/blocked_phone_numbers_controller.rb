class V1::BlockedPhoneNumbersController < ApplicationController
  before_action :authorize_blocked_phone_number
  before_action :find_blocked_phone_number, only: [:show, :update, :destroy]

  def index
    numbers = BlockedPhoneNumber.all
    render json: BlockedPhoneNumberRepresenter.for_collection.prepare(numbers)
  end

  def show
    render_blocked_phone_number
  end

  def create
    @blocked_phone_number = BlockedPhoneNumber.new(permitted_params)
    @blocked_phone_number.save!
    render_blocked_phone_number
  rescue ActiveRecord::RecordInvalid
    raise
  rescue StandardError => e
    unhandled_exception(e,
                        'Unable to add phone number to the blocklist at the moment. Please try again later.')
  end

  def update
    @blocked_phone_number.update!(permitted_params)
    render_blocked_phone_number
  end

  def destroy
    @blocked_phone_number.destroy!
    render_blocked_phone_number
  rescue StandardError => e
    unhandled_exception(e,
                        'Unable to remove phone number from the blocklist at the moment. Please try again later.')
  end

  private

  def find_blocked_phone_number
    @blocked_phone_number = BlockedPhoneNumber.find(params[:id])
  end

  def render_blocked_phone_number
    render json: BlockedPhoneNumberRepresenter.new(@blocked_phone_number)
  end

  def permitted_params
    params.permit(:number)
  end

  def authorize_blocked_phone_number
    authorize :blocked_phone_number
  end
end

class V1::AmbController < ApplicationController
  skip_before_action :authenticate, only: [:message, :oauth_callback]

  def message
    raise UnauthorizedException unless amb_authenticated?

    event_processor.process(Amb::Event.build(
                              id: params['id'] || request.headers.fetch('Id'),
                              version: params['v'],
                              type: params['type'],
                              locale: params['locale'],
                              opaque_id: params['sourceId'],
                              business_id: params['destinationId'] || request.headers.fetch('Destination-Id'),
                              device_capabilities:,
                              body: params['body'],
                              attachments: params['attachments'],
                              group: params['group'],
                              intent: params['intent'],
                              interactive_data: params['interactiveData'].try(:to_unsafe_h),
                              interactive_data_ref: params['interactiveDataRef']
                            ))
    head :ok
  end

  def oauth_callback
    permitted_params = params.permit(:state, :code)
    result = Amb::OAuthCallback.new(state: permitted_params.fetch('state'), code: permitted_params.fetch('code')).run
    query_params = ["status=#{result.status}"]
    query_params << "code=#{result.code}" if result.code.present?

    redirect_to "messages-auth://?#{query_params.join('&')}", allow_other_host: true
  end

  protected

  def should_verify_authorized
    false
  end

  private

  def amb_authenticated?
    Amb::Jwt.authenticated?(request.authorization.to_s.split(' ').second)
  end

  def event_processor
    @event_processor ||= Amb::EventProcessor.new
  end

  def device_capabilities
    overrides = Amb::UserDeviceCapabilityOverrides.get
    return overrides.fetch('capabilities', []) if overrides.fetch('enabled', false)

    request.headers.fetch('Capability-List').split(',')
  end
end

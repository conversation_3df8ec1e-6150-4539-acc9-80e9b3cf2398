class V1::DynamicsController < ApplicationController
  skip_before_action :authenticate, only: [:oauth_callback]

  def oauth_start
    if current_company.dynamics_azure_tenant.blank? || current_company.dynamics_domain.blank?
      raise ServiceException, "Please set Dynamics domains."
    end

    if current_company.oauth_client_id.blank? || current_company.oauth_client_secret.blank?
      raise ServiceException, "Please set the OAuth client credentials first."
    end

    state = AuthToken.issue_token(agent_id: current_user.id)
    redirect_uri = "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/dynamics/oauth_callback"
    tenant = current_company.dynamics_azure_tenant
    client_id = current_company.oauth_client_id
    resource = "https://#{current_company.dynamics_domain}"
    start_url = "https://login.windows.net/#{tenant}/oauth2/authorize?response_type=code&client_id=#{client_id}&redirect_uri=#{redirect_uri}&state=#{state}&resource=#{resource}"

    render json: { redirect_url: start_url }
  end

  def oauth_callback
    url = "https://" + URLHelper.host(request, remove_api: true)
    url += ":#{Rails.configuration.env[:client_app_port]}" if Rails.configuration.env[:client_app_port]
    url += "/oauth/dynamics/popup"

    if params[:code]
      payload, header = AuthToken.decode(params[:state])
      agent_id = payload["agent_id"]
      data = {
          :userId => agent_id,
          :redirectUrl => "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/dynamics/oauth_callback",
          :dynamicsAzureTenant => current_company.dynamics_azure_tenant,
          :resource => "https://#{current_company.dynamics_domain}",
          :clientId => current_company.oauth_client_id,
          :clientSecret => current_company.oauth_client_secret,
          :code => params[:code]
      }

      # CRM Server will take care of token acquire and store to redis.
      res = ::CRM::CrmServer.oauth_request(url:'/oauth_callback', method: :post, params: data, user_id: agent_id)
      if res && res.dig('error').present?
        url += "?error=#{params[:error]}&error_description=#{params[:error_description]}"
      else
        agent = User.find(agent_id)
        agent.crm_authenticated = true
        agent.save
      end
    end
    redirect_to url, allow_other_host: true
  end

  private

  def should_verify_authorized
    false
  end
end

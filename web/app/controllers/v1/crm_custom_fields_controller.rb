class V1::CrmCustomFieldsController < ApplicationController
  before_action :required_custom_field, only: [:update, :destroy]
  before_action :authorize_custom_field

  def index
    fields = CrmCustomField.all
    render json: CrmCustomFieldRepresenter.for_collection.prepare(fields)
  end

  def create
    @custom_field = CrmCustomField.create!(permitted_params)
    render_custom_field
  end

  def update
    @custom_field.update!(permitted_params)
    render_custom_field
  end

  def destroy
    @custom_field.destroy
    render_custom_field
  end

  private
  def authorize_custom_field
    authorize @custom_field || CrmCustomField
  end

  def permitted_params
    params.require(:crm_custom_field).permit(:name, :crm_field, :key, :type)
  end

  def required_custom_field
    @custom_field = CrmCustomField.find(params.require(:id))
  end

  def render_custom_field
    render json: CrmCustomFieldRepresenter.new(@custom_field)
  end
end

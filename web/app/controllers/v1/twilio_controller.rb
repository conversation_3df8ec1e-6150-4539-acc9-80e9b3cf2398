class V1::TwilioController < ApplicationController
  include TwilioService::AudibleMessageSupport
  include TwilioService::CallParameterHelper
  include UseSipHeaders

  skip_before_action :authenticate
  before_action :extract_sip_phone_number
  before_action :set_sip_headers, only: :ivr_intro_sip
  before_action :set_tracker_id
  before_action :set_client_app_id
  before_action :set_sids
  before_action :request_validation, except: %i[debugging_events debugging_forward]
  before_action :failure_simulate, except: %i[debugging_events debugging_forward voice_fallback status_callback]

  def should_verify_authorized
    false
  end

  SIP_REGEX = /^sip:(.*?)@.*$/

  def voice
    twiml = voice_callback_service.voice
    if twiml.blank?
      head :ok
      return
    end
    render xml: twiml.to_xml
  end

  # TwiML <Refer> verb action
  def refer_callback
    twiml = Twilio::TwiML::VoiceResponse.new(&:hangup)
    render xml: twiml.to_xml
  end

  def conference_callback
    voice_callback_service.conference_callback

    head :ok
  end

  def recording_callback
    voice_callback_service.recording_callback

    head :ok
  end

  def status_callback
    voice_callback_service.status_callback
    head :ok
  end

  def status_callback_sip
    status_callback
  end

  def siprec_status_callback
    voice_callback_service.siprec_status_callback
    head :ok
  end

  # The action for <Record> verb
  def voicemail_callback
    # Voicemail recording is finished, but the recording file may not yet be accessible. Twilio will
    # make a request to the given recordingStatusCallback, which is 'voicemail_recording_callback'
    # in this case, when the recording file is available to access.
    render xml: empty_twiml.to_xml
  end

  # The recordingStatusCallback for <Record> verb
  def voicemail_recording_callback
    voice_callback_service.voicemail_recording_callback

    head :ok
  end

  def conference_join
    twiml = voice_callback_service.conference_join

    render xml: twiml.to_xml
  end

  # plays hold music
  def play_hold
    twiml = voice_callback_service.holding
    render xml: twiml.to_xml
  end

  def loop_audio
    twiml = voice_callback_service.loop_audio
    render xml: twiml.to_xml
  end

  #
  # SMS
  #

  # Inbound message callback
  def message_callback
    inbound_sms_callback

    head :ok
  end

  # Outbound message status callback
  def message_status_callback
    twilio_message_service.message_status_callback(
      message_status: params[:MessageStatus],
      error_code: params[:ErrorCode]
    )

    head :ok
  end

  # Bi-directional SMS, a.k.a SMS Chat
  # We use a different end-point in-case we want to make future changes.
  # We rely on different Twilio messaging services for each SMS functionality.
  # Twilio sid is part of the request payload
  def sms_chat
    inbound_sms_callback
    head :ok
  end

  #
  # IVR call
  #
  def ivr_intro
    twiml = voice_callback_service.ivr_intro

    render xml: twiml.to_xml
  end

  def queue_lang
    twiml = voice_callback_service.queue_lang

    render xml: twiml.to_xml
  end

  def extension_directory_callback
    step = params[:step]&.to_s

    if step.blank? # initial search
      twiml = voice_callback_service.extension_directory_callback
    elsif step == 'new_search_confirmation'
      twiml = voice_callback_service.new_search_confirmation_callback
    elsif step == 'agent_connect_confirmation'
      twiml = voice_callback_service.agent_connect_confirmation_callback
    end

    render xml: twiml.to_xml
  end

  def ivr_intro_sip
    ivr_intro
  end

  def ivr_callback
    twiml = voice_callback_service.ivr_callback

    render xml: twiml.to_xml
  end

  def ivr_deflected_to_queue_callback
    twiml = voice_callback_service.ivr_deflected_to_queue_callback

    render xml: twiml.to_xml
  end

  def ivr_transfer_to_non_leaf_queue_callback
    twiml = voice_callback_service.ivr_transfer_to_non_leaf_queue_callback

    render xml: twiml.to_xml
  end

  def ivr_recording_option
    twiml = voice_callback_service.ivr_recording_option
    render xml: twiml.to_xml
  end

  def graceful_outage
    twiml = voice_callback_service.graceful_outage

    render xml: twiml.to_xml
  end

  def over_capacity_deflection
    twiml = voice_callback_service.over_capacity_deflection

    render xml: twiml.to_xml
  end

  def after_hours_deflection
    twiml = voice_callback_service.after_hours_deflection

    render xml: twiml.to_xml
  end

  def presession_deflection
    case params[:step]&.to_s
    when 'start'
      twiml = voice_callback_service.presession_deflection_start
    when 'accepted'
      twiml = voice_callback_service.presession_deflection_accepted
    when 'finish'
      twiml = voice_callback_service.presession_deflection_finish
    when 'fail'
      twiml = voice_callback_service.presession_deflection_failure
    else
      raise ServiceException, "Invalid step #{params[:step]}"
    end

    render xml: twiml.to_xml
  end

  # IVR call was in queue for announcement_interval minutes
  # will be redirected to this when over_capacity_deflection or estimated_wait_time repeat is enabled.
  def announcement_deflection
    twiml = voice_callback_service.announcement_deflection

    render xml: twiml.to_xml
  rescue SystemStackError => sse
    open('/tmp/SystemStackError.txt', 'a') do |fd|
      sse.backtrace.each_with_index do |frame, index|
        fd.write("#{index}: #{frame}\n")
      end
    end
    raise sse

  end

  def ivr_waiting
    twiml = voice_callback_service.ivr_waiting

    render xml: twiml.to_xml
  end

  def ivr_payment
    twiml = voice_callback_service.payment
    render xml: twiml.to_xml
  end

  def ivr_csat
    twiml = voice_callback_service.ivr_csat

    render xml: twiml.to_xml
  end

  def callback_number_request
    twiml = voice_callback_service.callback_number_request

    render xml: twiml.to_xml
  end

  def callback_number_confirm
    twiml = voice_callback_service.callback_number_confirm

    render xml: twiml.to_xml
  end

  def csat_voicememo_recording_callback
    voice_callback_service.csat_voicememo_recording_callback

    head :ok
  end

  def ivr_wait_time_sms
    twiml = voice_callback_service.ivr_wait_time_sms

    render xml: twiml.to_xml
  end

  # Receive a Twilio webhook every time an error or warning occurs on UJET account.
  def debugging_events
    debugging_events = TwilioService::DebuggingEvents.new(request, params)
    debugging_events.error_debug
    head :ok
  end

  def debugging_forward
    debugging_events = TwilioService::DebuggingEvents.new(request, params)
    debugging_events.error_forward
    head :ok
  end

  def voice_fallback
    error_url = params[:ErrorUrl]
    if error_url.present?
      error_uri = URI.parse(error_url)
      controller_and_action = Rails.application.routes.recognize_path(error_uri.path, method: request.method)
      action = controller_and_action[:action]
      return self.try(action)
    end
    head :ok
  end

  def hangup
    twiml = Twilio::TwiML::VoiceResponse.new(&:hangup)
    render xml: twiml.to_xml
  end

  def task_virtual_agent_start
    twiml = voice_callback_service.start_task_virtual_agent
    render xml: twiml.to_xml
  end

  def task_virtual_agent_finish
    twiml = voice_callback_service.finish_task_virtual_agent
    render xml: twiml.to_xml
  end

  def virtual_agent_stream_start
    twiml = voice_callback_service.start_virtual_agent_stream
    render xml: twiml.to_xml
  end

  def virtual_agent_stream_end
    twiml = voice_callback_service.virtual_agent_stream_ended
    render xml: twiml.to_xml
  end

  def virtual_agent_status_callback
    voice_callback_service.virtual_agent_status_callback
    head :ok
  end

  def agent_assist_status_callback
    voice_callback_service.agent_assist_status_callback
    head :ok
  end

  def virtual_agent_recording_callback
    voice_callback_service.virtual_agent_recording_callback
    head :ok
  end

  def agent_voice_detection_missed
    twiml = voice_callback_service.agent_voice_detection_missed
    render xml: twiml.to_xml
  end

  def ivr_escalation
    twiml = voice_callback_service.ivr_escalation
    render xml: twiml.to_xml
  end

  def ivr_escalation_failure
    twiml = voice_callback_service.ivr_escalation_failed
    render xml: twiml.to_xml
  end

  def async_amd_callback
    twiml = voice_callback_service.async_amd_callback
    if twiml.blank?
      head :ok
      return
    end
    render xml: twiml.to_xml
  end

  def dnc
    twiml = voice_callback_service.dnc
    render xml: twiml.to_xml
  end

  def stop_media_streams
    twiml = voice_callback_service.stop_media_streams
    render xml: twiml.to_xml
  end

  def mobile_agent_voice
    twiml = voice_callback_service.mobile_agent_voice
    render xml: twiml.to_xml
  end

  def ucaas_agent_voice
    twiml = voice_callback_service.ucaas_agent_voice
    render xml: twiml.to_xml
  end

  def announce_and_hangup
    twiml = voice_callback_service.announce_and_hangup
    render xml: twiml.to_xml
  end

  def announce
    twiml = voice_callback_service.announce
    render xml: twiml.to_xml
  end

  def internal_call_after_hours_deflection
    twiml = voice_callback_service.internal_call_after_hours_deflection
    render xml: twiml.to_xml
  end

  def internal_call_after_hours_deflection_callback
    twiml = voice_callback_service.internal_call_after_hours_deflection_callback
    render xml: twiml.to_xml
  end

  def internal_call_over_capacity_deflection
    twiml = voice_callback_service.internal_call_over_capacity_deflection
    render xml: twiml.to_xml
  end

  def internal_call_over_capacity_deflection_callback
    twiml = voice_callback_service.internal_call_over_capacity_deflection_callback
    render xml: twiml.to_xml
  end

  def internal_call_automatic_redirection
    twiml = voice_callback_service.internal_call_automatic_redirection
    render xml: twiml.to_xml
  end

  def internal_call_voicemail_recorded
    twiml = voice_callback_service.internal_call_voicemail_recorded
    render xml: twiml.to_xml
  end

  def internal_announcement_deflection
    twiml = voice_callback_service.internal_announcement_deflection
    render xml: twiml.to_xml
  end

  def sessionless_sms
    Sms::SessionlessSmsService.send_auto_response(from_phone: params[:To], to_phone: fix_invalid_latvian_number(params[:From]))
    head :ok
  end

  private

  def should_verify_authorized
    false
  end

  def empty_twiml
    Twilio::TwiML::VoiceResponse.new do |r|
    end
  end

  def voice_callback_service
    params[:Region] = region
    @voice_callback_service ||= TwilioService::VoiceCallback.new(params)
  end

  def twilio_message_service
    from = fix_invalid_latvian_number(params[:From])

    Messaging::Callback::Twilio.new(
      message_sid: params[:MessageSid],
      to: params[:To],
      from: from,
      messaging_service_sid: params[:MessagingServiceSid]
    )
  end

  def extract_sip_phone_number
    [:To, :From].each do |key|
      next unless params[key].present?

      result = params[key].match(SIP_REGEX)
      next unless result.present?

      phone_number = Phonelib.parse(result[1])
      next unless phone_number.possible?

      params["Original_#{key}"] = params[key]

      # prepend + prefix if there isn't. Twilio phone number validation needs it.
      params[key] = phone_number.e164
    end
  end

  def set_tracker_id
    if params[:call_id].present?
      RequestStore.store[:tracker_id] = "call_#{params[:call_id]}"
    elsif params[:chat_id].present?
      RequestStore.store[:tracker_id] = "chat_#{params[:chat_id]}"
    end
  end

  def set_client_app_id
    if params[:client_app_id].present?
      RequestStore.store[:client_app_id] = params[:client_app_id]
    end
  end

  def request_validation
    auth_token = Company.current.twilio_auth_token(region)
    validator = Twilio::Security::RequestValidator.new(auth_token)
    twilio_signature = request.headers['HTTP_X_TWILIO_SIGNATURE']
    original_url = request.original_url

    unless validator.validate(original_url, request.request_parameters, twilio_signature)
      logger.warn(data: {action: params[:action]}) { '[Twilio] twilio request is NOT valid.' }

      raise ServiceException, 'Twilio request validation failure' if FeatureFlag.on?('twilio-request-validation')
    end
  end

  def failure_simulate
    if FeatureFlag.on?('simulate-twilio-callback-failure')
      raise ServiceException, 'Similating Twilio failure'
    end
  end

  def set_sids
    RequestStore.store[:call_sid] = params[:CallSid] if params[:CallSid].present?
    RequestStore.store[:conference_sid] = params[:ConferenceSid] if params[:ConferenceSid].present?
  end

  def inbound_sms_callback
    return if Company.current.locked?

    # Return if number is blocked
    rec = BlockedPhoneNumberService.find_phone_number_in_blocklist(number: params[:From])
    if rec
      Rails.logger.info("blocking sms from blocked phone number #{rec.id}")
      return
    end

    num_media = params[:NumMedia].to_i - 1
    medias = []
    (0..num_media).each do |n|
      media_content_type = "MediaContentType#{n}"
      media_url = "MediaUrl#{n}"
      medias.push({
                    media_provider: :twilio,
                    media_url: params[media_url],
                    media_type: params[media_content_type]
                  })
    end

    twilio_message_service.message_callback(
      body: params['Body'],
      medias: medias
    )
  end

  def region
    request.headers['HTTP_X_HOME_REGION'] || TwilioService::Regions::GLOBAL_DEFAULT
  end
end

class V1::GenericApiController < ApplicationController
  skip_before_action :authenticate

  def oauth_auth
    if current_company.crm_settings.custom_crm.oauth_client_id.nil? or current_company.crm_settings.custom_crm.oauth_client_secret.nil?
      raise ServiceException, "Please set the OAuth client credentials first."
    end
    CRM::CrmServer.request(
      url: '/oauth_auth',
      method: :post
    )
    render json: {}, status: 200
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.warn(error: e) { "Error in Generic API rest : #{e.message}" }
    text = 'Unable to authenticate Generic API user'
    render json: { text: text }, status: :forbidden
  rescue => e
    Rails.logger.warn(error: e) { "Error in Generic API : #{e.message}" }
    text = 'Unable to authenticate Generic API user'
    render json: { text: text }, status: :forbidden
  end

  def oauth_callback
    CRM::CrmServer.request(
      url: '/oauth_callback',
      method: :post,
      params: {
        code: params[:code]
      }
    )
    # Need valid URL with hash value to close OAuth popup window
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/generic_api/oauth_callback_end#success=true", allow_other_host: true
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.warn(error: e) { "Error in Generic API callback rest : #{e.message}" }
    text = 'Unable to authenticate Generic API user'
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/generic_api/oauth_callback_end#success=false&message=#{text}", allow_other_host: true
  rescue => e
    Rails.logger.warn(error: e) { "Error in Generic API callback : #{e.message}" }
    text = 'Unable to authenticate Generic API user'
    redirect_to "https://#{URLHelper.host_with_port(request)}/v1/generic_api/oauth_callback_end#success=false&message=#{text}", allow_other_host: true
  end

  def oauth_callback_end
    # Use to close OAuth popup window
    render plain: ''
  end

  private

  def should_verify_authorized
    false
  end
end

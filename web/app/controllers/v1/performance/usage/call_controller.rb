# frozen_string_literal: true

module V1
  module Performance
    module Usage
      class CallController < V1::Performance::BaseController
        def live
          render json: { result: ::Performance::Usage::Call.live }
        end

        def period
          result = ::Performance::Usage::Call.period(
            filter: ::Performance::Filter.new(
              data_scope: data_scope, params: params
            )
          )
          render json: { result: result }
        end

        def policy_obj
          [:performance, :usage, :call]
        end
      end
    end
  end
end

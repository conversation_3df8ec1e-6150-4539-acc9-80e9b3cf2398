# frozen_string_literal: true

module V1
  module Performance
    # Base controller for performance requests
    class BaseController < ApplicationController
      around_action :use_dashboard_replica
      before_action :do_authorization

      protected

      def policy_obj
        raise 'Subclass of V1::Performance::BaseController must implement policy_obj'
      end

      def data_scope
        scope_class = (policy_obj.map { |x| x.to_s.camelize }.join('::') + 'Policy::Scope').constantize
        @data_scope ||= scope_class.new(current_user).resolve
        raise NoPermissionException unless @data_scope.present?

        @data_scope
      end

      def do_authorization
        authorize policy_obj
      end
    end
  end
end

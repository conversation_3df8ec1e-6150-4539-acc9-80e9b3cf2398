# frozen_string_literal: true

module V1
  module Performance
    class SummaryController < V1::Performance::BaseController

      def index
        from = params.dig(:from)
        to = params.dig(:to)
        call_scope = ::Dashboard::DataScope.for_user(current_user, :call)
        chat_scope = ::Dashboard::DataScope.for_user(current_user, :chat)
        render json: ::Performance::Summary.call(call_scope, chat_scope, from, to)
      end

      protected

      def policy_obj
        [:performance, :summary]
      end
    end
  end
end

# frozen_string_literal: true

module V1
  module Performance
    class ChatController < V1::Performance::BaseController
      before_action :set_channel_type

      def summary
        result = ::Performance.chat_summary(
          filter: ::Performance::Filter.new(
            data_scope: data_scope,
            params: params
          )
        )
        render json: result
      end

      def service_level
        result = ::Performance.chat_service_level(
          filter: ::Performance::Filter.new(
            data_scope: data_scope,
            params: params
          )
        )
        target_metrics = ::Performance::MenuTargetMetrics.new(
          menus: params[:menus],
          lang: params[:lang],
          channel_type: :chat
        )
        render json: {
          result: result,
          sla_target_percent: target_metrics.sl_target_percent,
          sla_target_time: target_metrics.sl_target_time,
          concurrency_target: target_metrics.concurrency_target,
          concurrency_global_target: ::Performance::Setting.chat_concurrency_target
        }
      end

      def queue
        result = ::Performance.chat_queue(
          filter: ::Performance::Filter.new(
            data_scope: data_scope,
            params: params
          )
        )
        render json: { result: result }
      end

      protected

      def policy_obj
        [:performance, :chat]
      end

      private

      def set_channel_type
        params.merge!({ channel_type: Chat.channel_type })
      end
    end
  end
end

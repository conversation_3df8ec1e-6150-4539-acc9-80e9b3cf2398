# frozen_string_literal: true

module V1
  module Performance
    class CallController < V1::Performance::BaseController
      before_action :set_channel_type

      def summary
        result = ::Performance.call_summary(
          filter: ::Performance::Filter.new(
            data_scope: data_scope,
            params: params
          )
        )
        render json: result
      end

      def service_level
        result = ::Performance.call_service_level(
          filter: ::Performance::Filter.new(
            data_scope: data_scope,
            params: params
          )
        )
        target_metrics = ::Performance::MenuTargetMetrics.new(
          menus: params[:menus],
          lang: params[:lang],
          channel_type: :voice_call
        )
        render json: {
          result: result,
          sla_target_percent: target_metrics.sl_target_percent,
          sla_target_time: target_metrics.sl_target_time
        }
      end

      def queue
        result = ::Performance.call_queue(
          filter: ::Performance::Filter.new(
            data_scope: data_scope,
            params: params
          )
        )
        render json: { result: result }
      end

      protected

      def policy_obj
        [:performance, :call]
      end

      def set_channel_type
        params.merge!({ channel_type: Call.channel_type })
      end
    end
  end
end

# frozen_string_literal: true
#
module V1
  module Performance
    module VirtualAgent
      class CallController < V1::Performance::BaseController
        before_action :set_channel_type

        def period
          validate_param_existence([:to, :from, :timezone, :time_series_units])
          result = ::Performance::VirtualAgent::Call.period(
            filter: ::Performance::Filter.new(
              data_scope: data_scope, params: params
            )
          )
          render json: { result: result }
        end

        def live
          validate_param_existence([:to, :from])
          result = ::Performance::VirtualAgent::Call.live(
            filter: ::Performance::Filter.new(
              data_scope: data_scope, params: params
            )
          )
          render json: { result: result }
        end

        def validate_param_existence(required_params)
          required_params.each { |rp| params.require(rp) }
        end

        private

        def set_channel_type
          params.merge!({ channel_type: Call.channel_type })
        end

        def policy_obj
          [:performance, :virtual_agent, :call]
        end
      end
    end
  end
end


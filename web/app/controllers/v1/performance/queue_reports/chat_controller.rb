# frozen_string_literal: true

module V1
  module Performance
    module QueueReports
      class ChatController < V1::Performance::BaseController
        before_action :set_channel_type
        before_action :add_queue_report_group_params

        def period
          render json: {
            result: ::Performance.chat_queue_reports_period(
              filter: ::Performance::Filter.new(
                data_scope: data_scope,
                params: params
              )
            )
          }
        end

        def live
          render json: {
            result: ::Performance.chat_queue_reports_live(
              filter: ::Performance::Filter.new(
                data_scope: data_scope,
                params: params
              )
            )
          }
        end

        protected

        def policy_obj
          [:performance, :queue_reports, :chat]
        end

        private

        def add_queue_report_group_params
          return if params.dig(:queue_report_groups).present?

          queue_report_groups = ::Dashboard::QueueReportGroup.with_chat.pluck(:id)
          params.merge!({ queue_report_groups: queue_report_groups, active_queue_groups: true })
        end

        def set_channel_type
          params.merge!({ channel_type: Chat.channel_type })
        end
      end
    end
  end
end

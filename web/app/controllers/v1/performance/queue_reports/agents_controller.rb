# frozen_string_literal: true

module V1
  module Performance
    module QueueReports
      class AgentsController < V1::Performance::BaseController
        before_action :add_queue_report_group_params

        def index
          agents = ::Performance.queue_reports_agents(
            filter: ::Performance::Filter.new(
              data_scope: data_scope,
              params: params
            ),
            include_on_hold: ActiveRecord::Type::Boolean.new.cast(params.dig(:include_on_hold))
          )

          add_pagination(agents) unless agents.blank?

          render json: {
            statuses: UserStatus.all_statuses.map { |status| { name: status.name, color: status.color } },
            agents: ::Dashboard::AgentRepresenter.for_collection.prepare(agents)
          }
        end

        protected

        def policy_obj
          [:performance, :queue_reports, :agent]
        end

        private

        def add_queue_report_group_params
          params.merge!({ active_queue_groups: true})
          return if params[:queue_report_groups].present?

          scope = ::Dashboard::QueueReportGroup
          scope = scope.with_call if params.dig(:channel_type).to_s == 'voice_call'
          scope = scope.with_chat if params.dig(:channel_type).to_s == 'chat'
          queue_report_groups = scope.pluck(:id)

          params.merge!({ queue_report_groups: queue_report_groups })
        end
      end
    end
  end
end

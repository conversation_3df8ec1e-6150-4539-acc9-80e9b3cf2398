class V1::UsersController < ApplicationController
  skip_before_action :authenticate, only: [:reset_password_code, :reset_password]
  before_action :require_user, only: [:destroy, :resync, :sign_out_agent, :active_comms]
  before_action :authorize_user, only: [:index, :create, :destroy, :simple, :sign_out_agent, :overview, :name_only,
                                        :import, :import_template, :multiple_destroy, :multiple_restore, :active_comms]
  attr_reader :user

  rescue_from 'RoleService::TooManyRolesAssignedError' do |e|
    render json: { message: e.message }, status: :bad_request
  end

  def index
    scope = policy_scope(User)
    scope = scope.under_manager(params[:manager_id]) if params[:manager_id]
    scope = scope.online if params[:online]

    # TODO: update these two lines for custom roles.
    roles = params.permit(roles: []).fetch(:roles, nil)
    scope = scope.with_any_roles(*roles) if roles

    # use permissions when grabbing agent.
    scope = scope.with_agent_permission if ActiveRecord::Type::Boolean.new.deserialize(params.delete(:agent))

    scope = scope.with_name(params[:search_text])

    opts = stats_params.merge(exclude_params)

    @users = UserLoader.load(scope, **opts)

    render json: UserRepresenter.for_collection.prepare(@users).to_json(exclude_params)
  end

  def simple
    filter_params = params.permit(:cursor, :page, :per_page, custom_roles: [], roles: [], permissions: [])
    search_text = params[:search_text]
    agents_without_extensions = ActiveRecord::Type::Boolean.new.cast(params[:agents_without_extensions])

    scoped = User.basic_search(search_text: search_text)
                 .with_extension_numbers(agents_without_extensions)
                 .with_activated(params[:activated])
                 .with_assigned_to_any_queue(params[:assigned_to_any_queue])
                 .with_deactivated(params[:with_deactivated])
                 .only_deactivated(params[:only_deactivated])
                 .with_internal_contact
                 .order(:id)

    scoped = scoped.with_extension_numbers_only if agents_without_extensions == false

    scoped = UserPolicy::Scope.new(current_user, scoped).resolve

    scoped = scoped.with_custom_roles(filter_params.delete(:custom_roles)) if filter_params.key?(:custom_roles)
    scoped = scoped.includes(:company_location) if FeatureFlag.on? 'enable-localization'

    users = SimpleQuery.call(scoped: scoped, args: filter_params.to_h)
    _exclude_params = exclude_params(default_exclude: [:teams, :assignments, :stats, :company, :assigned_call, :team_assignments, :campaign, :invitation_url, :user_setting])
    add_pagination(users)
    UserLoader.inject_teams(users, exclude: _exclude_params)
    swizzle_custom_roles(users)

    render json: UserRepresenter.for_collection.prepare(users).to_json(_exclude_params)
  end

  def show
    scope = policy_scope(User)
    opts = stats_params.merge(exclude_params)
    @user = UserLoader.find(scope, params[:id], **opts)
    authorize @user
    render_user
  end

  def detail
    scope = policy_scope(User)
    @user = UserLoader.find(scope, params[:id], stats: stats_params[:stats])
    authorize @user
    render json: AgentDetailRepresenter.new(@user)
  end

  def create
    ucaas_attr = ucaas_attr_from_params
    if ucaas_attr.present? # pre-validation for ucaas.
      errors = UserUcaasDataService.validate_sip_and_username(ucaas_attr[:ucaas_sip_uri], ucaas_attr[:ucaas_user_name], nil)
      return render json: { errors: errors }, status: :bad_request if errors.present?
    end

    extension_numbers = user_params[:extension_numbers]
    extension_numbers_service = ExtensionNumbersService.new
    extension_numbers_service.check_if_used_extension_numbers(extension_numbers) if extension_numbers.present?

    @user = UserService.create_and_invite(user_attrs_from_params)

    user_setting_service = UserSettingService.new
    user_settings_params = user_settings_params(user_params)
    user_setting_service.save(@user, user_settings_params)

    if ucaas_attr.present? && ucaas_attr[:enabled]
      UserUcaasDataService.create(ucaas_attr, @user.id)
      Ucaas::MsTeamsService.new.create_presence_subscription(URLHelper.base_url(request, remove_api: true))
    end

    if extension_numbers.nil?
      Contact::ContactService.create_agent_contact(@user.id)
    else
      extension_numbers_service.save_extension_numbers(extension_numbers, @user.id, @current_user.id)
    end

    phone_numbers = user_params[:phone_numbers]
    UserService.update_phone_assignments(@user.id, phone_numbers) unless phone_numbers.nil? || phone_numbers.empty?

    render_user
  end

  # /user/:id/status
  # @param: { status }
  def update_status
    @user = User.find(params[:id])
    status = params[:status]

    authorize @user

    if status.present?
      User::ChangeStatusValidator.new(@user, by_admin: true).validate!(status)

      if @user.in_call_wrapping_up_without_writing_disposition?
        @user.call_participants.wrapping_up.each(&:finished!)
      end

      @user.update(
        ignored_projection_count: 0,
        skipped_projection_count: 0
      )
      UserService.change_status(user: @user, status_name: status)
    end

    render_user
  end

  def update
    @user = User.find(params[:id])
    status = params[:user][:status]

    authorize @user

    # update extension_number
    user_params = params.require(:user)
    extension_numbers = user_params[:extension_numbers]

    if @current_user&.agent_permission?
      ExtensionNumbersService.new.save_extension_numbers(extension_numbers, user_params[:id].to_i, @current_user.id)
    else
      ExtensionNumbersService.new.save_extension_numbers(extension_numbers, user_params[:id].to_i, @user.id)
    end

    # update ucaas_attrs
    user_ucaas_attrs = ucaas_attr_from_params
    if user_ucaas_attrs.present? # for version mismatches without ucaas
      if user_ucaas_attrs[:enabled]
        errors = UserUcaasDataService.validate_sip_and_username(user_ucaas_attrs[:ucaas_sip_uri], user_ucaas_attrs[:ucaas_user_name], @user.id)
        return render json: { errors: errors }, status: :bad_request if errors.present?
      end

      ucaas_updated = UserUcaasDataService.update(user_ucaas_attrs, @user) unless status.present?

      if ucaas_updated
        Ucaas::MsTeamsService.new.create_presence_subscription(URLHelper.base_url(request, remove_api: true))
      end
    end
    user_setting_service = UserSettingService.new
    # update user_settings
    user_settings_params = user_settings_params(user_params)
    user_setting_service.save(@user, user_settings_params)

    # update user phone assignments
    phone_numbers = user_params[:phone_numbers] || []
    UserService.update_phone_assignments(@user.id, phone_numbers)

    # update user_attrs
    user_attrs = user_attrs_from_params
    UserService.update(@user, user_attrs, params[:admin_password])

    if status.present?
      User::ChangeStatusValidator.new(@user, by_admin: true).validate!(status)

      if @user.in_call_wrapping_up_without_writing_disposition?
        @user.call_participants.wrapping_up.each(&:finished!)
      end

      @user.update(
        ignored_projection_count: 0,
        skipped_projection_count: 0
      )
      UserService.change_status(user: @user, status_name: status)
    end

    render_user
  rescue UserService::InvalidPhoneNumberAssignmentsError => e
    render json: { message: e.to_s }, status: :bad_request
  rescue UserService::InvalidPasswordError, UserService::PermissionError => e
    render json: { message: e.to_s }, status: :forbidden
  end

  def sign_out_agent
    authenticate
    user_id = params[:id]
    @user = User.with_agent_permission.find_by_id(user_id)
    raise ServiceException, "Invalid user_id: #{user_id}" if @user.blank?

    if @user.in_call?
      @user.call_participants.in_progress.each do |participant|
        Rails.logger.info(agent_id: user_id, call: participant.call) { 'user disconnected from call by sign_out_agent' }
        CallService::ProgressService.new(participant.call).disconnect(by: @user)
      end
    end

    if @user.in_chat?
      @user.chat_participants.in_progress.each do |participant|
        Rails.logger.info(agent_id: user_id, chat: participant.chat) { 'user disconnected from chat by sign_out_agent' }
        ChatService::ProgressService.new(participant.chat).member_left(participant: participant)
      end
    end

    raise ServiceException, "Agent #{user_id} is in #{@user.status_name} status" if @user.offline? && !@user.logged_in?

    UserService.logout(user: @user, force_offline: true)

    render_user
  end

  def destroy
    UserService.delete(user: @user, performer: @current_user)

    UserUcaasDataService.delete_ucaas_data(@user)
    Ucaas::MsTeamsService.new.create_presence_subscription(URLHelper.base_url(request, remove_api: true))

    if @user.deleted?
      render_user
    else
      render json: {message: 'Unable to delete user, they are currently online'}, status: :conflict
    end
  end

  def resync
    case params[:channel]
    when 'call'
      @user.sync_update!(:call_queue_keys)
    when 'chat'
      @user.sync_update!(:chat_queue_keys)
    else
      @user.sync_update!(:all)
    end

    head :ok
  end

  def multiple_destroy
    user_ids = params.permit(user_ids: []).require(:user_ids)

    users = UserService.delete_all(user_ids: user_ids, performer: @current_user)

    UserUcaasDataService.delete_all_ucaas_data(users)
    Ucaas::MsTeamsService.new.create_presence_subscription(URLHelper.base_url(request, remove_api: true))

    users = users.index_by(&:id)
    deactivated_user_ids = []
    result = user_ids.map do |user_id|
      user = users[user_id.to_i]

      if user.nil?
        {user_id: user_id, status: 404, message: 'Not found'}
      elsif !user.deleted?
        {user_id: user_id, status: 409, message: 'Unable to delete user, they are currently online'}
      else
        deactivated_user_ids << user_id
        {user_id: user_id, status: 200, message: 'Success'}
      end
    end

    render json: result
  end

  def multiple_restore
    user_ids = params.permit(user_ids: []).require(:user_ids)

    users = UserService.restore_all(user_ids: user_ids)
    users = users.index_by(&:id)

    result = user_ids.map do |user_id|
      user = users[user_id.to_i]

      if user.nil?
        {user_id: user_id, status: 404, message: 'Not found'}
      elsif user.deleted?
        {user_id: user_id, status: 409, message: 'Unable to restore user: reason unknown'}
      else
        {user_id: user_id, status: 200, message: 'Success'}
      end
    end

    render json: result
  end

  def import_template
    data = UserService.import_template
    send_data(data, filename: 'import_users_template.csv', type: 'text/csv')
  end

  def import
    file = params.require(:file)
    # assume that it is UTF8 to resolve encoding problems of unicode characters : UJET-5875
    data = file.read.force_encoding(Encoding::UTF_8)
    result = UserService.import(data, file.original_filename)
    render json: result
  rescue UserService::ImportException => e
    render json: {message: e.message}, status: :bad_request
  end

  # triggered by the forgotten password email
  # TODO : use users#show?password_reset_code=xxx
  def reset_password_code
    # MUST check blank.
    user = User.find_by_password_reset_code params[:code] if params[:code].present?

    # TODO : define a meaningful exception and use it.
    raise ServiceException, 'Forgotten password code has been expired.' if user.blank?
    raise ServiceException, 'Reset password feature is disabled' unless reset_password_allowed?(user)

    render json: UserRepresenter.new(user)
  end

  # TODO : user passwords#reset POST /users/:id/password/reset
  # resest password from the fogotten password page
  def reset_password
    user = User.find_by_password_reset_code user_params.require(:password_reset_code)
    raise ServiceException, 'The code has been expired.' if user.blank?
    raise ServiceException, 'Reset password feature is disabled' unless reset_password_allowed?(user)

    user_params = params.require(:user)
    new_password = user_params.require(:password)
    raise ServiceException, 'New password should be different than the old password' if user.authenticate(new_password)

    user.update! user_params.permit(:password, :password_confirmation)
    user.expire_password_reset_code!

    render json: UserRepresenter.new(user)
  end

  ## Currently this end point is used for autocompletion
  def name_only
    scope = policy_scope(User)

    roles = params.permit(roles: []).fetch(:roles, nil)
    scope = scope.with_any_roles(*roles) if roles

    custom_permissions = params.permit(custom_permissions: []).fetch(:custom_permissions, nil)
    scope = scope.with_custom_permission(*custom_permissions) if custom_permissions

    scope = scope.select(:id, :first_name, :last_name, :email)

    render json: scope
  end

  def overview
    render json: RoleService::RoleOverview.call
  end

  def active_comms
    DatabaseReplica.using_read do
      render json: {
        calls: CallRepresenter.for_collection.prepare(
          user.call_participants.active.includes(:call).joins(:call).merge(Call.in_progress).map(&:call).uniq
        ),
        chats: ChatRepresenter.for_collection.prepare(
          user.chat_participants.active.includes(:chat).joins(:chat).merge(Chat.in_progress).map(&:chat).uniq
        )
      }
    end
  end

  private

  def require_user
    @user = policy_scope(User).with_deleted.find(params[:id])
  end

  def authorize_user
    # only the admin is allowed to use API
    authorize @user || User
  end

  def user_params
    params[:user]
  end

  def user_attrs_from_params
    user_params = params.require(:user)
    if user_params.present?
      user_params[:custom_roles] = nil unless user_params.key?(:custom_roles)
      user_params[:roles] = [] if user_params.key?(:roles) && user_params[:roles].nil?
      user_params[:permissions] = [] if user_params.key?(:permissions) && user_params[:permissions].nil?
      user_params.permit(policy(@user || User).permitted_attributes).to_h
    end
  end

  def ucaas_attr_from_params
    user_params = params.require(:user)

    return nil unless user_params.include?(:ucaas_data)

    enabled = ActiveModel::Type::Boolean.new.cast(user_params[:ucaas_data][:ucaas_user])

    if enabled
      {
        enabled: enabled,
        ucaas_user_name: user_params[:ucaas_data][:ucaas_user_name],
        ucaas_sip_uri: user_params[:ucaas_data][:ucaas_sip_uri]
      }
    else # if not enabled, remove the username and sip values
      {
        enabled: enabled
      }
    end
  end

  def stats_params
    params.permit(stats: [:stime, :etime]).to_h.deep_symbolize_keys
  end

  def exclude_params(default_exclude: [:company, :key, :last_login_time])
    _params = params.permit(exclude: [])
    _params[:exclude] = Array(_params[:exclude]).concat(default_exclude)
    _params[:exclude].map!(&:to_sym)
    _params[:exclude].push(:assignments) if _params[:exclude].delete(:channels)
    _params[:exclude].push(:team_assignments) if _params[:exclude].delete(:channels_from_teams)
    _params.to_h.deep_symbolize_keys
  end

  def legacy_roles_for_custom_role_ids(custom_role_ids)
    custom_role_id_to_classic_role = RoleService::Constants.ROLE_ATTRS.map { |attrs| [attrs[:id], attrs[:roles]] }.to_h
    custom_role_ids.map { |custom_role_id|
      custom_role_id_to_classic_role[Integer custom_role_id] or raise "Can't find custom role with role ID: #{custom_role_id}"
    }.reduce([], :|)
  end

  def render_user
    render json: UserRepresenter.new(@user).to_json(exclude_params)
  end

  def swizzle_custom_roles(users)
    RoleService::FasterCustomRoles.extend_users(users)
  end

  def user_settings_params(user_params)
    return {} if user_params.blank? || user_params[:user_setting].blank?

    user_params[:user_setting].present? ? user_params[:user_setting].permit(UserSettingPolicy.permitted_attributes).to_h : {}
  end
end

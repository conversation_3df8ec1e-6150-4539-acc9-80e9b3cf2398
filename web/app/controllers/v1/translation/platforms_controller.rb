class V1::Translation::PlatformsController < ApplicationController
  before_action :require_translation_platform, only: %i[show update validate destroy]
  before_action :authorize_translation_platform

  # GET /v1/translation/platforms
  def index
    @translation_platforms = TranslationPlatform.all
    render json: ::Translation::PlatformRepresenter.for_collection.prepare(@translation_platforms)
  end

  # GET /v1/translation/platforms/:id
  def show
    render json: ::Translation::PlatformRepresenter.new(@translation_platform)
  end

  # POST /v1/translation/platforms
  def create
    permitted_params_for_create = params.permit(:name, :platform_type)

    name = permitted_params_for_create.require(:name)
    platform_type = permitted_params_for_create.require(:platform_type)

    @translation_platform = TranslationPlatform.create!(
      name:          name,
      platform_type: platform_type || :google_cloud_translation
    )

    render json: ::Translation::PlatformRepresenter.new(@translation_platform)
  end

  # PATCH | PUT /v1/translation/platforms/:id
  def update
    permitted_params = params.permit(:name, :enabled)

    @translation_platform.update(permitted_params.to_h)

    render json: ::Translation::PlatformRepresenter.new(@translation_platform)
  end

  # DELETE /v1/translation/platforms/:id
  def destroy
    @translation_platform.destroy!
    head :ok
  end

  # POST /v1/translation/platforms/:id/validate
  def validate
    @translation_platform.validate_external_project!
    render json: ::Translation::PlatformRepresenter.new(@translation_platform)
  end

  private

  def authorize_translation_platform
    authorize TranslationPlatform, policy_class: Translation::PlatformPolicy
  end

  def require_translation_platform
    @translation_platform = TranslationPlatform.find(params[:id])
  end
end

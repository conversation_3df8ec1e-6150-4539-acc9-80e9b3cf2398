class V1::Translation::CredentialsController < ApplicationController
  before_action :require_translation_platform, only: %i[create]
  before_action :authorize_translation_platform

  # POST /v1/translation/platforms/:id/credentials
  def create
    credential = params[:credential]

    credential.rewind
    filename = credential.original_filename
    credential = credential.read

    validate_json!(credential)

    @credential = TranslationCredential.where(platform: @translation_platform).first_or_initialize
    @credential.update!(secret: credential, filename: filename)

    render json: { status: :ok }
  end

  private

  def require_translation_platform
    @translation_platform = TranslationPlatform.find(params[:id])
  end

  def authorize_translation_platform
    authorize TranslationPlatform, policy_class: Translation::PlatformPolicy
  end

  def validate_json!(data)
    JSON.parse(data)
  rescue JSON::ParserError, TypeError
    raise ServiceException, 'Invalid JSON format on service key'
  end
end

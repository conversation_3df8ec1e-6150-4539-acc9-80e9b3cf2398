class V1::Translation::ModelsController < ApplicationController
  before_action :authorize_translation_platform

  def index
    if params[:platform_id].present?
      translation_platform = TranslationPlatform.find(:platform_id)
      models = translation_platform.models
      glossaries = translation_platform.glossaries
    else
      models = TranslationModel.all
      glossaries = TranslationGlossary.all
    end

    render json: {
      models: ::Translation::ModelRepresenter.for_collection.prepare(models),
      glossaries: ::Translation::GlossaryRepresenter.for_collection.prepare(glossaries)
    }
  end

  private

  def authorize_translation_platform
    authorize TranslationModel, policy_class: Translation::ModelPolicy
  end
end

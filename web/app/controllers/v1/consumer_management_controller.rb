# frozen_string_literal: true

class V1::ConsumerManagementController < ApplicationController
  before_action :authorize_consumer_management

  def remove_data
    # split on comma or space
    identifiers = params.require(:identifiers).split(/[\s,]+/).map(&:strip)

    result = ConsumerManagementService.remove_data(identifiers: identifiers)

    render json: result
  end

  private

  def authorize_consumer_management
    authorize :consumer_management
  end
end

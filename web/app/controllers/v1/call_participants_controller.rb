class V1::CallParticipantsController < ApplicationController
  before_action :require_call

  def index
    participants = @call.participants.map { |p| CallParticipantRepresenter.new(p) }
    render json: participants
  end

  def create
    participant = service.create(
      phone_number: params[:phone_number],
      type: params[:type]
    )

    render json: CallParticipantRepresenter.new(participant)

  rescue Twilio::REST::RestError => e
    # Twilio Error and Warning Dictionary: https://www.twilio.com/docs/api/errors
    reason = Call::TwilioErrorMapping.convert(twilio_error_code: e.code.to_i)
    call_service = CallService::ProgressService.new(@call)
    call_service.fail_call(reason: reason, details: e.message)

    raise
  end

  def update
    participant = @call.participants.find(params[:id])

    if params.has_key?(:hold)
      service.hold(
        participant: participant,
        participant_placing_hold: @call.find_participant(type: :agent, identifier: current_user.id),
        hold: ActiveRecord::Type::Boolean.new.cast(params[:hold])
      )
    end

    if params.has_key?(:status)
      service.update_status(participant: participant, status: params[:status])
    end

    if params.has_key?(:client_muted)
      service.update_client_muted(
        participant: participant,
        client_muted: ActiveRecord::Type::Boolean.new.cast(params[:client_muted])
      )
    end

    render json: CallParticipantRepresenter.new(participant)
  end

  private

  def service
    @service ||= CallService::ParticipantService.new(call: @call)
  end

  def require_call
    @call = Call.find params[:call_id]
    RequestStore.store[:call] = @call
    authorize @call, :agent_request?
  end

end

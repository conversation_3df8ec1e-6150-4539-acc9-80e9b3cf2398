class V1::DirectAccessKeysController < ApplicationController
  before_action :require_direct_access_key, only: [:show, :update, :destroy]
  before_action :authorize_direct_access_key
  before_action :validate_direct_access_point, only: [:create, :update]

  def index
    @direct_access_keys = Menu::DirectAccessKey.in_lang(params[:lang])
                                               .in_type(params[:type])
                                               .in_menu_id(params[:menu_id])
                                               .in_menu_type(params[:menu_type])
                                               .in_language_level_setup(params[:language_level_setup])

    client_app_ids = @direct_access_keys.client_app.pluck(:id)
    mobile_apps = MobileApp.where(id: client_app_ids).index_by(&:id)

    render json: Menu::DirectAccessKeyRepresenter.for_collection.prepare(@direct_access_keys).to_json(user_options: {mobile_apps: mobile_apps})
  end

  def create
    @direct_access_key = Menu::DirectAccessKey.create!(permitted_params)
    render_menu_or_direct_access_key
  end

  def show
    render_menu_or_direct_access_key
  end

  def update
    @direct_access_key.update!(permitted_params)
    render_menu_or_direct_access_key
  end

  def destroy
    @direct_access_key.destroy
    render_menu_or_direct_access_key
  end

  private

  def permitted_params
    params.permit(:lang, :type, :name, :menu_type, :sub_type,
      :menu_id, :key, :crm_custom_user_segment_field,
      :greeting_message, :voice_file,
      :voice_file_data_uri, :use_voice_file,
      :api_response_matches)
  end

  def require_direct_access_key
    @direct_access_key = Menu::DirectAccessKey.find(params[:id])
  end

  def authorize_direct_access_key
    authorize @direct_access_key || Menu::DirectAccessKey
  end

  def representer_class
    "#{@direct_access_key.menu.class.name}Representer".constantize
  end

  def render_menu_or_direct_access_key
    if @direct_access_key.menu.present?
      render_menu
    else
      render_key
    end
  end

  def render_menu
    ## direct_access_key returns menu object with limited attributes
    render json: representer_class.new(@direct_access_key.menu).to_json(include: [:id, :parent_id, :position, :type, :direct_access_keys])
  end

  def render_key
    render json: Menu::DirectAccessKeyRepresenter.new(@direct_access_key).to_json
  end

  def validate_direct_access_point
    direct_inbound_number = PhoneNumberService::DirectInboundNumber.new(phone_number: permitted_params[:key])
    if direct_inbound_number.not_use_direct_access_key?(permitted_params, @direct_access_key)
      raise ServiceException, "The support phone number #{permitted_params[:key]} is a Direct Inbound Phone"
    end
  end
end

class V1::OutboundNumbersController < ApplicationController
  before_action :find_outbound_number, only: [:show, :update, :destroy, :verify]
  before_action :authorize_outbound_number

  around_action :use_read_replica, only: [:index]

  N11_CODE_PERMISSION_TYPE = 'n11_code'.freeze

  def index
    filter_params = params.permit(:n11_code, :unassigned, id: [])
    @outbound_numbers = filter_params[:id].present? ? OutboundNumber.with_user.where(id: filter_params[:id]) :  OutboundNumber.with_user.all

    if filter_params[:n11_code].present?
      @outbound_numbers = case filter_params[:n11_code]
                          when 'any'
                            @outbound_numbers.with_n11_codes
                          when 'emergency'
                            if ActiveRecord::Type::Boolean.new.cast(filter_params[:unassigned])
                              @outbound_numbers.with_emergency_n11_codes.with_verified_for_emergency.without_direct_access_keys_on_n11_service_callback
                            else
                              @outbound_numbers.with_emergency_n11_codes.with_verified_for_emergency
                            end
                          when 'service'
                            @outbound_numbers.with_service_n11_codes
                          when 'none'
                            @outbound_numbers.without_n11_codes
                          when 'all-but-emergency'
                            @outbound_numbers.without_emergency_n11_codes
                          else
                            @outbound_numbers
                          end
      @outbound_numbers = @outbound_numbers.distinct
    end

    filter_numbers

    if (params[:page] && params[:per_page]).present?
      @outbound_numbers = SimpleQuery.call(scoped: @outbound_numbers, args: params.permit(:page, :per_page).to_h)

      add_pagination(@outbound_numbers)
    end

    PhoneNumber.inject_assigned_queues(phone_numbers: @outbound_numbers)
    OutboundNumber::PermissionPreloaderService.inject_outbound_permissions(@outbound_numbers)

    render json: OutboundNumberRepresenter.for_collection.prepare(@outbound_numbers).to_json(user_options: {user: current_user})
  end

  def show
    PhoneNumber.inject_assigned_queues(phone_numbers: [@outbound_number])
    OutboundNumber::PermissionPreloaderService.inject_outbound_permissions([@outbound_number])
    render json: OutboundNumberRepresenter.new(@outbound_number).to_json(user_options: {user: current_user})
  end

  def create
    begin
      creator = ::PhoneNumberService::Creator.new(permitted_params.except(:permissions))

      ActiveRecord::Base.transaction do
        creator.call
        param_permissions = permitted_params[:permissions]

        if param_permissions.is_a?(Array) && param_permissions.present?
          n11_permissions, non_n11_permissions = param_permissions.partition { |permission| permission[:number_type] == N11_CODE_PERMISSION_TYPE }
          if parse_boolean(permitted_params[:has_outbound_restrictions]) && non_n11_permissions.present?
            OutboundNumber::PermissionService.create_multiple(creator.number.id, non_n11_permissions)
          end

          if n11_permissions.present?
            agent_permissions = filter_permissions(n11_permissions, true, 'agent')
            location_permissions = filter_permissions(n11_permissions, true, 'location')
            
            if parse_boolean(permitted_params[:has_n11_agent_restrictions])
              OutboundNumber::PermissionService.create_multiple(creator.number.id, agent_permissions)
            end
            if parse_boolean(permitted_params[:has_n11_location_restrictions])
              OutboundNumber::PermissionService.create_multiple(creator.number.id, location_permissions)
            end
          end
        end

        if permitted_params[:n11_special_code_ids].present?
          unless verify_special_codes?(permitted_params[:n11_special_code_ids])
            render json: { errors: "is not an special services number so cannot be added to the outbound number" }, status: :bad_request  
            return
          end

          creator.create_special_codes

          if param_permissions.is_a?(Array) && param_permissions.present?
            n11_permissions_result = param_permissions.select { |permission| permission[:number_type] == N11_CODE_PERMISSION_TYPE}
            special_code_agent_permission = filter_permissions(n11_permissions_result, false, 'agent')
            special_code_location_permission = filter_permissions(n11_permissions_result, false, 'location')

            if parse_boolean(permitted_params[:has_special_n11_agent_restrictions])
              OutboundNumber::PermissionService.create_multiple(creator.number.id, special_code_agent_permission)
            end

            if parse_boolean(permitted_params[:has_special_n11_location_restrictions])
              OutboundNumber::PermissionService.create_multiple(creator.number.id, special_code_location_permission)
            end
          end
        end
      end

      OutboundNumber::PermissionPreloaderService.inject_outbound_permissions([creator.number])
      render json: OutboundNumberRepresenter.new(creator.number).to_json(user_options: { user: current_user })
    rescue ActiveRecord::RecordInvalid => e
      render json: { errors: e.record.errors }, status: :bad_request
    rescue PhoneNumberService::Base::ValidationError => e
      render json: { errors: e.message }, status: :bad_request
    end
  end

  def update
    begin
      update_params = permitted_params.merge({ id: params[:id] }).except(:permissions)

      mutator = ::PhoneNumberService::Mutator.new(update_params)
      
      ActiveRecord::Base.transaction do
        mutator.call

        if permitted_params[:permissions].is_a?(Array)
          n11_permissions = permitted_params.to_h[:permissions]
                                            .select { |permission| permission[:number_type] == N11_CODE_PERMISSION_TYPE }

          agent_permissions = filter_permissions(n11_permissions, true, 'agent')
          location_permissions = filter_permissions(n11_permissions, true, 'location')      
          if parse_boolean(permitted_params[:has_n11_agent_restrictions])
            OutboundNumber::PermissionService.upsert(@outbound_number, agent_permissions,
                                                    type: N11_CODE_PERMISSION_TYPE,
                                                    permission_scope: 'agent')
          end
          if parse_boolean(permitted_params[:has_n11_location_restrictions])
            OutboundNumber::PermissionService.upsert(@outbound_number, location_permissions,
                                                    type: N11_CODE_PERMISSION_TYPE,
                                                    permission_scope: 'location')
          end
        end

        if permitted_params[:n11_special_code_ids].present? && !verify_special_codes?(permitted_params[:n11_special_code_ids])
          render json: { errors: "is not an special services number so cannot be added to the outbound number" }, status: :ok
          return
        end
        
        mutator.update_special_codes
        if permitted_params[:permissions].is_a?(Array)
          n11_permissions_result = permitted_params.to_h[:permissions]
                                            .select { |permission| permission[:number_type] == N11_CODE_PERMISSION_TYPE }
          special_code_agent_permission = filter_permissions(n11_permissions_result, false, 'agent')
          special_code_location_permission = filter_permissions(n11_permissions_result, false, 'location')

          if special_code_agent_permission.present?
            OutboundNumber::PermissionService.upsert_special_codes(@outbound_number, special_code_agent_permission,
                                                    type: N11_CODE_PERMISSION_TYPE,
                                                    permission_scope: 'agent')
          end

          if special_code_location_permission.present?
            OutboundNumber::PermissionService.upsert_special_codes(@outbound_number, special_code_location_permission,
                                                    type: N11_CODE_PERMISSION_TYPE,
                                                    permission_scope: 'location')
          end
        end
      end
      @outbound_number.reload
      OutboundNumber::PermissionPreloaderService.inject_outbound_permissions([@outbound_number])
      render json: OutboundNumberRepresenter.new(@outbound_number).to_json(user_options: {user: current_user})
    rescue ActiveModel::ValidationError => e
      render json: { errors: e.model.errors }, status: :bad_request
    rescue ActiveRecord::RecordInvalid => e
      render json: { errors: e.record.errors }, status: :bad_request
    rescue PhoneNumberService::Base::ValidationError => e
      render json: { errors: e.message }, status: :bad_request
    end
  end

  def destroy
    if @outbound_number.destroy
      head :ok
    else
      render json: {errors: @outbound_number.errors}, status: :bad_request
    end
  end

  def summary
    render json: OutboundNumber.summary
  end

  def verify
    verifier = NumberVerifierFactory.create(@outbound_number.number)
    verify_result = verifier.verify
    if @outbound_number.verified_for_emergency?
      OutboundNumberService.verify_emergency_number(number: @outbound_number, verifier:)
    end

    if verify_result.valid?
      @outbound_number.update_verified_channels(verify_result)
      @outbound_number.save!
      PhoneNumber.inject_assigned_queues(phone_numbers: [@outbound_number])
      render json: OutboundNumberRepresenter.new(@outbound_number).to_json(user_options: {user: current_user})
    else
      render json: {errors: verifier.errors}, status: :bad_request
    end
  end

  def default_numbers
    outbound_numbers = OutboundNumberFinder.find_for_all_nodes(lang: params[:lang]).values
    render json: OutboundNumberRepresenter.for_collection.prepare(outbound_numbers).to_json(user_options: {user: current_user})
  end

  private

  def find_outbound_number
    @outbound_number = OutboundNumber.find(params[:id])
  end

  def authorize_outbound_number
    authorize @outbound_number || OutboundNumber
  end

  def permitted_params
    @permitted_params ||= begin
                            keys = [
                              :label,
                              :number,
                              :global,
                              :number_type,
                              :has_outbound_restrictions,
                              :has_n11_agent_restrictions,
                              :has_n11_location_restrictions,
                              :long_code_default_permission,
                              :short_code_default_permission,
                              :has_special_n11_agent_restrictions,
                              :has_special_n11_location_restrictions,
                              { n11_code_ids: [] },
                              { n11_special_code_ids: [] },
                              :is_direct_inbound_number,
                              :user_id,
                              :concurrency_enabled,
                              :brand_name,
                              { permissions: [
                                :id,
                                :number_type,
                                :permission_type,
                                :is_delete,
                                :permission_scope,
                                :assignment_type,
                                :country_code,
                                :phone_number,
                                :user_id,
                                { assignments: [
                                  :assignee_id,
                                  :assignee_type
                                ] }
                              ] }
                            ]
                            keys << :verified_for_calls if manual_verification_allowed?
                            keys << :verified_for_sms if manual_verification_allowed?

                            params.permit(*keys)
                          end
  end

  def manual_verification_allowed?
    FeatureFlag.on?('no-phone-number-verification')
  end

  def require_verification?
    OutboundNumber.require_verification?
  end

  def need_to_verify_number?
    return false if permitted_params[:number].blank? # no number update submitted
    return false if @outbound_number.sanitized_number == PhoneNumber.sanitize_number(permitted_params[:number]) # number has not changed
    return false if not @outbound_number.verified_channels?
    true
  end

  def filters?
    params.values_at(:verified, :phone_number, :label, :assigned_queue, :global, :direct_inbound).any?
  end

  def filter_numbers
    return unless filters?

    if params[:verified].present?
      raise InvalidParameterException, 'Invalid verified parameter' unless [ 'call', 'sms', 'whatsapp' ].include?(params[:verified])
      if params[:verified] == 'whatsapp'
        raise InvalidParameterException, 'whats-app is unavailable' if Messaging::Channel.feature_flag_off?(:whatsapp)
      end
      channel = params[:verified].to_sym

      @outbound_numbers = @outbound_numbers.verified_for(channel)
    elsif params[:phone_number].present?
      # Remove spaces, - , +
      sanitized_number = params[:phone_number].gsub(/[^\d]/, '')
      @outbound_numbers = @outbound_numbers.for_sanitized_number(sanitized_number)
    elsif params[:label].present?
      @outbound_numbers = @outbound_numbers.for_label(params[:label])
    elsif params[:assigned_queue].present?
      @outbound_numbers = PhoneNumber.assigned(direction: :outbound, lang: params[:lang], assigned_queue: params[:assigned_queue])
    elsif params[:global].present?
      # Only allow true, false for global parameter to avoid sql injection
      return unless ['true', 'false'].include? params[:global]
      @outbound_numbers = @outbound_numbers.for_global(params[:global] == 'true')
    elsif params[:direct_inbound].present?
      return unless ['true', 'false'].include? params[:direct_inbound]
      @outbound_numbers = @outbound_numbers.for_direct_inbound(params[:direct_inbound] == 'true')
    end
  end

  def filter_permissions(permissions, emergency, permission_scope)
    permissions_result = permissions.map do |permission|
      if N11Code.find_by(code: permission[:phone_number])&.emergency == emergency && permission[:permission_scope] == permission_scope
        permission
      end
    end.compact
    permissions_result
  end

  def parse_boolean(value)
    ActiveRecord::Type::Boolean.new.cast(value)
  end

  # Verify that N11 Codes are selected as Special Services
  def verify_special_codes?(n11_special_code_ids)
    N11Code.exists?(id: n11_special_code_ids, emergency: true) ? false : true
  end
end

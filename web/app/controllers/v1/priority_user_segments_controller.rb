# frozen_string_literal: true

class V1::PriorityUserSegmentsController < ApplicationController
  before_action :required_user_segment, only: %i[update destroy]
  before_action :authorize_user_segment

  def index
    user_segments = PriorityUserSegment.all
    render json: PriorityUserSegmentRepresenter.for_collection.prepare(user_segments)
  end

  def create
    @user_segment = PriorityUserSegment.create!(permitted_params)
    render_user_segment
  rescue ActiveRecord::RecordNotUnique
    render json: { message: 'The field and value set has already been taken' }, status: :bad_request
  end

  def update
    @user_segment.update!(permitted_params)
    render_user_segment
  end

  def destroy
    @user_segment.destroy
    render_user_segment
  end

  private

  def authorize_user_segment
    authorize @user_segment || PriorityUserSegment
  end

  def permitted_params
    params.require(:priority_user_segment).permit(:crm_field, :value)
  end

  def required_user_segment
    @user_segment = PriorityUserSegment.find(params.require(:id))
  end

  def render_user_segment
    render json: PriorityUserSegmentRepresenter.new(@user_segment)
  end
end

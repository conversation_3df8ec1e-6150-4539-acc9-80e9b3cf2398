class V1::Notifications::AlertsController < ApplicationController
  before_action :require_alert, only: [:update]
  before_action :authorize_alert

  def index
    alerts = current_user.alerts.includes(:rule)
                         .where(dismissed_at: nil)
                         .page(params[:page]).per(params[:per_page])

    add_pagination(alerts)
    render json: Notification::AlertRepresenter.for_collection.prepare(alerts)
  end

  def update
    if ActiveRecord::Type::Boolean.new.cast(permitted_params[:dismiss])
      @alert.dismiss!

      # remove the alert from firebase
      key = "users/#{@alert.receiver.id}/notifications/#{@alert.id}"
      FirebaseWorker.perform_async(:delete, key, nil)
    end

    render json: Notification::AlertRepresenter.new(@alert)
  end

  private

  def require_alert
    @alert = Notification::Alert.find(params[:id])
  end

  def authorize_alert
    authorize @alert || Notification::Alert, policy_class: Notification::AlertPolicy
  end

  def permitted_params
    params.require(:alert).permit(:dismiss)
  end
end

class V1::Notifications::RulesController < ApplicationController
  before_action :require_rule, only: [:update, :destroy]
  before_action :authorize_rule

  def index
    rules = Notification::Rule.all
    render json: Notification::RuleRepresenter.for_collection.prepare(rules)
  end

  def create
    params = permitted_params.merge({ owner_id: current_user.id })
    @rule = Notification::Rule.class_for_type(params[:subject_type]).create!(params)
    render_rule
  end

  def update
    @rule.update!(permitted_params)

    # to reflect the change of subject_type
    @rule = Notification::Rule.find(params[:id])
    render_rule
  end

  def destroy
    @rule.destroy
    render_rule
  end

  private

  def require_rule
    @rule = Notification::Rule.find(params[:id])
  end

  def authorize_rule
    authorize Notification::Rule
  end

  def render_rule
    render json: Notification::RuleRepresenter.new(@rule)
  end

  def permitted_params
    params.require(:rule).permit(:subject_type, :subject_id, :metric,
                                 :metric_param, :metric_target, :operator, :time_frame, :receiver_type, #:receiver_ids,
                                 receiver_ids: [user_ids: [], team_ids: []])
  end
end

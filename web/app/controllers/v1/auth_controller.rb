class V1::AuthController < ApplicationController
  prepend_before_action :verify_authenticity_token, only: [:sign_in, :forgot_password]
  skip_before_action :authenticate, only: [:sign_in, :sign_out, :forgot_password, :login_policy]
  before_action :validate_company

  def sign_in
    service = AuthService.new
    agent = service.target_user email: user_params[:username],
                                password: user_params[:password],
                                id_token: user_params[:id_token],
                                access_token: user_params[:access_token]

    raise AuthException, AuthService::ERROR_CODES[:general] if agent.blank?
    raise AuthException, AuthService::ERROR_CODES[:account_locked] if Company.current.locked? && !agent.ujet?

    token = authenticate_user(agent)

    auth.set_authorization_cookie(token: token)
    log_proxy_token = set_new_log_proxy_cookie(agent)
    set_new_presence_cookie(agent)

    render json: generate_auth_status(auth_token: token, log_proxy_token:)
  rescue AuthException => e
    render json: {message: e.message, code: service.get_error_code(e.message)}, status: :unauthorized
  end

  def sign_out
    begin
      # do authenticate to find out current_user.
      # but ignore exceptions, want to return 200 all the time.
      authenticate

      UserService.logout(user: current_user)
    rescue UnauthorizedException
      # auth error can be safely ignored
    rescue StandardError => e
      Rails.logger.error("error while signing out: #{e}")
    end

    auth.delete_authorization_cookie

    head :ok
  end

  def status
    current_user.update_signed_request(salesforce_hash_key: params[:hash_key])

    token = auth.authenticated_token
    token = current_user.generate_auth_token if AuthToken.need_refresh(token)

    auth.set_authorization_cookie(token: token)
    log_proxy_token = refresh_expiring_log_proxy_token
    refresh_expiring_presence_token

    response.headers["Cache-Control"] = "no-cache, no-store"
    response.headers["Pragma"] = "no-cache"
    render json: generate_auth_status(log_proxy_token:)
  end

  def forgot_password
    user = User.find_by_email params[:email]
    if user.present? && !user.invited?
      raise ServiceException, 'Reset password feature is disabled' unless reset_password_allowed?(user)

      user.start_reset_password
    end
    render json: {}
  end

  def login_policy
    render json: render_login_policy
  end

  protected

  def should_verify_authorized
    false
  end

  private


  def user_params
    params[:user]
  end

  def render_login_policy
    {
      sso_enabled: current_company&.login_policy_settings&.sso_enabled,
      policy: current_company&.login_policy_settings&.policy,
      saml_forced: current_company&.login_policy_settings&.saml_forced,
      password_auth_enabled: current_company&.login_policy_settings&.password_auth_enabled,
      domain: current_company&.login_policy_settings&.domain,
      client_id: current_company&.login_policy_settings&.client_id
    }
  end

  def generate_auth_status(auth_token: nil, log_proxy_token: nil)
    firebase_config = FirebaseClient.firebase_config(current_user)
    {
      user: user_data,
      token: auth_token,
      firebase_token: firebase_config[:token],
      firebase_config: firebase_config
    }.tap do |hash|
      hash[:log_proxy_url] = auth.generate_log_proxy_url if Rails.configuration.env[:ujet][:log_proxy_enabled]
      hash[:log_proxy_auth_token] = log_proxy_token if log_proxy_token
      hash[:presence_ws_url] = auth.presence_ws_url if PresenceService.enabled?
    end
  end

  def user_data
    # reload user with associations to prevent N+1 query problem
    scope = policy_scope(User)
    user = UserLoader.find(scope, current_user.id, exclude: [:stats])
    AgentRepresenter.for_user(current_user).new(user)
  end

  def validate_company
    raise UnauthorizedException, 'Not Authorized' if Company.current.nil?
  end

  def refresh_expiring_presence_token
    return unless PresenceService.enabled?
    return unless AuthToken.presence_token_need_refresh?(auth.presence_service_cookie)

    set_new_presence_cookie(current_user)
  end

  def set_new_presence_cookie(user)
    return unless PresenceService.enabled?

    auth.set_presence_service_cookie(token: user.generate_presence_auth_token)
  end

  def refresh_expiring_log_proxy_token
    return unless Rails.configuration.env[:ujet][:log_proxy_enabled]

    token = auth.log_proxy_authorization_cookie
    token = set_new_log_proxy_cookie(current_user) if AuthToken.log_proxy_token_need_refresh?(token)
    token
  end

  def set_new_log_proxy_cookie(user)
    return unless Rails.configuration.env[:ujet][:log_proxy_enabled]
    token = user.generate_log_proxy_auth_token
    auth.set_log_proxy_authorization_cookie(token: token)
    token
  end
end

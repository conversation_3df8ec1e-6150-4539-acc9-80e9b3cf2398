# frozen_string_literal: true

class V1::BargeController < ApplicationController
  attr_reader :call, :chat, :comm

  before_action :require_comm
  before_action :authorize_barge

  def barge_call
    barge

    render json: {}
  end

  def barge_chat
    barge

    render json: {}
  end

  private

  def barge
    BargeService.new(barger: current_user, comm:).barge
  end

  def require_comm
    case request.path
    when %r{/calls/}
      @comm = @call = Call.includes_menus_with_settings.find(params[:id])
    when %r{/chats/}
      @comm = @chat = Chat.includes_menus_with_settings.find(params[:id])
    else
      render_not_found
    end
  end

  def authorize_barge
    authorize comm, policy_class: ::BargePolicy
  end

  def render_not_found
    render json: { message: 'Not Found.' }, status: :not_found
  end
end

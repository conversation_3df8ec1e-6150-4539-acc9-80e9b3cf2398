class V1::TwilioIpmController < ApplicationController
  skip_before_action :authenticate, only: [:callback]
  before_action :request_validation

  def callback
    case params[:EventType]
    when webhook_names[:events][:message_send]
      validate_message if message_validation_enabled?

    when webhook_names[:events][:message_sent]
      chat_callback_service.message_sent

    when webhook_names[:events][:member_removed]
      chat_callback_service.member_removed

    when webhook_names[:events][:member_added]
      chat_callback_service.member_added

    end
    head :ok
  end

  private

  def should_verify_authorized
    false
  end

  def chat_callback_service
    @chat_callback_service ||= ::TwilioService::ChatCallback.new(params)
  end

  # [UJET-18546] authenticate twilio request. block invalid twilio request
  def request_validation
    auth_token = Company.current.twilio_auth_token(region)
    validator = Twilio::Security::RequestValidator.new(auth_token)
    twilio_signature = request.headers['HTTP_X_TWILIO_SIGNATURE']
    original_url = request.original_url

    unless validator.validate(original_url, request.request_parameters, twilio_signature)
      logger.warn(data: { action: params[:action] }) { '[Twilio] twilio request is NOT valid.' }

      raise ServiceException, 'Twilio request validation failure' if FeatureFlag.on?('twilio-request-validation')
    end
  end

  def validate_message
    return if from_param == 'system'

    body = begin
      JSON.parse(params.require(:Body))
    rescue StandardError
      nil
    end

    raise ServiceException, 'Invalid message format' unless body.present?

    case body['type']
    when 'text'
      raise ServiceException, 'Text content not found' if body['content'].blank?

      test_message_error(body) if message_error_test_enabled?
    when 'photo'
      raise ServiceException, 'Photo Id not found' if body['media_id'].blank? && body['url'].blank?
    when 'file'
      nil
    when 'video'
      raise ServiceException, 'Video Id not found' if body['media_id'].blank?
    else
      raise ServiceException, 'Invalid message type'
    end
  end

  def message_validation_enabled?
    check_string_truthy(Rails.configuration.env[:chat][:message_validation])
  end

  def message_error_test_enabled?
    check_string_truthy(Rails.configuration.env[:chat][:message_error_test])
  end

  def test_message_error(message_body)
    raise ServiceException, 'message error test' if message_body['content'].start_with?('!error') && (Random.rand < 0.5)
  end

  def check_string_truthy(value)
    !check_string_falsy(value)
  end

  def check_string_falsy(value)
    value.blank? or value =~ /^(false|f|no|n|0)$/i
  end

  def from_param
    params[webhook_names[:params][:from]]
  end

  def channel_sid
    @channel_sid = params[:ChannelSid] || params[:ConversationSid]
  end

  def webhook_names
    ::TwilioService::ConversationsClient.webhook_names
  end

  def region
    request.headers['HTTP_X_HOME_REGION'] || ::TwilioService::Regions::GLOBAL_DEFAULT
  end
end

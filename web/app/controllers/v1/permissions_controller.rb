# frozen_string_literal: true

require 'csv'

module V1
  class PermissionsController < ApplicationController
    before_action :authorize_permission
    around_action :use_read_replica, only: [:index]

    def index
      render json: RoleService::Permissions.permissions_for_endpoint
    end

    def csv
      roles = %i[agent admin manager manager_admin manager_data manager_team manager_team_limited developer]
      operations = %i[view edit]

      header = [(%w[Section Page Feature] + (roles.map do |role|
        operations.map { |operation| "#{role.to_s.titleize.gsub(/ /, '-')} (#{operation.capitalize} All)" }
      end).flatten).to_csv]

      content = RoleService::Permissions::DESCRIPTIONS.map do |description|
        ([
          description[:section],
          description[:page],
          description[:feature].nil? ? '' : description[:feature]
        ] + roles.map do |role|
          operations.map do |operation|
            next 'N/A' if description[:permissions].keys.exclude?(operation)

            possible_scope_dictionaries = (description.dig(:default_roles_with_permission, role, operation) || [])
            picked_scope_dictionary = possible_scope_dictionaries.find do |x|
              (x.dig(:feature_flags) || []).all? { |flag, value| FeatureFlag.on?(flag) == value }
            end
            case (picked_scope_dictionary || {}).dig(:scopes)
            when nil
              ''
            when {}
              'X'
            when { 'restrict_to_teams' => true }
              'X (Team)'
            else
              raise ServiceException, "Found permission scope that couldn't be understood"
            end
          end
        end.flatten).to_csv
      end

      render inline: (header + content).join(''), content_type: 'text/csv'
    end

    def outdated
      template_roles = fetch_roles(from_db: false)
      db_roles = fetch_roles(from_db: true)

      if template_roles != db_roles
        render json: {
          status: 'outdated',
          roles: {
            not_in_template: db_roles - template_roles,
            not_in_db: template_roles - db_roles
          }
        }
      else
        template_permissions = fetch_permissions(roles: template_roles, from_db: false)
        db_permissions = fetch_permissions(roles: db_roles, from_db: true)

        if template_permissions != db_permissions
          render json: {
            status: 'outdated',
            permissions: {
              not_in_template: db_permissions - template_permissions,
              not_in_db: template_permissions - db_permissions
            }
          }
        else
          render json: { status: 'OK' }
        end
      end
    end

    private

    def fetch_roles(from_db:)
      if from_db
        CustomRole.where(builtin: true).as_json.map do |r|
          r.symbolize_keys
        end
      else
        RoleService::Constants.ROLE_ATTRS.dup
      end.map { |r| r.except(:authlevel).except(:position).except(:deleted_at) }.sort_by { |t| t[:id] }
    end

    def fetch_permissions(roles:, from_db:)
      if from_db
        CustomRole.where(builtin: true).map do |r|
          r.custom_permissions.dup.map { |p| p.merge(role: r.roles[0]) }
        end.flatten
      else
        roles.map do |r|
          RoleService::Permissions.for_builtin_roles[r[:roles][0]].dup.map { |p| p.merge(role: r[:roles][0]) }
        end.flatten.map do |r|
          r[:permission] = r[:permission].to_sym
          r
        end
      end
    end

    def authorize_permission
      authorize :permission
    end
  end
end

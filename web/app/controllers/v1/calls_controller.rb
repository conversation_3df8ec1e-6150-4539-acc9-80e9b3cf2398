class V1::CallsController < ApplicationController

  class SummaryRepresenter < CallRepresenter
    include SummarizableRepresenter
  end

  around_action :use_dashboard_replica, only: [:index, :previous, :show]
  before_action :require_call, only: [:show, :observe, :force_end, :unobserve]
  before_action :authorize_call

  def index
    index_with_options(time_column: :created_at)
  end

  def previous
    index_with_options(time_column: :ends_at)
  end

  def show
    render_call
  end

  # Create an observer participant and return a token for the observer
  def observe
    unless @call.connected?
      raise ServiceException, "Call is not connected"
    end

    participant = ObserveService.new(current_user, @call).observe

    data = CallService::VoipProviderFactory.create(call: @call).generate_token(participant: participant)
    data[:observer] = ::AgentApi::CallObserverRepresenter.new(participant)
    render json: data
  end

  def unobserve
    ObserveService.new(current_user, @call).unobserve
    head :ok
  end

  def flush_voicemails
    render json: { affected_count: CallService::VoicemailService.flush_all! }
  end

  def force_end
    raise ServiceException, 'Call already ended' if @call.ended?

    CallService::ProgressService.new(@call).force_end_call(by_user: current_user)
    head :ok
  end

  private

  def index_with_options(additional_options)
    options = params.permit(:from, :to, :manager_id, :device_id, :end_user_id, :lang,
      :in_progress, :has_transfer, :repeated, :page, :per_page, :search_text, :sort_direction, :sort_column,
      type: [], status: [], fail_reason: [], menus: [], agents: [], teams: [], includes: []
    ).to_h

    # Set the accessibility of the current_user
    scope = policy_scope(Call.all, options[:status])
    scope = scope.not_shortly_abandoned.check_hide_eu_in_menu_abandoned
    calls = CallService::Base.search(scope, options.merge(additional_options))
    add_pagination(calls)
    render json: CallRepresenter.for_collection.prepare(calls)
  end

  def render_call
    if @call
      render json: SummaryRepresenter.for_user(current_user).new(@call)
    else
      render json: nil
    end
  end

  def require_call
    @call = Call.includes_menus_with_settings.find params[:id]
    RequestStore.store[:call] = @call
  end

  def authorize_call
    authorize @call || Call
  end

  def policy_scope(scope, statuses)
    CallPolicy::Scope.new(current_user, scope, statuses).resolve
  end
end

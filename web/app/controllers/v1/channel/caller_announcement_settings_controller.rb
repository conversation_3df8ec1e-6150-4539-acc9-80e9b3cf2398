class V1::Channel::CallerAnnouncementSettingsController < ApplicationController
  include MenuChannelSetable

  private

  def resource_class
    Menu::VoiceCallSetting
  end

  def setting_params
    params.permit(policy(resource_class).permitted_attributes)
  end

  def render_channel_setting
    setting_finder = CallerAnnouncement::SettingFinder.new
    render json: { id: @channel_setting.id, menu_id: @channel_setting.menu_id, lang: @channel_setting.lang,
                   channel_setting: setting_finder.find_for_menu(menu_id: @channel_setting.menu_id, lang: @channel_setting.lang) }
  end
end

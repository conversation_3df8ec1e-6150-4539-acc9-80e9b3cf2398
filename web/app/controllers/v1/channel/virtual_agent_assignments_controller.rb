# frozen_string_literal: true

class V1::Channel::VirtualAgentAssignmentsController < ApplicationController
  before_action :require_channel_setting
  before_action :require_virtual_agent_assignment, only: %i[show update destroy]
  before_action :authorize_virtual_agent_assignment

  # when channel_setting is Menu::ChatSetting
  # GET /v1/channel/chat_settings/:chat_setting_id/virtual_agent_assignment
  def show
    render_virtual_agent_assignment
  end

  # update virtual agent assignment attributes
  # when channel_setting is Menu::ChatSetting
  # PUT/PATCH /v1/channel/chat_settings/:chat_setting_id/virtual_agent_assignment
  def update
    permitted_params = params.permit(
      :virtual_agent_id,
      :operation_hour_option,
      :allow_skip_virtual_agent,
      :skip_virtual_agent_ivr_option,
      :skip_user_segment_enabled,
      :skip_user_segment_field,
      :skip_user_segment_value,
      :data_parameters_enabled,
      :send_only_transfers_to_va_enabled,
      :send_only_dap_inquiry_enabled,
      direct_access_key_ids: [],
      data_parameters: [:field, :type, :value, :source, :source_field],
      data_records: [:in_crm_record, :in_metadata]
    )

    ::Chatbot::VirtualAgentChannelAssignmentService.update(
      virtual_agent_assignment: @virtual_agent_assignment,
      attrs: permitted_params.to_h
    )

    render_virtual_agent_assignment
  end

  private

  def authorize_virtual_agent_assignment
    authorize @virtual_agent_assignment
  end

  def require_channel_setting
    channel_setting
  end

  def require_virtual_agent_assignment
    @virtual_agent_assignment ||= channel_setting.virtual_agent_assignment
  end

  def channel_setting
    @channel_setting ||= if params[:voice_call_setting_id]
                           Menu::VoiceCallSetting.find(params[:voice_call_setting_id])
                         elsif params[:chat_setting_id]
                           Menu::ChatSetting.find(params[:chat_setting_id])
                         else
                           raise ServiceException, "Invalid channel"
                         end
  end

  def render_virtual_agent_assignment
    render json: Menu::VirtualAgentAssignmentRepresenter.new(@virtual_agent_assignment)
  end
end

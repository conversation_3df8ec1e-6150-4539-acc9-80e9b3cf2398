# frozen_string_literal: true

class V1::Channel::EmailChannelSettingsController < ApplicationController
  include MenuChannelSetable
  include ValidateAttachmentsBeforeUpload
  before_action :validate_attachments!, only: [:update_auto_response_settings]

  MAX_AMOUNT_OF_ATTACHMENTS = 10
  MAX_SIZE_OF_ATTACHMENTS = 25.megabytes

  def index
    lang = params.require(:lang)
    menu_id = params.require(:menu_id)
    @channel_settings = resource_scope.includes(:auto_response_attachments).where(lang: lang, menu_id: menu_id)

    before_render_channel_settings(@channel_settings)
    render json: channel_setting_representer.for_collection.prepare(@channel_settings)
  end

  # PATCH /v1/channel/email_channel_settings/:email_channel_setting_id/auto_reponse_settings
  def update_auto_response_settings
    permitted_params = permit_params_auto_response
    auto_response_payload = audible_translation_data

    email_channel_setting_id = params[:id]
    email_support_common_service = EmailAdapter::EmailSupportCommonService.new(email_channel_setting_id:)
    data_ids_for_deletion = email_support_common_service.auto_response_data_validation(@channel_setting, auto_response_payload, permitted_params)

    Menu::EmailChannelSetting.transaction do
      # delete attachments
      if permitted_params[:remove_attachments].present?
        @channel_setting.auto_response_attachments.where(id: permitted_params[:remove_attachments]).destroy_all
      end

      if data_ids_for_deletion.present?
        @channel_setting.auto_response_attachments.where(data_id: data_ids_for_deletion).destroy_all
      end

      @channel_setting.auto_response_enabled = permitted_params[:enabled]

      message_body = auto_response_payload&.dig('text') || ''
      if message_body.present?
        email_embedded_image_upload = EmailService::EmailAutoResponseEmbeddedImageUploadService.new(@channel_setting, message_body)
        auto_response_payload['text'] = email_embedded_image_upload.auto_response_embedded_image_upload
        # upsert audible_translation data
        audible_translation_upsert(auto_response_payload)
      end

      # create new attachments
      if permitted_params[:attachments].present?
        attachments = permitted_params[:attachments]&.map { |value| { 'file' => value, 'file_size' => value.tempfile.size } }
        @channel_setting.auto_response_attachments.create!(attachments)
      end

      if permitted_params[:sender_email_account_id].present?
        @channel_setting.sender_email_account_id = permitted_params[:sender_email_account_id]
      end

      @channel_setting.save!
    end

    if @channel_setting.invalid?
      render json: { errors: @channel_setting.errors.full_messages },
             status: bad_request and return
    end

    @channel_setting.auto_response_attachments.reload # reload to make sure auto response attachments are up to date
    render_channel_setting
  end

  private

  def require_channel_setting
    @channel_setting = resource_scope.includes(:auto_response_attachments).find(params[:id])
  end

  def resource_class
    Menu::EmailChannelSetting
  end

  def setting_params
    params.permit(policy(resource_class).permitted_attributes)
  end

  def channel_setting_representer
    Menu::EmailChannelSettingRepresenter
  end

  def authorize_channel_setting
    authorize :email_channel_setting
  end

  def before_render_channel_settings(channel_settings)
    channel_settings.each(&:load_assigned_agents_count)
  end

  def before_render_channel_setting(channel_setting)
    channel_setting.load_assigned_agents_count
  end

  def permit_params_auto_response
    params.require(:enabled)
    params.permit(:enabled, :sender_email_account_id, attachments: [], remove_attachments: [])
  end

  def validate_attachments!
    permitted_params = permit_params_auto_response
    return if permitted_params[:attachments].blank?

    attachments = permitted_params[:attachments]
    validate_attachments_ext!(attachments: attachments)
  end

  def audible_translation_data
    return unless params[:auto_response_message]

    parse_data = JSON.parse(params[:auto_response_message])
    parse_data.each do |key, attributes|
      parse_data[key] = attributes
    end
  end

  def audible_translation_upsert(auto_response_payload)
    translation = Audible::Translation.find_or_initialize_by(
      menu_id: auto_response_payload['menuId'],
      key: auto_response_payload['key']
    )

    translation.update(
      lang: auto_response_payload['lang'],
      type: auto_response_payload['type'],
      text: auto_response_payload['text']
    )
  end
end

# frozen_string_literal: true

class V1::Channel::ExternalDeflectionLinkSettingsController < ApplicationController
  include MenuChannelSetable

  private

  def resource_class
    Menu::ExternalDeflectionLinkSetting
  end

  def setting_params
    params.permit(:enabled, external_deflection_link_ids: [])
  end

  def channel_setting_representer
    Menu::ExternalDeflectionLinkSettingRepresenter
  end

  def handle_record_not_found_on_update(exception)
    return false unless exception.model == 'ExternalDeflectionLink'

    raise ServiceException,
          "Invalid external_deflection_link_ids list: #{params['external_deflection_link_ids'].join(', ')}"
  end

  def authorize_channel_setting
    authorize :external_deflection_link_setting
  end
end

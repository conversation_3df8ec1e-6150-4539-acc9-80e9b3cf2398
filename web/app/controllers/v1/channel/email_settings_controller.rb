# frozen_string_literal: true

# Deprecated. replaced by Channel::EmailChannelSettingsController
class V1::Channel::EmailSettingsController < ApplicationController
  include MenuChannelSetable

  private

  def resource_class
    Menu::EmailSetting
  end

  def setting_params
    params.permit(:enabled, :email, :recaptcha_enabled, :instruction_message)
  end

  def channel_setting_representer
    Menu::EmailSettingRepresenter
  end

  private

  def authorize_channel_setting
    authorize :email_setting
  end
end

class V1::Channel::VoiceCallSettingsController < ApplicationController
  include MenuChannelSetable

  CALL_RECORDING_STATUS = {
    do_not_record: 'record_never',
    record: 'record_always'
  }.freeze

  before_action :validate_virtual_agent_assignment, only: [:update]

  private

  def resource_class
    Menu::VoiceCallSetting
  end

  def resource_scope
    return super unless FeatureFlag.on?('voicebot')

    super.includes(menu: { virtual_agent_assignments: :virtual_agent })
  end

  def setting_params
    setting_params = params.permit(policy(resource_class).permitted_attributes)
    enhance_setting_params!(setting_params)
  end

  def channel_setting_representer
    Menu::VoiceCallSettingRepresenter
  end

  def before_render_channel_settings(channel_settings)
    channel_settings.each(&:load_assigned_agents_count)
    channel_settings.each(&:load_assigned_virtual_agents_count)
  end

  def before_render_channel_setting(channel_setting)
    channel_setting.load_assigned_agents_count
    channel_setting.load_assigned_virtual_agents_count
  end

  def enhance_setting_params!(setting_params)
    recording_option = setting_params[:recording_option]
    return setting_params if recording_option.nil? || recording_option == @channel_setting.recording_option

    inbound_call_params = fetch_inbound_call_params(recording_option)
    setting_params[:call_recording_settings] = { inbound_call: inbound_call_params }
    setting_params
  end

  def fetch_inbound_call_params(recording_option)
    case recording_option
    when CALL_RECORDING_STATUS[:do_not_record]
      { enabled: false, message_enabled: false, ask_user: false }
    when CALL_RECORDING_STATUS[:record]
      { enabled: true, message_enabled: true, ask_user: false }
    else
      { enabled: true, message_enabled: true, ask_user: true }
    end
  end
end

class V1::Channel::AgentAssistAssignmentsController < ApplicationController
  before_action :require_channel_setting
  before_action :authorize_agent_assist_assignment
  before_action :require_agent_assist_assignment, only: %i[show update destroy]

  # GET /v1/channel/chat_settings/:chat_setting_id/agent_assist_assignment
  def show
    render_agent_assist_assignment
  end

  # PATCH|PUT /v1/channel/chat_settings/:chat_setting_id/agent_assist_assignment
  def update
    permitted_params = params.permit(
      :agent_assist_profile_id,
      :enabled
    )

    ::AgentAssist::ChannelAssignmentService.update(
      agent_assist_assignment: @agent_assist_assignment,
      attrs: permitted_params.to_h
    )

    render_agent_assist_assignment
  end

  # DELETE /v1/channel/chat_settings/:chat_setting_id/agent_assist_assignment
  def destroy
    @agent_assist_assignment.destroy!
    head :ok
  end

  private

  def require_agent_assist_assignment
    @agent_assist_assignment = channel_setting.agent_assist_assignment
  end

  def authorize_agent_assist_assignment
    authorize ::Menu::ChannelAgentAssistAssignment, policy_class: ::Menu::ChannelAgentAssistAssignmentPolicy
  end

  def require_channel_setting
    channel_setting
  end

  def channel_setting
    @channel_setting ||= if params[:voice_call_setting_id]
                           Menu::VoiceCallSetting.find(params[:voice_call_setting_id])
                         elsif params[:chat_setting_id]
                           Menu::ChatSetting.find(params[:chat_setting_id])
                         else
                           raise ServiceException, "Invalid channel"
                         end
  end


  def render_agent_assist_assignment
    render json: Menu::AgentAssistAssignmentRepresenter.new(@agent_assist_assignment)
  end
end

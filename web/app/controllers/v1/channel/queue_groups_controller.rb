# frozen_string_literal: true

class V1::Channel::QueueGroupsController < ApplicationController
  before_action :require_channel_setting
  before_action :require_queue_group, except: [:index, :create]
  before_action :authorize_queue_group, except: [:assign, :unassign]

  def index
    groups = channel_setting.queue_groups.assignable
    render json: Menu::QueueGroupRepresenter.for_collection.prepare(groups)
  end

  def create
    permitted_params = params.permit(
      :group_level,
      :percent,
      :condition_operator,
      :channel,
      :cascade_agent_availability_enabled,
      :min_available_agents,
      :queue_length_enabled,
      :queue_length_threshold,
      :sla_enabled,
      assign: [
        agent_ids: [],
        team_ids: [],
      ],
      sla_thresholds: [
        :condition,
        :threshold_per_queue,
        :time_frame_per_queue,
        { queue: [:menu_id, :lang, :channel] }
      ]
    )

    @queue_group = service.create_group(percent: permitted_params[:percent],
                                        assign: permitted_params[:assign],
                                        channel: permitted_params[:channel],
                                        cascade_setting_enabled: cascade_setting_enabled?,
                                        cascade_agent_availability_enabled: permitted_params[:cascade_agent_availability_enabled],
                                        min_available_agents: permitted_params[:min_available_agents],
                                        condition_operator: permitted_params[:condition_operator],
                                        queue_length_enabled: permitted_params[:queue_length_enabled],
                                        queue_length_threshold: permitted_params[:queue_length_threshold],
                                        sla_enabled: permitted_params[:sla_enabled],
                                        sla_thresholds: permitted_params[:sla_thresholds])
    render_queue_group
  end

  def update
    permitted_params = params.permit(
      :group_level,
      :percent,
      :assign,
      :unassign,
      :condition_operator,
      :channel,
      :cascade_agent_availability_enabled,
      :min_available_agents,
      :queue_length_enabled,
      :queue_length_threshold,
      :sla_enabled,
      sla_thresholds: [
        :condition,
        :threshold_per_queue,
        :time_frame_per_queue,
        { queue: [:menu_id, :lang, :channel] }
      ]
    )
    @queue_group = nil
    permitted_params[:percent] = normalize_percent(permitted_params[:percent])
    @queue_group = service.update_group(group: queue_group,
                                        group_level: permitted_params[:group_level] || params[:level],
                                        channel: permitted_params[:channel],
                                        cascade_setting_enabled: cascade_setting_enabled?,
                                        cascade_agent_availability_enabled: permitted_params[:cascade_agent_availability_enabled],
                                        min_available_agents: permitted_params[:min_available_agents],
                                        condition_operator: permitted_params[:condition_operator],
                                        queue_length_enabled: permitted_params[:queue_length_enabled],
                                        queue_length_threshold: permitted_params[:queue_length_threshold],
                                        sla_enabled: permitted_params[:sla_enabled],
                                        sla_thresholds: permitted_params[:sla_thresholds],
                                        percent: permitted_params[:percent],
                                        assign: params[:assign],
                                        unassign: params[:unassign])

    render_queue_group
  end

  def destroy
    service.destroy_group(group: queue_group)
    render_queue_group_destroy
  end

  def assignments
    render json: render_assignments, status: 200
  end

  def assign
    cmd = QueueGroupCommand.new(queue_group, params[:agents], params[:teams])
    authorize cmd

    service.assign!(
      group: cmd.group,
      agent_ids: cmd.agent_ids,
      team_ids: cmd.team_ids
    )

    render json: render_assignments, status: 200
  end

  def unassign
    cmd = QueueGroupCommand.new(queue_group, params[:agents], params[:teams])
    authorize cmd

    service.unassign!(
      group: cmd.group,
      agent_ids: cmd.agent_ids,
      team_ids: cmd.team_ids
    )

    render json: render_assignments, status: 200
  end

  def candidates
    candidates = if params[:level] == 'all'
                   service.all_candidates(manager: current_user,
                                          bulk_preload: true)
                 else
                   service.candidates(
                     group: queue_group, manager: current_user, bulk_preload: true
                   )
                 end

    team_ids = candidates[:teams].map(&:id)
    agents = User::TeamHistoryService.get_agent_id(team_ids)

    render json: {
      agents: candidates[:agents].map do |agent|
        represent_agent = agent.slice(:id, :first_name, :last_name, :email, :display_role)
        represent_agent[:ucaas_user] = candidates[:ucaas_user_ids][agent.id].present?
        represent_agent
      end,
      teams: candidates[:teams].map do |team|
        represent_team = team.slice(:id, :name, :members_count, :display_role)
        represent_team[:agent_ids] = agents[team.id]&.map(&:user_id)
        represent_team[:has_ucaas_agents] = candidates[:ucaas_team_ids].include?(team.id)
        represent_team
      end
    }
  end

  def require_channel_setting
    channel_setting
  end

  def require_queue_group
    return if params[:level] == 'all' || !queue_group.blank?

    raise ServiceException, "Invalid group level : #{params[:level]}"
  end

  private

  def authorize_queue_group
    authorize @queue_group || Menu::QueueGroup
  end

  def channel_setting
    @channel_setting ||= if params[:voice_call_setting_id]
                           Menu::VoiceCallSetting.find(params[:voice_call_setting_id])
                         elsif params[:chat_setting_id]
                           Menu::ChatSetting.find(params[:chat_setting_id])
                         elsif params[:video_call_setting_id]
                           Menu::VoiceCallSetting.find(params[:video_call_setting_id])
                         elsif params[:email_channel_setting_id]
                           Menu::EmailChannelSetting.find(params[:email_channel_setting_id])
                         else
                           raise ServiceException, 'Invalid channel'
                         end
  end

  def render_queue_group
    render json: Menu::QueueGroupRepresenter.new(queue_group).to_hash.merge(render_assignments)
  end

  def render_assignments
    assignments = queue_group.assignments
    assignments.load_all_assigned_users
    team_ids = assignments.filter_map { |assignment| assignment.assignee_id if assignment.assignee_type == 'Team' }
    agents = User::TeamHistoryService.get_agent_id(team_ids)
    RoleService::FasterCustomRoles.extend_users(assignments.assigned_users)
    {
      assigned_agents_count: assignments.assigned_agents.count,
      assignments: Menu::QueueAssignmentRepresenter.for_collection.prepare(assignments).to_hash(user_options: { agent_group: agents })
    }
  end

  def render_queue_group_destroy
    render json: Menu::QueueGroupRepresenter.new(queue_group)
  end

  def queue_group
    @queue_group ||= channel_setting.queue_groups.assignable.find_by(
      group_level: params[:level]
    )
  end

  def service
    @service ||= Menu::QueueGroupService.new(channel_setting:)
  end

  def normalize_percent(value)
    value.present? && value != '' ? value.to_i : 0
  end

  def cascade_setting_enabled?
    return params[:cascade_setting_enabled] unless params[:cascade_setting_enabled].nil?

    params[:cascade_agent_availability_enabled]
  end
end

# frozen_string_literal: true

class V1::Channel::ChatSettingsController < ApplicationController
  include MenuChannelSetable

  before_action :validate_virtual_agent_assignment, only: [:update]

  private

  def resource_class
    Menu::ChatSetting
  end

  def resource_scope
    return super unless FeatureFlag.on?('chatbot')

    super.includes(menu: { virtual_agent_assignments: :virtual_agent })
  end

  def setting_params
    params.permit(policy(resource_class).permitted_attributes)
  end

  def channel_setting_representer
    Menu::ChatSettingRepresenter
  end

  def before_render_channel_settings(channel_settings)
    channel_settings.each(&:load_assigned_agents_count)
    channel_settings.each(&:load_assigned_virtual_agents_count)
  end

  def before_render_channel_setting(channel_setting)
    channel_setting.load_assigned_agents_count
    channel_setting.load_assigned_virtual_agents_count
  end
end

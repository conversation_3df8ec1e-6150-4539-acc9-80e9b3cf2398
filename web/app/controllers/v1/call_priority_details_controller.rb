class V1::CallPriorityDetailsController < V1::PriorityDetailsController
  before_action :authorize_call

  private

  def authorize_call
    authorize Call
  end

  def sortable_columns
    %w(call_id) + super
  end

  def load_priority_details
    Call::PriorityDetail.includes(call: [:agent, menu_path: { items: { menu: :settings } }], transfer: [{to_menus: :settings}])
  end

  def representer_class
    CallPriorityDetailRepresenter
  end
end

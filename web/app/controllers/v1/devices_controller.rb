class V1::DevicesController < ApplicationController
  before_action :require_service
  skip_before_action :authenticate, only: [:download_app, :open_app]

  def download_app
    user_agent = request.headers['User-Agent']
    url = @service.download_app_url(user_agent: user_agent, nonce: params[:nonce], mobile_app_id: params[:mobile_app_id])

    if url.blank?
      url = get_error_url(user_agent: user_agent)
    end

    redirect_to url, allow_other_host: true
  end

  def open_app
    user_agent = request.headers['User-Agent']
    url = @service.open_app_url(
      user_agent: user_agent,
      nonce: params[:nonce],
      mobile_app_id: params[:mobile_app_id],
      call_id: @call.id
    )

    if url.blank?
      url = get_error_url(user_agent: user_agent)
    end

    redirect_to url, allow_other_host: true
  end

  private
  def require_service
    @call = Call.find(params[:id])
    @service = CallService::SmartChannelService.new(call: @call)
  end

  def get_error_url(user_agent:)
    platform = case user_agent
    when /iPhone/
      'ios'
    when /Android/
      'android'
    else
      ''
    end

    params = {
      platform: platform,
      company: current_company.name
    }

    "#{current_company.base_url}/no-app?#{params.to_query}"
  end
end

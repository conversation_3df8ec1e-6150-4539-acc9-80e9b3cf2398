# frozen_string_literal: true

module V1
  class ContactsController < ApplicationController
    before_action :authorize_contact
    before_action :require_contact, only: [:update, :show, :destroy]
    before_action :require_contact_list, only: [:index]

    # GET /v1/contact_lists/:contact_list_id/contacts
    def index
      paginate_contacts(contacts_scope_with_types)
    end

    def show = render_contact

    def create
      @contact = Contact.create!(permitted_params)
      render_contact
    end

    def update
      @contact.update!(permitted_params)
      render_contact
    end

    def destroy
      if params[:dry_run] == 'yes'
        errors = Contact::DestroyService.validate([@contact])
        render json: { errors: }
      else
        @contact.destroy
        head :ok
      end
    end

    private

    def paginate_contacts(contacts)
      page = params[:page] || 1
      per_page = params[:per_page] || 50
      contacts = contacts.page(page).per(per_page)

      add_pagination(contacts)

      render_contacts(contacts)
    end

    def render_contacts(contacts)
      result = {
        breadcrumbs: @contact_list.breadcrumbs,
        data: ContactWithInUsePositionsRepresenter.for_collection.prepare(contacts)
      }

      render json: result
    end

    ##
    # [AXE-3359]: Update contact selection.
    # Method to filter contacts based on contact list and type.
    # @return [Array<Contact>] the contacts have been filtered.
    #
    def contacts_scope_with_types
      # Filter contacts based on contact_list_id.
      scope = Contact.by_contact_list_id(params[:contact_list_id])

      # Allow filtering contacts by multiple types if specified and valid.
      if params[:type].present?
        allowed_types = params[:type].split(',') & Contact.types.keys
        scope = scope.by_type(allowed_types)
      end

      scope
    end

    def authorize_contact
      authorize @contact || Contact, policy_class: ContactPolicy
    end

    def require_contact = @contact ||= Contact.find(params[:id])

    def require_contact_list = @contact_list ||= ContactList.find(params[:contact_list_id])

    def render_contact = render json: ContactRepresenter.new(@contact)

    def permitted_params
      params.require(:contact).permit(
        :name,
        :type,
        :source,
        :sip_uri,
        :phone_number,
        :contact_list_id,
        :need_create_crm_records_outbound,
        :use_refer,
        sip_params: sip_attributes
      )
    end

    def sip_attributes
      [
        :data_parameters_enabled,
        {
          data_parameters: [:field, :type, :value, :source, :source_field, :required]
        },
        {
          data_records: [
            :in_metadata,
            :in_crm_record
          ]
        }
      ]
    end
  end
end

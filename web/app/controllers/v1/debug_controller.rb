# frozen_string_literal: true

class V1::DebugController < ApplicationController
  before_action :confirm_debug_enabled

  def referer_map
    render json: @@referer_map.map
  end

  protected

  def should_verify_authorized
    false
  end

  def confirm_debug_enabled
    raise 'Cannot access DebugController because debugging is not enabled in this environment' unless
      Rails.application.config.use_debug_controller
  end
end

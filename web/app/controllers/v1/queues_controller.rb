class V1::QueuesController < ApplicationController
  around_action :use_read_replica, only: [:managed, :managed_teams]

  # Get queues managed by current user
  # GET /v1/queues/managed?channel=

  before_action :authorize_queue

  def managed
    channel = params.permit(:channel).require(:channel)
    queues = ::Dashboard::DataScope.for_user(current_user, channel.to_sym).active_queues(channel, with_path: true)

    render json: QueueRepresenter.for_collection.prepare(queues)
  end

  # NOTE: currently only for the report.
  def managed_teams
    channel = params.permit(:channel).require(:channel)
    is_restricted = ReportService::ReportRestrictionChecker.check_restricted_data(
      report_type: 'queue',
      custom_permissions: current_user.custom_permissions
    )

    scope = is_restricted ? Dashboard::DataScope.restricted_to_team_scope(current_user) : Dashboard::DataScope.all_scope
    queues = scope.active_queues(channel, with_path: true)

    render json: QueueRepresenter.for_collection.prepare(queues)
  end

  private

  def authorize_queue
    authorize :queue
  end
end

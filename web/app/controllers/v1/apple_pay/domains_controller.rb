module V1
  module ApplePay
    class DomainsController < ApplicationController
      skip_before_action :authenticate

      def show
        return head :not_found if merchant.nil?
        return unless stale?(last_modified: merchant.updated_at.utc, etag: merchant)
        return head :not_found if domain_verification_file.nil?

        render plain: domain_verification_file
      end

      protected

      def should_verify_authorized
        false
      end

      private

      def domain_verification_file
        @domain_verification_file ||= merchant.try(:domain_verification_file)
      end

      def merchant
        @merchant ||= ::ApplePay::Merchant.first
      end
    end
  end
end

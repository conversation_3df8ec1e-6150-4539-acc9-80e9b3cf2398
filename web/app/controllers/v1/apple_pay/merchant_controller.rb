module V1
  module ApplePay
    class MerchantController < ApplicationController
      before_action :require_merchant
      before_action :authorize_merchant

      def show
        merchant = @merchant.present? ? @merchant : ::ApplePay::Merchant.new

        render json: ::ApplePay::MerchantRepresenter.new(merchant)
      end

      def create_or_update
        if @merchant.nil?
          @merchant = ::ApplePay::MerchantCreator.new.create(permitted_params)
        else
          ::ApplePay::MerchantUpdater.new(@merchant).update(permitted_params.to_unsafe_h)
        end
        render json: ::ApplePay::MerchantRepresenter.new(@merchant)
      rescue ActiveModel::RangeError, ActiveRecord::ValueTooLong => e
        Rails.logger.error(error: e) { e.message }
        render json: { message: 'Failed to save Apple Pay Merchant' }, status: :bad_request
      end

      def destroy
        @merchant.destroy!
        render json: ::ApplePay::MerchantRepresenter.new(@merchant)
      end

      private

      def permitted_params
        file_info = [:content, :name, :size]
        key_pair_info = [key: file_info, cert: file_info]

        params.require(:merchant).permit(
          :merchant_id,
          :enabled,
          :country_code,
          :display_name,
          domain_verification_file: file_info,
          auth_key_pair: key_pair_info,
          processing_key_pair: key_pair_info
        )
      end

      def authorize_merchant
        authorize ::ApplePay::Merchant
      end

      def require_merchant
        @merchant ||= ::ApplePay::Merchant.first
      end
    end
  end
end

class V1::PhoneNumbersController < ApplicationController
  def unassigned
    unless params[:direction].present? && params[:direction] == "inbound"
      render json: {errors: "we only support inbound filters"}, status: :bad_request
      return
    end

    unless params[:lang].present? && params[:menu].present?
      render json: {errors: "lang and menu_type parameters are required"}, status: :bad_request
      return
    end

    unless params[:verified].present?
      render json: {errors: "verified parameter is required"}, status: :bad_request
      return
    end

    lang = params[:lang]
    menu_type = Menu.class_by_type(params[:menu]).try(:name)

    channel = :call if params[:verified] == "call"
    channel = :sms if params[:verified] == "sms"

    if params[:verified] == 'whatsapp'
      raise InvalidParameterException, 'whats-app is unavailable' if Messaging::Channel.feature_flag_off?(:whatsapp)
      channel = :whatsapp
    end

    unless channel.present?
      render json: {errors: "verified parameter value is not supported"}, status: :bad_request
      return
    end

    @phone_numbers = PhoneNumber.unassigned(direction: :inbound, lang: lang, menu_type: menu_type).verified_for(channel)
    render json: PhoneNumberRepresenter.for_collection.prepare(@phone_numbers).to_json
  end

end

# frozen_string_literal: true

module V1
  class AvailabilityFiltersController < ApplicationController
    before_action :require_availability_filter, except: [:index, :create]
    before_action :authorize_availability_filters

    def index
      availability_filters = Availability::Filter.admin_created.includes(:allowances)
      render json: AvailabilityFilterRepresenter.for_collection.prepare(availability_filters)
    end

    def show
      render_availability_filter
    end

    def create
      @availability_filter = AvailabilityFilterService.new(permitted_params).create_filter
      render_availability_filter
    end

    def update
      AvailabilityFilterService.new(permitted_params).update_filter(@availability_filter)
      render_availability_filter
    end

    def destroy
      @availability_filter.destroy
      render_availability_filter
    end

    private

    def require_availability_filter
      @availability_filter = Availability::Filter.find(params[:id])
    end

    def render_availability_filter
      render json: AvailabilityFilterRepresenter.new(@availability_filter)
    end

    def permitted_assignment_params
      params.permit(
        teams: [],
        users: []
      ).to_h.deep_symbolize_keys
    end

    def permitted_params
      params.permit(
        :name,
        :description,
        filter_rules: [
          voice_call: [
            :transfer,
            ivr: permitted_voice_call_params,
            mobile: permitted_voice_call_params,
            web: permitted_voice_call_params
          ],
          chat: [
            :transfer,
            mobile: permitted_chat_params,
            web: permitted_chat_params,
            sms: permitted_chat_params,
            whatsapp: permitted_chat_params
          ]
        ]
      ).to_h.deep_symbolize_keys
    end

    def authorize_availability_filters
      authorize :availability_filters
    end

    def permitted_voice_call_params
      CommQueue::ChannelCategory::MAP.keys
    end

    def permitted_chat_params
      [:incoming, :transfer]
    end
  end
end

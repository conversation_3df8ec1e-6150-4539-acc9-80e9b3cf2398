# frozen_string_literal: true

module V1
  class FilterOptionsController < V1::Performance::BaseController
    def self.filter_handler(name, obj_name: name.camelize)
      define_method(name) do
        render json: "::MonitoringFilters::#{obj_name}".constantize.new(current_user, access_type).fetch
      end
    end

    filter_handler 'custom_roles'
    filter_handler 'queue_report_groups'
    filter_handler 'teams'
    filter_handler 'supervisors'
    filter_handler 'languages'
    filter_handler 'company_locations', obj_name: 'Locations'
    filter_handler 'user_statuses'
    filter_handler 'agents'

    protected

    def policy_obj
      [:performance, :filter_options]
    end

    def access_type
      ::Performance::FilterOptionsPolicy.new(current_user).access_type
    end
  end
end

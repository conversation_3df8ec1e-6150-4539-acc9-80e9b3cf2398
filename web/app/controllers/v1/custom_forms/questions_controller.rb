class V1::CustomForms::QuestionsController < ApplicationController
  before_action :authorize_form
  before_action :set_custom_form
  before_action :set_question, only: [:show, :destroy, :update, :duplicate]

  QUESTION_TYPES = {
    text_entry: CustomForms::TextEntryQuestion,
    list_picker: CustomForms::ListPickerQuestion,
    date: CustomForms::DateQuestion,
    time: CustomForms::TimeQuestion
  }.freeze

  QUESTION_REPRESENTERS = {
    text_entry: CustomForm::Question::TextEntryRepresenter,
    list_picker: CustomForm::Question::ListPickerRepresenter,
    date: CustomForm::Question::DateRepresenter,
    time: CustomForm::Question::TimeRepresenter
  }.freeze

  # GET /v1/forms/:form_id/questions
  def index
    render_list_questions
  end

  # GET /v1/forms/:form_id/questions/:id
  def show
    render_question
  end

  # POST /v1/forms/:form_id/questions
  def create
    create_question(question_params)

    return render_question if @question
  end

  # POST /v1/forms/:form_id/questions/:id/duplicate
  def duplicate
    parameters = { question_id: params[:id] }
    job = Jobs::DuplicateQuestionJob.create!(parameters:).tap do |job|
      JobWorker.perform_async(job.id)
    end
    head :accepted, location: v1_job_url(job)
  end

  # PATCH /v1/forms/:form_id/questions/:id
  def update
    return render_question if @question.update!(question_params)
  end

  # DELETE /v1/forms/:form_id/questions/:id
  def destroy
    job = Jobs::DeleteQuestionJob.create!(parameters: {
        question_id: params[:id],
      }).tap do |job|
      JobWorker.perform_async(job.id)
    end

    head :accepted, location: v1_job_url(job)
  end

  private

  def set_custom_form
    @custom_form = Form.custom_form.find_by(id: params[:form_id])
    return head :not_found unless @custom_form
  end

  def set_question
    @question = @custom_form.questions.includes(:elements).find_by(id: params[:id])
    return head :not_found unless @question
  end

  def create_question(params)
    question_type = QUESTION_TYPES[params[:type].to_sym]
    @custom_form.check_maximum_question_count
    raise ArgumentError, "Question type '#{params[:type]}' is not supported" if question_type.nil?

    params[:type] = question_type.to_s
    @question = question_type.create!(params)
  end

  def render_question
    question_represent = QUESTION_REPRESENTERS[@question.type_name.to_sym]
    render json: { question: question_represent.new(@question) }, status: :ok
  end

  def render_list_questions
    questions = @custom_form.questions.order(:position).map do |q|
      QUESTION_REPRESENTERS[q.type_name.to_sym].new(q).to_hash(user_options: { render_elements: false })
    end

    render json: { questions: }, status: :ok
  end

  def question_params
    permitted_params = params.permit(
      :type,
      :is_mandatory,
      :is_masked,
      :question,
      :position,
      :placeholder,
      :character_limit,
      :content_type,
      :is_multiselect
    )
    permitted_params[:custom_form_id] = @custom_form.id
    permitted_params[:type] = @question.type if @question.present?
    permitted_params
  end

  def authorize_form
    authorize Form, policy_class: FormPolicy
  end
end

class V1::CustomForms::ListElementsController < ApplicationController
  before_action :authorize_form
  before_action :set_custom_form
  before_action :set_question
  before_action :set_list_element, only: [:destroy, :update, :duplicate]

  # POST /v1/forms/:form_id/questions/:question_id/list_elements
  def create
    @list_element = @question.elements.create!(list_element_params)
    render_element
  end

  # PATCH /v1/forms/:form_id/questions/:question_id/list_elements/:id
  def update
    @list_element.update!(list_element_params)
    render_element
  end

  # DELETE /v1/forms/:form_id/questions/:question_id/list_elements/:id
  def destroy
    @list_element.destroy!
    head :ok
  end

  # POST /v1/forms/:form_id/questions/:question_id/list_elements/:id/duplicate
  def duplicate
    @list_element = FormService::ListElement::Duplicator.new(@question, @list_element).call
    render_element
  end

  private

  def set_custom_form
    @custom_form = Form.custom_form.find_by(id: params[:form_id])
    return head :not_found unless @custom_form
  end

  def set_question
    @question = @custom_form.questions.find_by(id: params[:question_id])
    return head :not_found unless @question
  end

  def set_list_element
    @list_element = @question.elements.find_by(id: params[:id])
    return head :not_found unless @list_element
  end

  def render_element
    render json: { list_element: element_representer.new(@list_element) }, status: :ok
  end

  def element_representer
    CustomForm::Question::ListElementRepresenter
  end

  def list_element_params
    permitted_params = params.permit(
      :value,
      :image,
      :list_position,
      :list_name
    )
    permitted_params[:element_type] = CustomForms::QuestionElement.element_types[:list]
    determine_image(permitted_params)
  end

  def determine_image(permitted_params)
    return permitted_params unless @list_element

    image = permitted_params.fetch(:image, nil)

    # Remove this image field since we want to keep the current image
    # unless it's being updated with a new one.
    # This is important since the client will only send image's url when updating the object.
    permitted_params.delete(:image) if @list_element.image.present? && (image == @list_element.image.url)
    permitted_params
  end

  def authorize_form
    authorize Form, policy_class: FormPolicy
  end
end

class V1::SfdcUserStatusesController < ApplicationController
  before_action :authorize_user_status

  def show
    if Company.current.crm_type == :ujet || Company.current.crm_type == :custom
      render json: []
    else
      render_active_statuses
    end
  end

  def show_crm_statuses
    if Company.current.crm_type == :ujet || Company.current.crm_type == :custom
      render json: []
    else
      render_all_crm_statuses
    end
  end

  def show_ucaas
    if Company.current.ucaas_type == :msteams
      render_active_ucaas_statuses
    else
      render json: []
    end
  end

  def update
    if Company.current.crm_type == :salesforce || Company.current.crm_type == :kustomer || Company.current.crm_type == :servicenow
      SfdcUserStatusService.update_user_status_mapping(mapping_table: params.require(:from_crm).to_a)

      if Company.current.crm_type == :kustomer
        UserStatusToCrmMappingService.update_all_mappings(mappings: params.require(:to_crm).to_a)
      end

      render_active_statuses
    else
      render json: []
    end
  end

  def update_ucaas
    if Company.current.ucaas_type == :msteams
      mapping_table = params.require(:ucaas_user_statuses).to_a
      Ucaas::UcaasUserStatusService.update_user_status_mapping(mapping_table: mapping_table)
      render_active_ucaas_statuses
    else
      render json: []
    end
  end


  private

  def render_all_crm_statuses
    render json: UserStatusMappingService.crm_presence_statuses
  end

  def render_active_statuses
    from_crm_mappings = UserStatusMappingService.from_crm_mappings
    to_crm_mappings = UserStatusMappingService.to_crm_mappings

    render json: {
      from_crm: SfdcUserStatusRepresenter.for_collection.new(from_crm_mappings),
      to_crm: SfdcUserStatusRepresenter.for_collection.new(to_crm_mappings)
    }
  end

  def render_active_ucaas_statuses
    active_ucaas_user_statuses = Ucaas::UcaasUserStatusService.active_statuses
    UserStatusMappingService.inject_user_statuses(active_ucaas_user_statuses)
    render json: UcaasUserStatusRepresenter.for_collection.new(active_ucaas_user_statuses)
  end

  def authorize_user_status
    authorize UserStatus
  end
end

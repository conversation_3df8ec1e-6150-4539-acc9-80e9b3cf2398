# frozen_string_literal: true

module V1
  class ContactListsController < ApplicationController
    before_action :authorize_contact_list
    before_action :require_contact_list, only: [:update, :show, :destroy]

    def index
      contact_lists = ContactList.where.not(type: ContactList.types[:personal])

      result = {
        breadcrumbs: [ContactList.default_breadcrumb],
        data: ContactListRepresenter.for_collection.prepare(contact_lists)
      }

      render json: result
    end

    def show = render_contact_list

    def create
      @contact_list = ContactList.custom.create!(permitted_params)
      render_contact_list
    end

    def update
      @contact_list.update!(permitted_params)
      render_contact_list
    end

    def destroy
      if params[:dry_run] == 'yes'
        errors = Contact::DestroyService.validate_contact_list(@contact_list)
        render json: {errors: errors}
      else
        @contact_list.destroy
        head :ok
      end
    end

    private

    def authorize_contact_list = authorize @contact_list || ContactList

    def require_contact_list = @contact_list ||= ContactList.find(params[:id])

    def render_contact_list = render json: ContactListRepresenter.new(@contact_list)

    def permitted_params = params.require(:contact_list).permit(:name, :type, :need_create_crm_records_outbound)
  end
end

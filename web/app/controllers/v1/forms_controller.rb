# frozen_string_literal: true

module V1
  class FormsController < ApplicationController
    before_action :authorize_form
    before_action :require_form, only: [:show, :update, :destroy, :duplicate]

    def index
      types = params[:type]&.split(',') || [:web, :custom]
      menu_types = params[:menu_types]&.split(',') || [:ivr, :web, :mobile, :whatsapp, :amb]
      lang = params[:lang]
      page = params[:page] || 1
      per_page = params[:per_page] || 50

      form_lists = FormService.list_forms(types:, menu_types:, lang:, page:, per_page:)
      fields = determine_fields
      add_pagination(form_lists)

      render json: {
        forms: FormWithQuestionCountRepresenter.for_collection.prepare(form_lists).to_hash(
          user_options: { fields: }
        )
      }, status: :ok
    end

    def show
      render_form
    end

    def create
      creator_service = FormService::Creator.new(form_params)
      creator_service.call
      @form = creator_service.form
      render_form
    end

    def duplicate
      parameters = { form_id: params[:id] }
      job = Jobs::DuplicateFormJob.create!(parameters:).tap do |job|
        JobWorker.perform_async(job.id)
      end
      head :accepted, location: v1_job_url(job)
    end

    def destroy
      job = Jobs::DeleteFormJob.create!(parameters: params[:id]).tap do |job|
        JobWorker.perform_async(job.id)
      end

      head :accepted, location: v1_job_url(job)
    end

    def update
      mutator_service = FormService::Mutator.new(form_params, @form)
      mutator_service.call
      @form = mutator_service.form
      render_form
    end

    private

    def form_params
      params.permit(
        :form_type,
        :name,
        :title,
        :header,
        :image,
        :footer,
        :subtitle,
        :external_id,
        :preview_endpoint,
        languages: [],
        menu_types: [],
        channels: []
      )
    end

    def determine_fields
      fields = params[:fields]&.split(',') || []
      fields.flatten
    end

    def render_form
      render json: { form: FormRepresenter.new(@form) }, status: :ok
    end

    def authorize_form
      authorize Form, policy_class: FormPolicy
    end

    def require_form
      @form ||= Form.find(params[:id])
    end
  end
end

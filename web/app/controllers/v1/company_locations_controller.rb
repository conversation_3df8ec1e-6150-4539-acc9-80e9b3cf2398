class V1::CompanyLocationsController < ApplicationController
  before_action :authorize_company_location
  before_action :require_location, except: [:create, :index]

  def index
    locations = CompanyLocation.all
    render json: CompanyLocationRepresenter.for_collection.prepare(locations)
  end

  def create
    @location = CompanyLocation.create!(permitted_params)
    render_location
  end

  def update
    @location.update!(permitted_params)
    render_location
  end

  def destroy
    @location.destroy
    render_location
  end

  private
  def authorize_company_location
    authorize(CompanyLocation)
  end

  def require_location
    @location = CompanyLocation.find(params[:id])
  end

  def render_location
    render json: CompanyLocationRepresenter.new(@location)
  end

  def permitted_params
    params.require(:location).permit(:location, :lang)
  end
end
# frozen_string_literal: true

module V1
  class OutboundNumberPermissionsController < ApplicationController
    before_action :require_outbound_permission, except: [:create, :candidates_for_create]
    before_action :authorize_outbound_permission

    def show
      @permission.location? ? preload_location_assignments : preload_agent_assignments
      render json: OutboundNumber::PermissionRepresenter.new(@permission)
    end

    # get assignment candidates for a rule that already exists
    def candidates
      service = OutboundNumber::PermissionService.new(@permission)
      candidates = service.candidates

      render json: OutboundNumber::PermissionCandidatesRepresenter.new(candidates)
    end

    # get candidates for a rule that's not created yet
    def candidates_for_create
      service = OutboundNumber::PermissionService.new(nil)
      candidates = service.candidates

      render json: OutboundNumber::PermissionCandidatesRepresenter.new(candidates)
    end

    def create
      permission = OutboundNumber::PermissionService.create(
        phone_number_id: permitted_params[:phone_number_id],
        number_type: permitted_params[:number_type],
        permission_type: permitted_params[:permission_type],
        permission_scope: permitted_params[:permission_scope]  || 'agent',
        assignment_type: permitted_params[:assignment_type],
        country_code: permitted_params[:country_code],
        phone_number: permitted_params[:phone_number],
        assignments: permitted_params[:assignments]
      )

      render json: OutboundNumber::PermissionRepresenter.new(permission)
    end

    def update
      service = OutboundNumber::PermissionService.new(@permission)
      service.update(
        permission_type: permitted_params[:permission_type],
        assignment_type: permitted_params[:assignment_type],
        country_code: permitted_params[:country_code],
        phone_number: permitted_params[:phone_number],
        assignments: permitted_params[:assignments],
        permission_scope: permitted_params[:permission_scope] || 'agent'
      )

      @permission.assignments.reload # reload to make sure assignments are up to date
      @permission.location? ? preload_location_assignments : preload_agent_assignments
      render json: OutboundNumber::PermissionRepresenter.new(@permission)
    end

    def destroy
      @permission.destroy!

      head :ok
    end

    private

    def require_outbound_permission
      @permission = OutboundNumber::Permission.find(params[:id])
    end

    def preload_agent_assignments
      service = OutboundNumber::PermissionPreloaderService.new([@permission])
      service.inject_assigned_agents
    end

    def preload_location_assignments
      service = OutboundNumber::PermissionPreloaderService.new([@permission])
      service.inject_assigned_locations
    end

    def permitted_params
      @permitted_params ||= params.permit(
        :phone_number_id,
        :number_type,
        :permission_type,
        :permission_scope,
        :assignment_type,
        :country_code,
        :phone_number,
        assignments: [
          :assignee_id,
          :assignee_type
        ]
      )
    end

    def authorize_outbound_permission
      authorize OutboundNumber::Permission, policy_class: OutboundNumberPermissionPolicy
    end
  end
end

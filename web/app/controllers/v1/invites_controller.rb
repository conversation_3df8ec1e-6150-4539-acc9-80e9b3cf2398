class V1::InvitesController < ApplicationController
  skip_before_action :authenticate, only: [:show, :accept]
  before_action :find_user_by_invite_code, only: [:show, :accept]
  before_action :authorize_user, only: [:create, :create_multiple]

  # Returns a user having invitation code
  def show
    skip_authorization # for dev environments.
    render_user
  end

  def accept
    skip_authorization # for dev environments.
    user_params = params.permit(user: [:password, :password_confirmation]).require(:user)

    UserService.accept_invite(
      user: @user,
      password: user_params[:password],
      password_confirmation: user_params[:password_confirmation]
    )

    token = AuthToken.issue_token user_id: @user.id, exp: Time.now.to_i + 7.day.to_i
    render json: {user: UserRepresenter.new(@user).to_hash(exclude: user_exclude), token: token}
  end

  def create
    @user = User.find(params[:user_id])

    raise ServiceException, "User not found" if @user.blank?

    @user.generate_invite_code
    @user.save!

    AgentMailer.invite_email(@user).deliver_later

    render_user
  end

  def create_multiple
    users = User.where(id: params[:user_ids])
    result = UserService.invite(users)
    response_json = {
      success_user_ids: result[:success_users].map(&:id),
      failed_user_ids: result[:failed_users].map(&:id),
    }
    render json: response_json
  end

  def authorize_user
    authorize :invite
  end


  private

  def find_user_by_invite_code
    code = params[:code]
    if code.blank?
      raise ServiceException, "Invalid code"
    end

    @user = User.find_by_invite_code(code)

    if @user.blank?
      raise ServiceException, "Invite code has been expired."
    end
  end

  def render_user
    render json: UserRepresenter.new(@user).to_json(exclude: user_exclude)
  end

  def user_exclude
    [:company, :key, :teams, :assignments, :team_assignments, :campaign, :stats]
  end
end

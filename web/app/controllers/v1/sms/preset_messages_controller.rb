class V1::Sms::PresetMessagesController < ApplicationController
  before_action :require_sms_preset_message, only: [:show, :update, :destroy, :restore_default]
  before_action :authorize_sms_preset_message

  def index
    _params = params.permit(:lang, :global, :menu_id, :type)

    lang = _params.require(:lang)

    @messages = SmsPresetMessage.all_with_default(lang: lang, type: _params[:type])
    @messages = @messages.where(menu_id: nil) if _params[:global]
    @messages = @messages.where(menu_id: _params[:menu_id]) if _params[:menu_id]

    render_messages
  end

  def show
    render_message
  end

  def create
    message_params = params.permit(:type, :menu_id, :lang, :text, :label, :position)
    @message = SmsPresetMessageFactory.create!(message_params)
    render_message

  rescue SmsPresetMessageFactory::MessageInvalid => e
    raise ServiceException, "Cannot create message"
  end

  def update
    @message.update!(params.permit(:label, :text, :position))
    render_message
  end

  def destroy
    raise ServiceException, "Cannot delete message" unless @message.can_be_deleted?
    @message.destroy!
    render_message
  end

  def restore_default
    raise ServiceException, "Cannot restore message" unless @message.can_be_restored?
    @message.restore_default!
    render_message
  end

  private

  def require_sms_preset_message
    @message = SmsPresetMessage.find params[:id] || params[:preset_message_id]
  end

  def authorize_sms_preset_message
    authorize [:sms, :preset_message]
  end

  def render_message
    if @message
      render json: SmsPresetMessageRepresenter.new(@message)
    else
      render json: nil
    end
  end

  def render_messages
    render json: SmsPresetMessageRepresenter.for_collection.prepare(@messages)
  end
end

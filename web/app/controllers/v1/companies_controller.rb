require './lib/data_uri'

class V1::CompaniesController < ApplicationController
  before_action :authorize_company

  def show
    render json: CompanyRepresenter.for_user(current_user).new(current_company)
  end

  def update
    data = params.require(:company).permit(*permitted_attributes)
    current_company.update! data

    render json: CompanyRepresenter.for_user(current_user).new(current_company)
  end

  private

  def permitted_attributes
    permitted = [:use_mobile, :use_ivr, :use_web, :use_sms_chat, :use_email]
    permitted << :use_whats_app if ::Messaging::Channel.feature_flag_on?(:whatsapp)
    permitted << :use_amb if ::Messaging::Channel.feature_flag_on?(:amb)
    permitted
  end

  def authorize_company
    authorize current_company
  end
end

class V1::Sip<PERSON>ddressesController < ApplicationController
  before_action :authorize_sip_address
  before_action :require_sip_address, only: [:update, :show, :destroy]

  def index
    addresses = SipAddress.all
    render json: SipAddressRepresenter.for_collection.prepare(addresses)
  end

  def create
    @sip_address = SipAddress.new(permitted_params)
    @sip_address.save!
    render_address
  end

  def show
    render_address
  end

  def update
    @sip_address.update!(permitted_params)
    render_address
  end

  def destroy
    @sip_address.destroy!
    head :ok
  end

  private

  def render_address
    render json: SipAddressRepresenter.new(@sip_address)
  end

  def permitted_params
    params.permit(:label, :sip_uri, :use_refer)
  end

  def require_sip_address
    @sip_address = SipAddress.find(params[:id])
  end

  def authorize_sip_address
    authorize :sip_address
  end
end

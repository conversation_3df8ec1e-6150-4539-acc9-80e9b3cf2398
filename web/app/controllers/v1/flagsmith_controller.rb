class V1::FlagsmithController < ApplicationController
  skip_before_action :authenticate, only: [:feature_callback]
  before_action :request_validation

  def feature_callback
    FeatureFlagSource::Flagsmith.new.fetch_all(async: true)
    head :ok
  end

  private

  def should_verify_authorized
    false
  end

  def request_validation
    flagsmith_signature = request.headers['X-Flagsmith-Signature']
    sha = OpenSSL::HMAC.hexdigest(
      OpenSSL::Digest.new('sha256'),
      Rails.configuration.env[:flagsmith][:webhook_secret],
      request.raw_post
    )

    unless flagsmith_signature == sha
      raise ServiceException, 'Flagsmith request validation failure'
    end
  end
end

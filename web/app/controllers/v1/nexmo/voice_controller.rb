# frozen_string_literal: true

class V1::Nexmo::VoiceController < ApplicationController
  include UseSipHeaders

  wrap_parameters false

  skip_before_action :authenticate

  SIP_REGEX = /^sip:(.*?)@.*$/

  before_action :set_uuids
  before_action :extract_sip_phone_number
  around_action :lock_request_participant

  # Nexmo uses the same voice callback for both PSTN and SIP
  before_action :set_sip_headers, only: [:answer, :answer_sip]

  def event
    service.event
    head :ok
  end

  def answer
    ncco = service.answer
    render json: ncco.to_json
  end

  def answer_sip
    ncco = service.answer_sip
    render json: ncco.to_json
  end

  def outbound_answer
    ncco = service.outbound_answer
    render json: ncco.to_json
  end

  def fallback_answer
    head :ok
  end

  def agent_assist_answer
    ncco = service.agent_assist_answer
    render json: ncco.to_json
  end

  def campaign_answer
    ncco = service.campaign_answer
    render json: ncco.to_json
  end

  def agent_assist_event
    service.agent_assist_event
    head :ok
  end

  def mobile_agent_answer
    ncco = service.mobile_agent_answer
    render json: ncco.to_json
  end

  def ucaas_agent_answer
    ncco = service.ucaas_agent_answer
    render json: ncco.to_json
  end

  def announce_and_hangup
    ncco = service.announce_and_hangup
    render json: ncco.to_json
  end

  def internal_call_after_hours_deflection
    ncco = service.internal_call_after_hours_deflection
    render json: ncco.to_json
  end

  def internal_call_after_hours_deflection_callback
    ncco = service.internal_call_after_hours_deflection_callback
    render json: ncco.to_json
  end

  def internal_call_over_capacity_deflection
    ncco = service.internal_call_over_capacity_deflection
    render json: ncco.to_json
  end

  def internal_call_over_capacity_deflection_callback
    ncco = service.internal_call_over_capacity_deflection_callback
    render json: ncco.to_json
  end

  def internal_call_automatic_redirection
    ncco = service.internal_call_automatic_redirection
    render json: ncco.to_json
  end

  def internal_call_voicemail_recorded
    ncco = service.internal_call_voicemail_recorded
    render json: ncco.to_json
  end

  def internal_announcement_deflection
    ncco = service.internal_announcement_deflection
    render json: ncco.to_json
  end

  def recording
    service.recording
    head :ok
  end

  def recording_option
    ncco = service.ivr_recording_option
    render json: ncco.to_json
  end

  def conference_join
    ncco = service.conference_join
    render json: ncco.to_json
  end

  def voicemail
    service.voicemail
    head :ok
  end

  def queue_lang
    ncco = service.queue_lang
    render json: ncco.to_json
  end

  def extension_directory_callback
    step = params[:step]&.to_s

    if step.blank? # initial search
      ncco = service.extension_directory
    elsif step == 'new_search_confirmation'
      ncco = service.new_search_confirmation
    elsif step == 'agent_connect_confirmation'
      ncco = service.agent_connect_confirmation
    end

    render json: ncco.to_json
  end

  def queue_menu
    ncco = service.queue_menu
    render json: ncco.to_json
  end

  def deflection_to_queue
    ncco = service.ivr_deflected_to_queue_callback
    render json: ncco.to_json
  end

  def transfer_to_non_leaf_queue
    ncco = service.ivr_transfer_to_non_leaf_queue_callback
    render json: ncco.to_json
  end

  def csat
    ncco = service.ivr_csat
    render json: ncco.to_json
  end

  def csat_voicememo
    service.csat_voicememo
    head :ok
  end

  def wait_time_sms
    ncco = service.ivr_wait_time_sms
    render json: ncco.to_json
  end

  def callback_number_request
    ncco = service.callback_number_request
    render json: ncco.to_json
  end

  def callback_number_confirm
    ncco = service.callback_number_confirm
    render json: ncco.to_json
  end

  def graceful_outage
    ncco = service.graceful_outage
    render json: ncco.to_json
  end

  def over_capacity_deflection
    ncco = service.over_capacity_deflection
    render json: ncco.to_json
  end

  def after_hours_deflection
    ncco = service.over_capacity_deflection
    render json: ncco.to_json
  end

  def waiting
    ncco = service.ivr_waiting
    render json: ncco.to_json
  end

  def announcement_deflection
    ncco = service.announcement_deflection
    render json: ncco.to_json
  end

  def holding
    ncco = service.holding
    render json: ncco.to_json
  end

  def payment
    ncco = service.payment
    render json: ncco.to_json
  end

  def dnc
    ncco = service.dnc
    head :ok
  end

  def hangup
    ncco = service.hangup
    render json: ncco.to_json
  end

  def task_virtual_agent_start
    ncco = service.start_task_virtual_agent
    render json: ncco.to_json
  end

  def task_virtual_agent_finish
    ncco = service.finish_task_virtual_agent
    render json: ncco.to_json
  end

  def virtual_agent_stream_start
    ncco = service.start_virtual_agent_stream
    render json: ncco.to_json
  end

  def virtual_agent_stream_end
    ncco = service.virtual_agent_stream_ended
    render json: ncco.to_json
  end

  def agent_voice_detection_missed
    ncco = service.agent_voice_detection_missed
    render json: ncco.to_json
  end

  def escalation
    ncco = service.ivr_escalation
    render json: ncco.to_json
  end

  def escalation_failure
    ncco = service.ivr_escalation_failed
    render json: ncco.to_json
  end

  private

  # for race conditions concerning timing of nexmo API requests, we want to lock per request with a
  # higher acquire time to act as a sync lock for incoming api calls, and with a much lower sleep time of 10ms
  # due to what was noticed in logs, about how race condition requests were coming in within 200ms of each other
  def lock_request_participant
    participant_uuid = params[:uuid] || params[:voip_participant_uuid]
    Redis::Tenant.with_lock("nexmo-voice-request-lock:#{participant_uuid}", life: 2, sleep: 10) do
      yield
    rescue Redis::Lock::LockNotAcquired => e
      Rails.logger.error("Nexmo request lock failed to be ******** for participant #{participant_uuid}")
    end
  end

  def should_verify_authorized
    false
  end

  def service
    @service ||= NexmoService::VoiceCallback.new(params)
  end

  def extract_sip_phone_number
    [:to, :from].each do |key|
      next unless params[key].present?

      result = params[key].match(SIP_REGEX)
      next unless result.present?

      phone_number = Phonelib.parse(result[1])
      next unless phone_number.possible?

      params["Original_#{key}"] = params[key]

      # nexmo numbers come in as e164 format without + prefix
      params[key] = phone_number.e164('')
    end
  end

  def set_uuids
    call_uuid = params[:uuid] || params[:voip_participant_uuid]
    RequestStore.store[:call_uuid] = call_uuid if call_uuid.present?
    RequestStore.store[:conversation_uuid] = params[:conversation_uuid] if params[:conversation_uuid].present?
  end
end

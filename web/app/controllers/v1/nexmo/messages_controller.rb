class V1::Nexmo::MessagesController < ApplicationController
  wrap_parameters false

  skip_before_action :authenticate
  before_action :require_sms_parameters

  def inbound
    head :ok
  end

  def status
    head :ok
  end

  def sms_inbound
    messaging_service.message_callback(body: params[:text], medias: [])
    head :ok
  end

  def sms_status
    messaging_service.message_status_callback(message_status: params[:status], error_code: params['err-code'])
    head :ok
  end

  def should_verify_authorized
    false
  end

  private

  # These will not be sent to us if we receive an MMS from nexmo (not supported)
  # we want to respond with no_content to stop nexmo from retrying these bad requests
  def require_sms_parameters
    return head :no_content unless params[:to].present?
    return head :no_content unless params[:msisdn].present?

    head :no_content unless params[:messageId].present?
  end

  def messaging_service
    Messaging::Callback::Nexmo.new(
      message_sid: params[:messageId],
      to: params[:to],
      from: params[:msisdn],
      messaging_service_sid: nil
    )
  end
end

# frozen_string_literal: true

class V1::ChatShortcutCategoriesController < ApplicationController
  before_action :require_category, only: %i[update destroy]
  before_action :authorize_category

  def index
    categories = ChatShortcutCategory.page(params[:page]).per(params[:per_page])
    add_pagination(categories)
    render json: ChatShortcutCategoryRepresenter.for_collection.prepare(categories)
  end

  def create
    category = ChatShortcutCategory.create!(permitted_params)
    render json: ChatShortcutCategoryRepresenter.new(category)
  rescue ActiveRecord::RecordNotUnique
    render json: {message: "Name has already been taken"}, status: :bad_request
  end

  def update
    @category.update!(permitted_params)
    render json: ChatShortcutCategoryRepresenter.new(@category)
  rescue ActiveRecord::RecordNotUnique
    render json: {message: "Name has already been taken"}, status: :bad_request
  end

  def destroy
    @category.destroy!
    head :ok
  end

  private

  def require_category
    @category = ChatShortcutCategory.find(params[:id])
  end

  def authorize_category
    authorize @category || ChatShortcutCategory
  end

  def permitted_params
    params.permit(:name)
  end
end

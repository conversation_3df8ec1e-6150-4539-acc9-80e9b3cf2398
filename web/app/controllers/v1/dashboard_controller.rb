 class V1::DashboardController < ApplicationController
  around_action :use_dashboard_replica

  def summary
    authorize :summary_stat, :show?
    @summary = ::Dashboard::Summary.new(data_scope(:call), data_scope(:chat), time_frame)
    render json: @summary
  end

  def call
    authorize :call_stat, :show?
    @call_stats = ::Dashboard::CallStats.new(data_scope(:call), filters(:call))
    render json: @call_stats
  end

  def chat
    authorize :chat_stat, :show?
    @chat_stats = ::Dashboard::ChatStats.new(data_scope(:chat), filters(:chat))
    render json: @chat_stats
  end

  def email
    authorize :email_stat, :show?
    @email_stats = ::Dashboard::EmailStats.new(email_filters(:email))

    render json: @email_stats
  end

  private

  def data_scope(comm_type)
    Dashboard::DataScope.for_user(current_user, comm_type)
  end

  def filters(comm_type)
    params.require(:time_frame)
    filter_params = params.permit(
      time_frame: [:from, :to, :unit],
      menus: [],
      teams: [],
      agents: [],
      queue_report_groups: []
    )
    ::Dashboard::Filters.new(filter_params.merge(data_scope: data_scope(comm_type)).to_h)
  end

  def email_filters(comm_type)
    params.require(:time_frame)
    filter_params = params.permit(
      :page,
      :per_page,
      :sort_column,
      :sort_direction,
      :mode,
      :mailbox,
      :queue,
      :status,
      :lang,
      :email_id,
      :agent,
      :agent_id,
      :agent_email,
      :custom_agent_id,
      :direction,
      :location,
      time_frame: [:from, :to]
    )
    ::Dashboard::EmailFilters.new(filter_params.merge(data_scope: data_scope(comm_type)).to_h)
  end

  def time_frame
    time_frame_params = params.permit(time_frame: %i[from to]).fetch(:time_frame, nil)
    if time_frame_params.present?
      ::Dashboard::TimeFrame.from_params(time_frame_params.to_h)
    else
      nil
    end
  end
end

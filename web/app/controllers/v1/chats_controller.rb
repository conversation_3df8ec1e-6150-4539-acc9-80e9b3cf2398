class V1::ChatsController < ApplicationController

  class SummaryRepresenter < ChatRepresenter
    include SummarizableRepresenter
  end

  around_action :use_dashboard_replica, only: [:index, :previous, :show]
  before_action :require_chat, only: [:show, :observe, :unobserve, :messages, :force_end]
  before_action :require_chat_with_message_history_store, only: [:messages]
  before_action :authorize_chat

  def index
    index_with_options(time_column: :created_at)
  end

  def previous
    index_with_options(time_column: :ends_at)
  end

  def show
    render json: SummaryRepresenter.for_user(current_user).new(@chat)
  end

  def observe
    ObserveService.new(current_user, @chat).observe

    data = ChatRepresenter.new(@chat).as_json
    render json: data
  end

  def unobserve
    ObserveService.new(current_user, @chat).unobserve
    head :ok
  end

  def tokens
    params.require(:device_id)

    provider_type = params[:provider_type] || 'twilio_conversations'
    payload = ChatService::Provider::TokenService.generate_token(provider_type: provider_type,
                                                                 user: current_user,
                                                                 device_id: params[:device_id])
    render json: payload
  end

  def messages
    since_id = params[:since_id].presence || 0
    messages = ::Messaging::MessageStore.get_latest(comm: @chat, since_id: since_id)

    render json: ChatMessageRepresenter.for_user(current_user).for_collection.prepare(messages)
  end

  def force_end
    raise ServiceException, 'Chat already ended' if @chat.ended?

    ChatService::ProgressService.new(@chat).force_end_chat(by_user: current_user)
    head :ok
  end

  private

  def index_with_options(additional_options = {})
    options = params.permit(:from, :to, :manager_id, :device_id, :end_user_id, :agent_id,
      :in_progress, :has_transfer, :repeated, :page, :per_page, :search_text, :sort_direction, :sort_column,
      status: [], sub_status: [], menus: [], agents: [], teams: [], includes: []
    ).to_h

    # Set the accessibility of the current_user
    scope = policy_scope(Chat.all, options[:status])

    scope = scope.not_shortly_abandoned(options[:from], options[:to])

    chats = ChatService::Base.search(scope, options.merge(additional_options))

    add_pagination(chats)

    render json: ChatRepresenter.for_collection.prepare(chats)
  end

  def require_chat
    @chat = Chat.includes_menus_with_settings.find params[:id]
  end

  def authorize_chat
    authorize @chat || Chat
  end

  def policy_scope(scope, statuses)
    ChatPolicy::Scope.new(current_user, scope, statuses).resolve
  end

  def require_chat_with_message_history_store
    raise ::ServiceException, "This chat type does not support the correct message store" unless @chat.history_store == Chat::MESSAGE_HISTORY_MESSAGE_STORE
  end
end

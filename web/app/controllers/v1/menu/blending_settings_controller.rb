# frozen_string_literal: true

class V1::Menu::BlendingSettingsController < ApplicationController
  before_action :authorize_blending_setting

  def show
    return render_not_found unless preconditions_valid?

    render_blending
  end

  def update
    updater = Menu::BlendingSettingUpdater.new(params:)
    updater.update(params: permitted_params)
    render_blending
  end

  private

  def resource_class
    Menu::BlendingSetting
  end

  def permitted_params
    params.permit(
      :parent,
      :enabled,
      :condition_operator,
      :sla_enabled,
      :availability_enabled,
      :availability_threshold,
      :queue_length_enabled,
      :queue_length_threshold,
      sla_thresholds: [
        :condition,
        :threshold_per_queue,
        :time_frame_per_queue,
        queue: [
          :menu_id,
          :lang,
          :channel
        ]
      ]
    )
  end

  def authorize_blending_setting
    authorize resource_class
  end

  def render_blending
    finder_result = Menu::BlendingSettingFinder.new.find_for_menu(menu_id: params[:menu_id], lang: params[:lang])
    render json: BlendingSettingFinderRepresenter.new(finder_result)
  end

  def render_not_found
    render json: { message: 'Not Found' }, status: :not_found
  end

  def preconditions_valid?
    return false unless Menu::Base.where(id: params[:menu_id]).exists?

    Menu::Setting.where(menu_id: params[:menu_id], lang: params[:lang], current: true).exists?
  end
end

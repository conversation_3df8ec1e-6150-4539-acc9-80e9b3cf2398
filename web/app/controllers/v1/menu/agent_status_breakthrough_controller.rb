class V1::Menu::AgentStatusBreakthroughController < ApplicationController
  before_action :require_setting
  before_action :authorize_setting
  def update
    updater = Menu::SettingUpdater.new(menu_setting: @setting, params: permitted_params)
    if updater.update_status_breakthrough_dap
      render json: Menu::SettingRepresenter.new(@setting)
    else
      render json: { errors: @setting.errors }, status: :bad_request
    end
  end

private

  def require_setting
    @menu = ::Menu::Base.find(params.require(:menu_id))
    @setting = @menu.setting(lang: params.require(:lang))
    if @setting.blank?
      raise ActiveRecord::RecordNotFound, "Invalid lang #{params[:lang]}"
    end
  end

  def authorize_setting
    authorize resource_class
  end

  def permitted_params
    params
      .permit(:menu_id, :lang, :agent_status_breakthrough_enabled, :allow_breakthrough_dap_only, selected_dap_list: [])
  end

  def resource_class
    Menu::Base
  end
end

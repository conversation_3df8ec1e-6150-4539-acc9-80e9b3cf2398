# frozen_string_literal: true

class V1::Menu::WhisperSettingsController < ApplicationController
  before_action :authorize_setting

  def show
    return render_not_found unless preconditions_valid?

    setting_finder = Whisper::SettingFinder.new
    render json: { whisper: setting_finder.find_for_menu(menu_id: params[:menu_id], lang: params[:lang]) }
  end

  private

  def render_not_found
    render json: { message: 'Not Found' }, status: :not_found
  end

  def preconditions_valid?
    return false unless Menu::Base.where(id: params[:menu_id]).exists?

    Menu::Setting.where(menu_id: params[:menu_id], lang: params[:lang], current: true).exists?
  end

  def authorize_setting
    authorize Menu::Setting::Whisper
  end
end

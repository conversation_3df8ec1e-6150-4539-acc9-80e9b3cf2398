class V1::Menu::PhoneNumberAssignmentsController < ApplicationController
  before_action :find_phone_number_assignment, only: [:update, :destroy]
  before_action :authorize_phone_number_assignment

  def index
    @phone_number_assignments = ordered_assignments
    render json: Menu::PhoneNumberAssignmentRepresenter.for_collection.prepare(@phone_number_assignments)
  end

  def create
    @phone_number_assignment = Menu::PhoneNumberAssignment.new(permitted_create_params)

    if verification_required? && !@phone_number_assignment.is_number_verified?
      render json: { errors: { "base": ['Phone number is not verified'] } }, status: :expectation_failed
    elsif @phone_number_assignment.save
      render json: Menu::PhoneNumberAssignmentRepresenter.new(@phone_number_assignment)
    else
      render json: { errors: @phone_number_assignment.errors }, status: :bad_request
    end
  rescue ActiveRecord::RecordNotUnique => e
    render json: { errors: { "base": ['Could not create the assignment - record not unique'] } }, status: :conflict
  end

  def update
    if @phone_number_assignment.update(permitted_update_params)
      render json: Menu::PhoneNumberAssignmentRepresenter.new(@phone_number_assignment)
    else
      render json: { errors: @phone_number_assignment.errors }, status: :bad_request
    end
  rescue ActiveRecord::RecordNotUnique => e
    render json: { errors: { "base": ['Could not update the assignment - record not unique'] } }, status: :conflict
  end

  def destroy
    @phone_number_assignment.destroy!
    head :ok
  end

  private

  def verification_required?
    return true if params[:direction].present? && params[:direction] == :inbound

    OutboundNumber.require_verification?
  end

  def find_phone_number_assignment
    @phone_number_assignment = Menu::PhoneNumberAssignment.find(params[:id])
  end

  def permitted_create_params
    params.permit(:menu_id, :lang, :phone_number_id, :default, :direction)
  end

  def permitted_update_params
    params.permit(:default)
  end

  def ordered_assignments
    Menu::PhoneNumberAssignment.with_phone_number.all_ordered_with_default_assigned(menu_id: params[:menu_id],
                                                                  lang: params[:lang],
                                                                  direction: params[:direction])
  end

  def authorize_phone_number_assignment
    authorize @phone_number_assignment || Menu::PhoneNumberAssignment
  end
end

# frozen_string_literal: true

module V1
  module Menu
    class FallbackIvrSettingsController < ApplicationController
      before_action :require_setting
      before_action :authorize_setting

      def show
        return render_not_found unless preconditions_valid?

        menu_setting = Menu::Setting.where(menu_id: params[:menu_id], lang: params[:lang])

        render json: FallbackIvrSettingRepresenter.new(menu_setting.fallback_ivr)
      end

      private

      def render_not_found
        render json: { message: 'Not Found' }, status: :not_found
      end

      def preconditions_valid?
        return false unless Menu::Base.where(id: params[:menu_id]).exists?

        Menu::Setting.where(menu_id: params[:menu_id], lang: params[:lang]).exists?
      end

      def authorize_setting
        authorize Menu::Setting::FallbackIvr
      end
    end
  end
end

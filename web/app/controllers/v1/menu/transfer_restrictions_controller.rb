class V1::Menu::TransferRestrictionsController < ApplicationController
  before_action :require_menu_setting
  before_action :authorize_transfer_restrictions

  # GET /v1/menus/:menu_id/transfer_restrictions/:lang
  def show
    @restriction_setting = Menu::TransferRestrictionSetting.from_menu_setting(@menu_setting)
    render json: Menu::TransferRestrictionSettingRepresenter.new(@restriction_setting)
  end

  # PATCH|PUT /v1/menus/:menu_id/transfer_restrictions/:lang
  def update
    @updater = Menu::TransferRestrictionUpdater.new(menu_setting: @menu_setting)
    @updater.update(Menu::TransferRestrictionSetting.new(permitted_params.to_h))

    @restriction_setting = Menu::TransferRestrictionSetting.from_menu_setting(@menu_setting)

    render json: Menu::TransferRestrictionSettingRepresenter.new(@restriction_setting)
  rescue Menu::TransferRestrictionUpdater::InvalidSettingError => e
    render json: { message: e.message }, status: :bad_request
  end

  private

  def require_menu_setting
    @menu_setting = Menu::Setting.includes(transfer_restriction: { selected_queues: :menu }).find_by!(
      menu_id: params.require(:menu_id),
      lang: params.require(:lang)
    )
  end

  def authorize_transfer_restrictions
    authorize Menu::Base
  end

  def permitted_params
    params.permit(
      :inherited,
      restriction: [
        :enabled,
        :allow_from,
        :allow_to,
        :transfer_queue_option,
        :disallow_to_same_queue,
        {
          selected_queues: [:lang, :menu_id, :human_agent, :virtual_agent, :menu_readout]
        }
      ]
    )
  end
end

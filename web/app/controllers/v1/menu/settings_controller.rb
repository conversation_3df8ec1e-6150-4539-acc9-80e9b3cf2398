class V1::Menu::SettingsController < ApplicationController
  before_action :require_setting
  before_action :authorize_setting

  # GET /v1/menus/:menu_id/settings/:lang
  def show
    set_parent_target_metric_attr(@setting)
    render_setting
  end

  # PUT|PATCH /v1/menus/:menu_id/settings/:lang
  def update
    updater = Menu::SettingUpdater.new(menu_setting: @setting, params: permitted_params.to_h)
    if updater.update!
      set_parent_target_metric_attr(@setting)
      render_setting
    else
      render json: { errors: @setting.errors }, status: :bad_request
    end
  rescue ActiveRecord::RecordInvalid => e
    render json: { errors: e.record.errors }, status: :bad_request
  rescue StandardError => e
    raise ServiceException, e.message
  end

  def render_setting
    render json: Menu::SettingRepresenter.new(@setting).to_json(
      user_options: {
        include_queue_path: true,
        user_status_list_inheritance: user_status_list_inheritance
      }
    )
  end

  def user_status_list_inheritance
    @user_status_list_inheritance = Menu::UserStatusList::Finder.menu_ancestor_to_inherit_user_status_list(@setting.menu_id)
    {
      parent_name: @user_status_list_inheritance&.name,
      user_status_list_id_to_inherit: @user_status_list_inheritance&.user_status_list&.user_status_list_id
    }
  end

  def unset_user_status_list
    @setting.user_status_list.user_status_list_id = nil
    @setting.save

    render_setting
  end

  private

  def require_setting
    @menu = ::Menu::Base.find(params[:menu_id])
    @setting = @menu.setting(lang: params[:lang])
    if @setting.blank?
      raise ActiveRecord::RecordNotFound, "Invalid lang #{params[:lang]}"
    end
  end

  def set_parent_target_metric_attr(setting)
    return if setting.blank?
    setting.parent_target_metric = Menu::Setting::CurrentTargetMetric.call(menu_id: setting.menu.parent_id, lang: setting.lang)
  end

  def authorize_setting
    authorize resource_class
  end

  def permitted_params
    params.require(:setting).permit(policy(resource_class).permitted_attributes)
  end

  def resource_class
    Menu::Base
  end
end

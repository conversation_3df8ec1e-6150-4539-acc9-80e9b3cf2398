# frozen_string_literal: true

class V1::Menu::WrapUpSettingsController < ApplicationController
  before_action :authorize_setting
  before_action :require_setting

  def show
    render_wrap_up
  end

  def update
    wrap_up_updater = Menu::WrapUp::Updater.new(@setting)
    wrap_up_updater.update(permitted_params.to_h)
    render_wrap_up
  end

  private

  def permitted_params
    params.require(:wrap_up).permit(policy(resource_class).permitted_attributes)
  end

  def render_wrap_up
    wrap_up = wrap_up_finder.find_for_menu(@setting)
    render json: { wrap_up: FindSettingResultRepresenter.new(wrap_up) }
  end

  def wrap_up_finder
    Menu::WrapUp::Finder
  end

  def require_setting
    @setting = Menu::Setting.find_by!(menu_id: params[:menu_id], lang: params[:lang])
  end

  def resource_class
    Menu::Setting::WrapUp
  end

  def authorize_setting
    authorize resource_class
  end
end

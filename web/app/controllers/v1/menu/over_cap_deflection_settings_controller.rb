# frozen_string_literal: true

class V1::Menu::OverCapDeflectionSettingsController < ApplicationController
  before_action :authorize_setting

  def show
    return render_not_found unless preconditions_valid?

    setting_finder = OverCapDeflection::SettingFinder.new
    render json: {
      over_cap_deflection: Menu::OverCapDeflectionSettingsRepresenter.new(
        setting_finder.find_for_menu(menu_id: params[:menu_id], lang: params[:lang])
      )
    }
  end

  private

  def render_not_found
    render json: { message: 'Not Found' }, status: :not_found
  end

  def preconditions_valid?
    return false unless Menu::Base.where(id: params[:menu_id]).exists?

    Menu::Setting.where(menu_id: params[:menu_id], lang: params[:lang]).exists?
  end

  def authorize_setting
    authorize Menu::Setting::OverCapDeflection
  end
end

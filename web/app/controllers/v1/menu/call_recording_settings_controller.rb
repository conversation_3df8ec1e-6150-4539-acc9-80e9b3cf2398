# frozen_string_literal: true

class V1::Menu::CallRecordingSettingsController < ApplicationController
  before_action :authorize_setting
  before_action :require_setting

  def show
    render_call_recording_setting
  end

  def update
    call_recording_updater = Menu::CallRecordingSetting::Updater.new(@setting)
    call_recording_updater.update(permitted_params.to_h)
    render_call_recording_setting
  end

  private

  def permitted_params
    params.require(:call_recording_setting).permit(policy(resource_class).permitted_attributes)
  end

  def authorize_setting
    authorize resource_class
  end

  def resource_class
    Menu::Setting::CallRecordingSetting
  end

  def call_recording_finder
    Menu::CallRecordingSetting::Finder
  end

  def render_call_recording_setting
    call_recording_setting = call_recording_finder.find_for_menu(@setting)
    render json: FindSettingResultRepresenter.new(call_recording_setting)
  end

  def require_setting
    @setting = Menu::VoiceCallSetting.find_by!(menu_id: params[:menu_id], lang: params[:lang])
  end
end

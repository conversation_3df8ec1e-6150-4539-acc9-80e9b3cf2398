class V1::Menu::CodeMenuAssignmentsController < ApplicationController
  before_action :require_disposition_code_menu_assignment, only: [:destroy]
  before_action :authorize_setting

  def show
    call_code_menu_finder = WrapUp::CodeMenuAssignmentFinder.new(menu_id: params[:menu_id], lang: params[:lang], channel: :voice_call)
    chat_code_menu_finder = WrapUp::CodeMenuAssignmentFinder.new(menu_id: params[:menu_id], lang: params[:lang], channel: :chat)
    render json: { call: call_code_menu_finder.find_list_assignment, chat: chat_code_menu_finder.find_list_assignment }
  end

  def patch
    exist?(code_menu_assignment_params) ? update : create
  end

  private

  def exist?(code_menu_assignment)
    menu_id = params[:menu_id]
    channel = code_menu_assignment[:channel]
    lang = code_menu_assignment[:lang]
    call_direction_type = code_menu_assignment[:call_direction_type]
    @code_menu_assignment = ::Menu::CodeMenuAssignment.includes(:disposition_menu_list)
                                                      .where(menu_id:, channel:, lang:, call_direction_type:).first
    !@code_menu_assignment.blank?
  end

  def authorize_setting
    authorize @code_menu_assignment || Menu::CodeMenuAssignment
  end

  def require_disposition_code_menu_assignment
    @code_menu_assignment = ::Menu::CodeMenuAssignment.includes(:disposition_menu_list).find(params[:id])
  end

  def update
    @code_menu_assignment.update!(code_menu_assignment_params)
    render_code_menu_assignment(@code_menu_assignment)
  end

  def create
    raise ServiceException, "Disposition List Is Required!" unless params[:code_menu_assignment][:list_id]
    @code_menu_assignment = ::Menu::CodeMenuAssignment.new(code_menu_assignment_params)
    @code_menu_assignment.save!
    render_code_menu_assignment(@code_menu_assignment)
  end

  def render_code_menu_assignment(code_menu_assignment)
    render json: ::Menu::CodeMenuAssignmentRepresenter.new(code_menu_assignment)
  end

  def permitted_params(code_menu_assignment)
    code_menu_assignment.permit(policy(::Menu::CodeMenuAssignment).permitted_attributes)
  end

  def code_menu_assignment_params
    code_assignment_params = permitted_params(params[:code_menu_assignment])
    channel = code_assignment_params[:channel].to_sym
    code_assignment_params[:call_direction_type] = case channel
                                                   when :outbound_call
                                                     :outbound
                                                   when :voice_call
                                                     :inbound
                                                   end
    code_assignment_params[:channel] = :voice_call if channel == :outbound_call
    code_assignment_params
  end
end

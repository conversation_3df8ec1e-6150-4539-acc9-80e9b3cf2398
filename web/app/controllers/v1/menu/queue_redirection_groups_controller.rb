class V1::Menu::QueueRedirectionGroupsController < ApplicationController
  before_action :authorize_setting
  before_action :require_setting, only: [:create, :update, :enable]

  def index
    redirection_data = redirection_group_service.list
    redirection_groups_setting = Menu::QueueRedirectionGroupSettingsRepresenter.for_collection.prepare(redirection_data[:redirection_groups_setting])

    render json: {
      redirection_enabled: redirection_data[:redirection_enabled],
      redirection_groups_setting:
    }
  end

  def create
    create_redirection_group
  end

  def destroy
    redirection_group = redirection_group_service.remove(params[:redirection_group_level])
    render json: Menu::QueueRedirectionGroupRepresenter.new(redirection_group)
  end

  def enable
    response = redirection_group_service.enablement(setting, params)
    render json: response
  end

  def update
    redirection_group_setting = redirection_group_service.update(permitted_update_params)
    render_redirection_group_setting(redirection_group_setting)
  end

  private

  def create_redirection_group
    redirection_group_setting = redirection_group_service.create
    render_redirection_group_setting(redirection_group_setting)
  end

  def permitted_params
    params.permit(:menu_id, :lang)
  end

  def redirection_group_service
    Menu::QueueRedirectionGroupsService.new(permitted_params)
  end

  def render_redirection_group_setting(redirection_group_setting)
    return render json: { errors: redirection_group_setting.errors }, status: :bad_request if redirection_group_setting.errors.present?

    render json: Menu::QueueRedirectionGroupSettingsRepresenter.new(redirection_group_setting)
  end

  def require_setting
    raise ServiceException, 'Invalid setting' if setting.blank?
  end

  def setting
    menu_id = redirection_group_service.process_menu_id(params[:menu_id])
    Menu::Setting.find_by(menu_id:, lang: params[:lang])
  end

  def authorize_setting
    authorize resource_class
  end

  def resource_class
    Menu::QueueRedirectionGroup
  end

  def permitted_update_params
    params.require(:redirection_setting).permit(policy(resource_class).permitted_attributes)
  end
end

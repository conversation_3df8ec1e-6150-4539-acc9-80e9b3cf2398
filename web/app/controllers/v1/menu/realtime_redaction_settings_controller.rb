class V1::Menu::RealtimeRedactionSettingsController < ApplicationController
  before_action :authorize_realtime_redaction_settings

  # GET /v1/menus/:menu_id/realtime_redaction_settings/:lang
  def show
    render_realtime_redaction_setting
  end

  # PATCH|PUT /v1/menus/:menu_id/realtime_redaction_settings/:lang
  def update
    updater = Menu::RealtimeRedactionSettingUpdater.new(permitted_params)
    updater.update(permitted_update_params.to_h)
    render_realtime_redaction_setting
  end

  private

  def authorize_realtime_redaction_settings
    authorize ::Menu::Base
  end

  def render_realtime_redaction_setting
    finder = RealtimeRedactionSetting::Finder.new(menu_id: permitted_params[:menu_id], lang: permitted_params[:lang])
    build_realtime_redaction_setting_data = finder.find_realtime_redaction_setting_for_queue

    render json: RealtimeRedactionSettingResultRepresenter.new(build_realtime_redaction_setting_data)
  end

  def permitted_params
    params.permit(:menu_id, :lang)
  end

  def permitted_update_params
    params.require(:realtime_redaction_setting).permit(
      :enabled,
      :inherited,
      :platform_id,
      :inspect_template_id,
      :de_identify_template_id,
      operation_settings: [
        :upload_unredacted_notes_in_crm_and_metadata,
        :send_unredacted_transcript_to_consumer,
        store_unredacted_messages: [
          :enabled,
          :upload_unredacted_transcript_to_crm_ticket,
          :upload_unredacted_transcript_to_external_storage
        ]
      ],
      redaction_settings: [
        :agent_session_notes_redaction,
        :allow_agent_view_unredacted_message,
        agent_incoming_messages_redaction: [
          :enabled,
          :on_virtual_agent,
          :on_human_agent
        ],
        agent_outgoing_messages_redaction: [
          :enabled,
          :on_virtual_agent,
          :on_human_agent
        ]
      ]
    )
  end
end

class V1::Menu::PostSessionTransferSettingsController < ApplicationController
  before_action :authorize_post_session_transfer_setting

  def show
    render_post_session_transfer_setting
  end

  def update
    updater = PostSessionTransferSetting::Updater.new(permitted_params)
    updater.update!(permitted_update_params.to_h)
    render_post_session_transfer_setting
  end

  private

  def permitted_params
    params.permit(:menu_id, :lang)
  end

  def permitted_update_params
    params.require(:post_session_transfer_setting).permit(policy(resource_class).permitted_attributes)
  end

  def resource_class
    Menu::PostSessionTransferSetting
  end

  def render_post_session_transfer_setting
    finder = PostSessionTransferSetting::Finder.new(menu_id: permitted_params[:menu_id], lang: permitted_params[:lang])
    post_session_transfer_setting_data = finder.find_post_session_transfer_setting_for_queue

    render json: PostSessionTransferSettingResultRepresenter.new(post_session_transfer_setting_data)
  end

  def authorize_post_session_transfer_setting
    authorize resource_class
  end
end

# frozen_string_literal: true

class V1::Menu::ContactListSettingsController < ApplicationController
  before_action :authorize_setting
  before_action :require_menu_setting
  before_action :get_parent_setting, only: [:show, :update]

  # GET /v1/menus/:menu_id/settings/contact_list/:lang
  def show
    render_contact_list
  end

  # PUT|PACH /v1/menus/:menu_id/settings/contact_list/:lang
  def update
    @menu_setting.update!(permitted_params)
    Menu::SettingUpdater.new(menu_setting: @menu_setting, params: permitted_params).update_setting_descendants

    render_contact_list
  end

  private

  def get_parent_setting
    @parent_menu_setting = Contact::SettingFinder.find_for_menu(params[:menu_id].to_i, params[:lang])
  end

  def render_contact_list
    res_contact_list = {
      contact_list_settings: {
        has_parent: @parent_menu_setting.present?,
        sources: source,
        source_after_reset: source_after_reset
      }
    }
    render json: res_contact_list
  end

  def source
    setting = @menu_setting.contact_override_inheritance && @parent_menu_setting ? @parent_menu_setting : @menu_setting
    {
      menu_id: setting.menu_id,
      lang: setting.lang,
      menu_name: setting.name,
      settings: {
        contact_list_id: setting.contact_list_id,
        accessible_to_global_contact: setting.accessible_to_global_contact,
        contact_override_inheritance: @menu_setting.contact_override_inheritance
      }
    }
  end

  def source_after_reset
    return nil unless @parent_menu_setting.present?

    {
      menu_id: @parent_menu_setting.menu_id,
      lang: @parent_menu_setting.lang,
      menu_name: @parent_menu_setting.name,
      settings: {
        contact_list_id: @parent_menu_setting.contact_list_id,
        accessible_to_global_contact: @parent_menu_setting.accessible_to_global_contact
      }
    }
  end

  def require_menu_setting
    @menu_setting = Menu::Setting.find_by!(menu_id: params[:menu_id], lang: params[:lang])
  end

  def permitted_params
    params.require(:contact_list_settings).permit(:contact_list_id, :contact_override_inheritance, :accessible_to_global_contact)
  end

  def authorize_setting
    authorize Menu::Base
  end
end

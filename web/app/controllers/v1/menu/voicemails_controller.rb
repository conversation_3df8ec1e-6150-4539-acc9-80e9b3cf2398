# frozen_string_literal: true

module V1
  module Menu
    class VoicemailsController < ApplicationController
      before_action :require_queue_keys, only: [:show, :destroy]
      before_action :authorize_voicemail

      # GET /v1/menus/:menu_id/voicemails/:lang
      def show
        voice_mails = VoicemailQueue.active_queues(queue_keys: @queue_keys)

        render json: { menu_id: @menu.id, lang: @lang, voicemail_count: voicemail_count(voice_mails) }
      end

      # DELETE /v1/menus/:menu_id/voicemails/:lang
      def destroy
        CallService::VoicemailService.bulk_remove!(queue_keys: @queue_keys, crm_post: false)

        head :accepted
      end

      # GET /v1/menus/voicemails
      def index
        voice_mails = VoicemailQueue.active_queues

        render json: { voicemail_count: voicemail_count(voice_mails) }
      end

      # DELETE /v1/menus/voicemails
      def destroy_all
        CallService::VoicemailService.bulk_remove!(crm_post: false)

        head :accepted
      end

      private

      def voicemail_count(voicemails)
        voicemails.reduce(0) { |sum, hash| sum + hash[:count] }
      end

      def require_queue_keys
        @menu = ::Menu.find(params.require(:id))
        @lang = params.require(:lang)
        @voice_call_setting = @menu.voice_call_setting(lang: @lang)

        raise ActiveRecord::RecordNotFound, "Invalid lang #{@lang}" if @voice_call_setting.blank?

        @queue_keys = @voice_call_setting.queue_groups.map { |group| group.as_q.key }
      end

      def authorize_voicemail
        authorize Call, policy_class: ::Menu::VoicemailPolicy
      end
    end
  end
end

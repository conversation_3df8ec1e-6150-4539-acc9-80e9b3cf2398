# frozen_string_literal: true

class V1::Menu::SdkCustomDataSettingsController < ApplicationController
  before_action :require_sdk_custom_data_setting
  before_action :authorize_sdk_custom_data_setting

  def show
    render_sdk_custom_data
  end

  def update
    updater = Menu::SdkCustomDataSettingUpdater.new(sdk_custom_data_setting: @sdk_custom_data_setting)
    updater.update(permitted_params.to_h)
    render_sdk_custom_data
  end

  private

  def require_sdk_custom_data_setting
    @sdk_custom_data_setting = Menu::SdkCustomDataSetting.find_by!(menu_id: params[:menu_id], lang: params[:lang])
  end

  def resource_class
    Menu::SdkCustomDataSetting
  end

  def permitted_params
    params.require(:sdk_custom_data).permit(policy(resource_class).permitted_attributes)
  end

  def authorize_sdk_custom_data_setting
    authorize resource_class
  end

  def render_sdk_custom_data
    finder = SdkCustomData::SdkCustomDataFinder.new
    sdk_custom_data = finder.find_for_menu(menu_id: params[:menu_id], lang: params[:lang])
    render json: FindSettingResultRepresenter.new(sdk_custom_data)
  end
end

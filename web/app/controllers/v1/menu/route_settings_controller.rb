# frozen_string_literal: true

class V1::Menu::RouteSettingsController < ApplicationController
  before_action :require_route_setting
  before_action :authorize_route_setting

  def show
    render_route
  end

  def update
    updater = Menu::RouteSettingUpdater.new(route_setting: @route_setting)
    updater.update(permitted_params.to_h)
    render_route
  end

  private

  def require_route_setting
    @route_setting = Menu::RouteSetting.find_by!(menu_id: params[:menu_id], lang: params[:lang])
  end

  def resource_class
    Menu::RouteSetting
  end

  def permitted_params
    params.require(:route).permit(policy(resource_class).permitted_attributes)
  end

  def authorize_route_setting
    authorize resource_class
  end

  def render_route
    finder = Route::RouteFinder.new
    route = finder.find_for_menu(menu_id: params[:menu_id], lang: params[:lang])
    render json: FindSettingResultRepresenter.new(route)
  end
end

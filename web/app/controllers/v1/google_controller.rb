class V1::GoogleController < ApplicationController
  skip_before_action :authenticate

  def oauth_callback
    # This is callback from admin console, don't use for agent widget
    CRM::CrmServer.request(
      url: '/google_oauth_callback',
      method: :post,
      params: {
        code: params[:code]
      }
    )
    # Need valid URL with hash value to close OAuth popup window
    redirect_to "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/google/oauth_callback_end#success=true", allow_other_host: true
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.warn(error: e) { "Error in Google callback rest : #{e.message}" }
    text = 'Unable to authenticate Google user'
    redirect_to "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/google/oauth_callback_end#success=false&message=#{text}", allow_other_host: true
  rescue => e
    Rails.logger.warn(error: e) { "Error in Google callback : #{e.message}" }
    text = 'Unable to authenticate Google user'
    redirect_to "https://#{URLHelper.host_with_port(request, remove_api: true)}/v1/google/oauth_callback_end#success=false&message=#{text}", allow_other_host: true
  end

  def oauth_callback_end
    # Use to close OAuth popup window
    render plain: ''
  end

  private

  def should_verify_authorized
    false
  end
end

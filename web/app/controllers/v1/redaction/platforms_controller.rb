class V1::Redaction::PlatformsController < ApplicationController
  before_action :authorize_data_loss_prevention_platform
  before_action :require_dlp_platform, only: [:show]
  before_action :require_data_loss_prevention_platform, only: %i[update destroy validate]

  def create
    permitted_params_for_create = params.permit(:name, :service_type)

    name = permitted_params_for_create.require(:name)
    service_type = permitted_params_for_create.require(:service_type)

    redaction_platform = DataLossPrevention::Platform.create!(
      name:          name,
      service_type:  service_type
    )

    render json: ::Redaction::PlatformRepresenter.new(redaction_platform)
  end

  def update
    permitted_params_for_update = params.permit(:name, :enabled)

    if params.key?(:name)
      permitted_params_for_update[:name] = params.require(:name)
    end

    # Check if 'enabled' exists in the params and throw a 'ParameterMissing' error if it's blank
    if params.key?(:enabled)
      permitted_params_for_update[:enabled] = params.require(:enabled)
    end

    @data_loss_prevention_platform.update!(permitted_params_for_update.to_h)

    render json: ::Redaction::PlatformRepresenter.new(@data_loss_prevention_platform)
  end

  def validate
    @data_loss_prevention_platform.validate_key!
    render json: ::Redaction::PlatformRepresenter.new(@data_loss_prevention_platform)
  end

  # GET /v1/redaction/platforms/:id
  def show
    render json: ::Redaction::PlatformRepresenter.new(@dlp_platform)
  end

  def index
    # Fetch optional parameters for status and enabled filters
    status_filter = params[:status_list] # e.g., ["valid", "need_attentions"]
    enabled_filter = params[:enabled] # e.g., true or false
    include_templates = params[:include_templates].nil? ? true : ActiveModel::Type::Boolean.new.cast(params[:include_templates])
    
    # Use the new scope to retrieve platforms based on the filters
    platforms = DataLossPrevention::Platform.by_status_and_enabled(statuses: status_filter, enabled: enabled_filter)
    platforms = platforms.includes(:templates) if include_templates

    render json: ::Redaction::PlatformRepresenter
                  .for_collection
                  .prepare(platforms)
                  .to_json(user_options: { include_templates: })
  end

  def destroy
    @data_loss_prevention_platform.destroy!
    update_chat_settings
    head :ok
  end

  private

  def require_data_loss_prevention_platform
    @data_loss_prevention_platform = DataLossPrevention::Platform.find(params[:id])
  end

  def authorize_data_loss_prevention_platform
    authorize DataLossPrevention::Platform, policy_class: DataLossPrevention::PlatformPolicy
  end

  def require_dlp_platform
    @dlp_platform = DataLossPrevention::Platform.includes(:templates, :credential).find(params[:id])
  end

  def update_chat_settings
    safe_company_update do |company|
      setting_params = {
        realtime_redaction: ::Settings::Shared::RealtimeRedaction.new.attributes
      }
      SettingsService::ChatSettingsUpdater.new(company, setting_params, current_user).call
    end
  end
end

class V1::Redaction::TemplatesController < ApplicationController
  before_action :authorize_data_loss_prevention_template
  before_action :require_data_loss_prevention_platform , only: %i[destroy index]

  # DELETE /v1/redaction/platforms/:id/templates/:template_id
  #
  # Removes a template from the platform and updates the platform's status
  # based on remaining templates.
  #
  # @api public
  #
  # @example
  #   DELETE /v1/redaction/platforms/1/templates/2
  #
  # @param [Integer] :platform_id The ID of the platform.
  # @param [Integer] :template_id The ID of the template to remove.
  #
  # @return [void]
  #
  # @raise [Pundit::NotAuthorizedError] When the user is not authorized to
  #   perform this action.
  def destroy
    # Find and delete the specified template
    @data_loss_prevention_platform.templates.find(params[:template_id]).destroy!

    # Update the platform's status based on the statuses of remaining templates
    @data_loss_prevention_platform.update_status_based_on_templates

    # Return HTTP 200 with no content
    head :ok
  end

  # GET /v1/redaction/platforms/:platform_id/templates
  #
  # Retrieves all valid templates for a specific platform.
  #
  # @param [Integer] :platform_id The ID of the platform.
  #
  # @return [JSON] List of valid templates.
  def index
    templates = @data_loss_prevention_platform.templates.valid_templates
    render json: templates.map { |template| ::Redaction::TemplateRepresenter.new(template).to_hash }
  end

  def authorize_data_loss_prevention_template
    authorize DataLossPrevention::Platform, policy_class: DataLossPrevention::PlatformPolicy
  end

  def require_data_loss_prevention_platform
    @data_loss_prevention_platform = DataLossPrevention::Platform.find(params[:platform_id])
  end
end

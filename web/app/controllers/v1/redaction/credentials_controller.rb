class V1::Redaction::CredentialsController < ApplicationController
  before_action :require_data_loss_prevention_platform, only: %i[create]
  before_action :authorize_data_loss_prevention_platform

  # Creates or updates a credential associated with a data loss prevention platform.
  #
  # @return [JSON] the status of the operation
  def create
    # Retrieve the credential parameter from the request
    uploaded_credential = params.require(:credential)

    # Rewind the file pointer and read the original filename
    uploaded_credential.rewind
    credential_filename = uploaded_credential.original_filename

    # Read the credential data
    credential_data = uploaded_credential.read
    # Validate the JSON format of the credential data
    validate_json!(credential_data)

    # Find or initialize a credential associated with the platform
    @credential = DataLossPrevention::Credential.where(platform: @data_loss_prevention_platform).first_or_initialize

    # Update the credential with the external metadata and filename
    @credential.update!(external_metadata: credential_data, file: credential_filename)
    @data_loss_prevention_platform.validate_key!
    @credential.update!(verified: true)

    # Use TemplateRepresenter to format associated templates
    templates = {
      inspect_templates: Redaction::TemplateRepresenter.for_collection.new(@data_loss_prevention_platform.templates.inspect_templates).to_hash,
      de_identify_templates: Redaction::TemplateRepresenter.for_collection.new(@data_loss_prevention_platform.templates.de_identify_templates).to_hash
    }

    render json: {
      credential: ::Redaction::CredentialRepresenter.new(@credential),
      templates: templates
    }
  end

  private

  def require_data_loss_prevention_platform
    @data_loss_prevention_platform = DataLossPrevention::Platform.find(params[:id])
  end

  def authorize_data_loss_prevention_platform
    authorize DataLossPrevention::Platform, policy_class: DataLossPrevention::PlatformPolicy
  end

  def validate_json!(data)
    JSON.parse(data)
  rescue JSON::ParserError, TypeError
    raise ServiceException, 'Invalid JSON format on service key'
  end
end

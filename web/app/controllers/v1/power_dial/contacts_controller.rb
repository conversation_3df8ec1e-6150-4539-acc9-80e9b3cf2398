# FIXME: Deprecated
class V1::PowerDial::ContactsController < ApplicationController
  before_action :authorize_power_dial_contact
  before_action :require_campaign
  before_action :require_contact, only: [:update, :destroy]

  def create
    phone_number = params.require(:contact).require(:phone_number)
    @contact = PowerDial::ContactService.save_contact(@campaign, phone_number, permitted_params.to_h)
    render_contact
  end

  def index
    contacts = @campaign.contacts
    render json: PowerDial::ContactRepresenter.for_collection.prepare(contacts)
  end

  def update
    @contact.update!(permitted_params)
    render_contact
  end

  def destroy
    @contact.destroy
    render_contact
  end

  def import
    file = params.require(:file)
    data = file.read

    saved_contact_ids = []
    parse_csv(file.tempfile) do |contact|
      next if contact[:phone_number_required].blank?
      attrs = {
        first_name: contact[:first_name_optional],
        last_name: contact[:last_name_optional]
      }
      result = PowerDial::ContactService.save_contact(@campaign, contact[:phone_number_required], attrs)
      saved_contact_ids << result.id
    end

    PowerDial::ContactService.delete_contacts(campaign: @campaign, exclude_ids: saved_contact_ids)
    render json: PowerDial::ContactRepresenter.for_collection.prepare(@campaign.contacts)
  end

  def export
    headers = ['Phone Number (required)', 'First Name (optional)', 'Last Name (optional)', 'Status', 'Attempts', 'Last Dialed At']
    data = CSV.generate() do |csv|
      csv << headers
      @campaign.contacts.each do |contact|
        row = [
          contact.phone_number,
          contact.first_name,
          contact.last_name,
          contact.status,
          contact.attempts,
          contact.last_dialed_at
        ]
        csv << row.to_a
      end
    end

    send_data(data, filename: "#{@campaign.name}_contacts.csv", type: "text/csv")
  end

  private
  def require_campaign
    @campaign = ::PowerDial::Campaign.find params[:campaign_id]
  end

  def require_contact
    @contact = ::PowerDial::Contact.find params[:id]
  end

  def render_contact
    render json: PowerDial::ContactRepresenter.new(@contact)
  end

  def permitted_params
    params.require(:contact).permit(:first_name, :last_name)
  end

  def authorize_power_dial_contact
    authorize ::PowerDial::Contact
  end

  def parse_csv(file_path, &block)
    CSV.foreach(file_path,
        headers: true,
        header_converters: :symbol,
        &block)
  rescue Exception => e
    raise ServiceException, "Invalid file format. Please upload CSV file"
  end
end

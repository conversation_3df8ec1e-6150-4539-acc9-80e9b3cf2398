class V1::PowerDial::CampaignsController < ApplicationController
  before_action :authorize_power_dial_campaign
  before_action :require_campaign, except: [:create, :index]

  def create
    @campaign = ::PowerDial::Campaign.create!(permitted_params)
    render_campaign
  end

  def index
    campaigns = ::PowerDial::Campaign.all.includes(:agents, teams: :members)
    render json: PowerDial::CampaignRepresenter.for_collection.prepare(campaigns)
  end

  def show
    render_campaign
  end

  def update
    @campaign.remove_greeting_audio! unless params[:campaign][:greeting_audio_url]
    @campaign.update!(permitted_params)
    render_campaign
  end

  def destroy
    PowerDial::CampaignService.delete(@campaign)
    render_campaign
  end

  def assign
    service = ::PowerDial::AssignmentService.new(@campaign, params.permit(agent_ids: [], team_ids: []))
    service.assign!
    render_campaign
  end

  def unassign
    service = ::PowerDial::AssignmentService.new(@campaign, params.permit(agent_ids: [], team_ids: []))
    service.unassign!
    render_campaign
  end

  private
  def require_campaign
    @campaign = ::PowerDial::Campaign.find params[:id] || params[:campaign_id]
  end

  def render_campaign
    render json: ::PowerDial::CampaignRepresenter.new(@campaign)
  end

  def permitted_params
    params.require(:campaign).permit(:name, :status, :greeting_audio_data_uri)
  end

  def authorize_power_dial_campaign
    authorize ::PowerDial::Campaign
  end
end

class V1::PriorityDetailsController < ApplicationController
  before_action :require_feature_enabled
  around_action :use_read_replica, only: [:index]

  PRIORITY_DETAIL_COLUMNS = %w(
    original_value
    priority_level_offset
    wait_time_offset
    deltacast_offset
    transfer_from_offset
    prioritized_value
  ).freeze

  def index
    options = params.permit(:sort_column, :sort_direction, :page, :per_page)

    @priority_details = load_priority_details

    @priority_details = apply_sort_options(@priority_details, options.slice(:sort_column, :sort_direction))
    @priority_details = apply_page_options(@priority_details, options.slice(:page, :per_page))

    add_rank(@priority_details, options.slice(:sort_column, :sort_direction))

    add_pagination(@priority_details)

    render json: representer_class.for_collection.prepare(@priority_details)
  end

  private

  def require_feature_enabled
    unless CommQueue::PriorityDetail.enabled?
      render json: {message: 'Not Found'}, status: :not_found
    end
  end

  def apply_sort_options(scope, sort_options)
    validate_sort_options(sort_options)

    sort_column = sort_options[:sort_column].presence || "prioritized_value"
    sort_direction = sort_options[:sort_direction].presence || "ASC"

    scope.order(sort_column => sort_direction)
  end

  def validate_sort_options(sort_options)
    validate_sort_column(sort_options[:sort_column])
    validate_sort_direction(sort_options[:sort_direction])
  end

  def validate_sort_column(sort_column)
    return unless sort_column.present?

    unless sortable_columns.include?(sort_column)
      raise ServiceException, "Invalid sort column #{sort_column}"
    end
  end

  def sortable_columns
    PRIORITY_DETAIL_COLUMNS
  end

  def validate_sort_direction(sort_direction)
    return unless sort_direction.present?

    unless %w(ASC DESC).include?(sort_direction.upcase)
      raise ServiceException, "Invalid sort direction #{sort_direction}"
    end
  end

  def apply_page_options(scope, page_options)
    scope.page(page_options[:page]).per(page_options[:per_page])
  end

  def add_rank(scope, sort_options)
    sort_column = sort_options[:sort_column].presence || "prioritized_value"
    sort_direction = sort_options[:sort_direction].presence || "ASC"

    return unless sort_column == "prioritized_value"

    offset = scope.offset_value || 0

    if sort_direction.upcase == "ASC"
      scope.each_with_index do |priority_detail, index|
        priority_detail.rank = offset + index + 1
      end
    else
      total_count = scope.offset(nil).limit(nil).count

      scope.each_with_index do |priority_detail, index|
        priority_detail.rank = total_count - offset - index
      end
    end
  end

  def load_priority_details
    raise NotImplementedError, "#{self.class.name}##load_priority_details is not implemented"
  end

  def representer_class
    raise NotImplementedError, "#{self.class.name}##representer_class is not implemented"
  end
end

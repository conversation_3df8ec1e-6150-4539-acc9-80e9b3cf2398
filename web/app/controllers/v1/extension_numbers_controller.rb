# frozen_string_literal: true

class V1::ExtensionNumbersController < ApplicationController
  before_action :authorize_user

  def create
    extension = ExtensionNumbersService.new.generate

    render json: { extension_number: extension }
  rescue ServiceException => e
    render json: { message: e.to_s }, status: :bad_request
  end

  private

  def authorize_user
    # only the admin is allowed to use API
    authorize @user || User
  end
end

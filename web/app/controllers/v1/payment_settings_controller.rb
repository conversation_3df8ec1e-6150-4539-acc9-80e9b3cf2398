class V1::PaymentSettingsController < ApplicationController
  wrap_parameters include: Menu::PaymentSetting.attribute_names

  before_action :require_payment_setting, only: [:show, :update]
  before_action :authorize_payment_provider
  before_action :find_permitted_params, only: [:find]

  def show
    render_settings
  end

  def create
    menu = Menu.find params.require(:menu_id)
    @setting = menu.payment_settings.create! permitted_params
    @setting.save!
    render_settings

  rescue ActiveRecord::RecordNotUnique
    render json: {message: "Name has already been taken"}, status: :bad_request
  end

  def update
    @setting.attributes = permitted_params
    @setting.save!
    render_settings
  end

  def find
    @setting =
      if @find_params[:menu_id].blank? && @find_params[:lang].blank?
        Purchase::PaymentSettingFinder.find_outbound
      else
        Purchase::PaymentSettingFinder.find_queue(
          menu_id: @find_params[:menu_id],
          lang: @find_params[:lang]
        )
      end

    if @setting
      render_settings
    else
      render json: nil
    end
  end

  private

  def authorize_payment_provider
    authorize @setting || Menu::PaymentSetting
  end

  def require_payment_setting
    @setting = Menu::PaymentSetting.current.find(params[:id])
  end

  def find_permitted_params
    @find_params ||= params.permit(:menu_id, :lang)
  end

  def permitted_params
    @permitted_params ||= params.permit(:id, :menu_id, :payment_provider_id, :enabled, :zip_code_enabled, :lang, :default_currency)
  end

  def render_settings
    render json: Menu::PaymentSettingRepresenter.new(@setting)
  end
end

# frozen_string_literal: true
class V1::UserStatusesController < ApplicationController
  before_action :authorize_user_status
  before_action :require_user_status, only: [:show, :update, :destroy, :lists_with_assignments]

  def show
    render_user_status
  end

  def index
    render json: statuses_to_json(UserStatus.all_statuses)
  end

  def selectable
    render json: statuses_to_json(UserStatus.selectable_statuses)
  end

  def create
    @user_status = UserStatusService.create(permitted_params)
    render_user_status
  end

  def update
    @user_status = UserStatusService.update(permitted_params)
    render_user_status
  end

  def destroy
    render json: UserStatusRepresenter.new(UserStatusService.destroy(params.require(:name)))
  end

  def available_colors
    render json: UserStatus.colors.keys
  end

  # /v1/user_statuses/:name/lists_with_assignments
  def lists_with_assignments
    render json: UserStatusService.get_user_status_lists_with_assignment_names(@user_status.id)
  end

  private

  def render_user_status
    user_status_lists = UserStatusListService.get_lists_by_status(@user_status.id)
    render json: UserStatusRepresenter.new(@user_status).to_json(user_options: { user_status_lists: })
  end

  def statuses_to_json(statuses)
    UserStatusRepresenter.for_collection.prepare(statuses).to_json(
      user_options: {
        settings: UserStatusSetting.settings_by_user_status_id(statuses.map(&:id)),
        custom_role_associations: UserStatusRoleAssociation.associations_by_user_status_id(statuses.map(&:id))
      }
    )
  end

  def permitted_params
    params.require(:user_status).permit(:id,
                                        :name,
                                        :color,
                                        setting: [:all_roles_restricted, :restricted, :breakthrough_enabled],
                                        custom_roles: [],
                                        user_status_lists_ids: [],
                                        translations: [:lang, :translated_name])
  end

  def require_user_status
    @user_status = UserStatus.get_by_name(params[:name])
    render json: { error: 'User Status not found' }, status: :not_found unless @user_status
  end

  def authorize_user_status
    authorize UserStatus
  end
end

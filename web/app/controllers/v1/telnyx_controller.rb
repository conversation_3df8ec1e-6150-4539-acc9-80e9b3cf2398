# frozen_string_literal: true

module V1
  class TelnyxController < ApplicationController
    include UseSipHeaders

    skip_before_action :authenticate
    before_action :set_tracker_id
    before_action :request_validation, except: [:play_hold, :loop_audio]
    before_action :extract_sip_phone_number
    before_action :set_sip_headers, only: [:voice, :ivr_intro, :ivr_intro_sip]

    # optional protocol since telnyx currently does not send one (but we can at least prepare if they do)
    SIP_REGEX = /^(?:sips?:)?(.*?)@.*$/

    def voice
      texml = voice_callback_service.voice
      if texml.blank?
        head :ok
        return
      end
      render xml: texml.to_xml
    end

    def ivr_intro
      texml = voice_callback_service.ivr_intro

      render xml: texml.to_xml
    end

    def ivr_intro_sip
      ivr_intro
    end

    def ivr_callback
      texml = voice_callback_service.ivr_callback

      render xml: texml.to_xml
    end

    def ivr_waiting
      texml = voice_callback_service.ivr_waiting

      render xml: texml.to_xml
    end

    def ivr_csat
      texml = voice_callback_service.ivr_csat

      render xml: texml.to_xml
    end

    def ivr_deflected_to_queue_callback
      texml = voice_callback_service.ivr_deflected_to_queue_callback

      render xml: texml.to_xml
    end

    def ivr_transfer_to_non_leaf_queue_callback
      texml = voice_callback_service.ivr_transfer_to_non_leaf_queue_callback

      render xml: texml.to_xml
    end

    def callback_number_request
      texml = voice_callback_service.callback_number_request

      render xml: texml.to_xml
    end

    def callback_number_confirm
      texml = voice_callback_service.callback_number_confirm

      render xml: texml.to_xml
    end

    def csat_voicememo_recording_callback
      voice_callback_service.csat_voicememo_recording_callback

      head :ok
    end

    def ivr_wait_time_sms
      texml = voice_callback_service.ivr_wait_time_sms

      render xml: texml.to_xml
    end

    def queue_lang
      texml = voice_callback_service.queue_lang

      render xml: texml.to_xml
    end

    def extension_directory_callback
      step = params[:step]&.to_s

      if step.blank? # initial search
        texml = voice_callback_service.extension_directory_callback
      elsif step == 'new_search_confirmation'
        texml = voice_callback_service.new_search_confirmation_callback
      elsif step == 'agent_connect_confirmation'
        texml = voice_callback_service.agent_connect_confirmation_callback
      end

      render xml: texml.to_xml
    end

    def over_capacity_deflection
      texml = voice_callback_service.over_capacity_deflection

      render xml: texml.to_xml
    end

    def after_hours_deflection
      texml = voice_callback_service.after_hours_deflection

      render xml: texml.to_xml
    end

    def graceful_outage
      texml = voice_callback_service.graceful_outage

      render xml: texml.to_xml
    end

    def status_callback
      voice_callback_service.status_callback
      head :ok
    end

    def status_callback_sip
      status_callback
    end

    def refer_callback
      hangup
    end

    def siprec_status_callback
      voice_callback_service.siprec_status_callback
      head :ok
    end

    def stop_media_streams
      texml = voice_callback_service.stop_media_streams

      render xml: texml.to_xml
    end

    def announcement_deflection
      texml = voice_callback_service.announcement_deflection

      render xml: texml.to_xml
    end

    def presession_deflection
      case params[:step]&.to_s
      when 'start'
        texml = voice_callback_service.presession_deflection_start
      when 'accepted'
        texml = voice_callback_service.presession_deflection_accepted
      when 'finish'
        texml = voice_callback_service.presession_deflection_finish
      when 'fail'
        texml = voice_callback_service.presession_deflection_failure
      else
        raise ServiceException, "Invalid step #{params[:step]}"
      end

      render xml: texml.to_xml
    end

    def hangup
      texml = TelnyxService::TexmlBuilder.new(&:hangup)
      render xml: texml.to_xml
    end

    def conference_join
      texml = voice_callback_service.conference_join
      render xml: texml.to_xml
    end

    def play_hold
      # head request from telnyx to trigger a TexML pipeline for hold music
      return render xml: empty_texml.to_xml if request.head?

      request_validation

      texml = voice_callback_service.holding
      render xml: texml.to_xml
    end

    def conference_callback
      voice_callback_service.conference_callback
      head :ok
    end

    # this is the action for the record verb, meant to sent extra instructions after voicemail recording is finished.
    # we don't do anything after recording voicemails, so we can reply with empty texml
    # final recording information will be sent to voicemail_recording_callback
    def voicemail_callback
      render xml: empty_texml.to_xml
    end

    def voicemail_recording_callback
      voice_callback_service.voicemail_recording_callback
      head :ok
    end

    def virtual_agent_stream_start
      texml = voice_callback_service.start_virtual_agent_stream
      render xml: texml.to_xml
    end

    def virtual_agent_stream_end
      texml = voice_callback_service.virtual_agent_stream_ended
      render xml: texml.to_xml
    end

    def virtual_agent_status_callback
      voice_callback_service.virtual_agent_status_callback
      head :ok
    end

    def agent_assist_status_callback
      voice_callback_service.agent_assist_status_callback
      head :ok
    end

    def virtual_agent_recording_callback
      voice_callback_service.virtual_agent_recording_callback
      head :ok
    end

    def agent_voice_detection_missed
      texml = voice_callback_service.agent_voice_detection_missed
      render xml: texml.to_xml
    end

    def ivr_escalation
      texml = voice_callback_service.ivr_escalation
      render xml: texml.to_xml
    end

    def ivr_escalation_failure
      texml = voice_callback_service.ivr_escalation_failed
      render xml: texml.to_xml
    end

    def ivr_recording_option
      texml = voice_callback_service.ivr_recording_option
      render xml: texml.to_xml
    end

    def recording_callback
      voice_callback_service.recording_callback

      head :ok
    end

    def ivr_payment
      texml = voice_callback_service.payment
      render xml: texml.to_xml
    end

    def async_amd_callback
      texml = voice_callback_service.async_amd_callback
      if texml.blank?
        head :ok
        return
      end
      render xml: texml.to_xml
    end

    def dnc
      texml = voice_callback_service.dnc
      render xml: texml.to_xml
    end

    def mobile_agent_voice
      texml = voice_callback_service.mobile_agent_voice
      render xml: texml.to_xml
    end

    def ucaas_agent_voice
      texml = voice_callback_service.ucaas_agent_voice
      render xml: texml.to_xml
    end

    def announce_and_hangup
      texml = voice_callback_service.announce_and_hangup
      render xml: texml.to_xml
    end

    def loop_audio
      # head request from telnyx to trigger a TexML pipeline for hold music
      return render xml: empty_texml.to_xml if request.head?

      request_validation

      texml = voice_callback_service.loop_audio
      render xml: texml.to_xml
    end

    def task_virtual_agent_start
      texml = voice_callback_service.start_task_virtual_agent
      render xml: texml.to_xml
    end

    def task_virtual_agent_finish
      texml = voice_callback_service.finish_task_virtual_agent
      render xml: texml.to_xml
    end

    def internal_call_after_hours_deflection
      texml = voice_callback_service.internal_call_after_hours_deflection
      render xml: texml.to_xml
    end

    def internal_call_after_hours_deflection_callback
      texml = voice_callback_service.internal_call_after_hours_deflection_callback
      render xml: texml.to_xml
    end

    def internal_call_over_capacity_deflection
      texml = voice_callback_service.internal_call_over_capacity_deflection
      render xml: texml.to_xml
    end

    def internal_call_over_capacity_deflection_callback
      texml = voice_callback_service.internal_call_over_capacity_deflection_callback
      render xml: texml.to_xml
    end

    def internal_call_automatic_redirection
      texml = voice_callback_service.internal_call_automatic_redirection
      render xml: texml.to_xml
    end

    def internal_call_voicemail_recorded
      texml = voice_callback_service.internal_call_voicemail_recorded
      render xml: texml.to_xml
    end

    def internal_announcement_deflection
      texml = voice_callback_service.internal_announcement_deflection
      render xml: texml.to_xml
    end

    private

    def empty_texml
      TelnyxService::TexmlBuilder.new
    end

    def extract_sip_phone_number
      [:To, :From].each do |key|
        next unless params[key].present?

        result = params[key].match(SIP_REGEX)
        next unless result.present?

        phone_number = Phonelib.parse(result[1])
        next unless phone_number.possible?

        params["Original_#{key}"] = params[key]

        params[key] = phone_number.e164
      end
    end

    def set_tracker_id
      if params[:call_id].present?
        RequestStore.store[:tracker_id] = "call_#{params[:call_id]}"
      elsif params[:chat_id].present?
        RequestStore.store[:tracker_id] = "chat_#{params[:chat_id]}"
      end
    end

    def should_verify_authorized
      false
    end

    def request_validation
      public_key = Company.current.telnyx_settings.public_key
      validator = TelnyxService::SignatureValidator.new(public_key)
      telnyx_signature = request.headers['telnyx-signature-ed25519']
      telnyx_timestamp = request.headers['telnyx-timestamp']

      raise ServiceException, 'Missing Telnyx validation headers' if telnyx_signature.blank? || telnyx_timestamp.blank?

      return if validator.verify(request.raw_post, telnyx_signature, telnyx_timestamp)

      raise ServiceException, 'Telnyx request validation failure'
    end

    def voice_callback_service
      @voice_callback_service ||= TelnyxService::VoiceCallback.new(params)
    end
  end
end

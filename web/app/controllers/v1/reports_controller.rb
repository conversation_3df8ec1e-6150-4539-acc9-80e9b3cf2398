class V1::ReportsController < ApplicationController
  def create
    parameters = report_params.to_h.deep_symbolize_keys
    parameters[:filters] ||= {}.with_indifferent_access
    # current_user_id will bre removed after release 3.4
    parameters[:current_user_id] = @current_user.id
    parameters[:requested_user_id] = @current_user.id
    parameters[:filters][:all_agents] = 'true' if parameters[:report_type] == 'comm_type' && parameters[:filters][:all_agents].nil?

    authorize(Report.new(parameters: parameters), :create?)

    if more_process_allowed?
      @job = ReportService.create_report_async(parameters)
      head :accepted, location: v1_job_url(@job)
    else
      head :conflict
    end
  end

  def show
    @report = Report.find(params[:id])

    authorize(@report, :show?)

    file = @report.file # ReportUploader

    if file.exists?
      send_data file.read, filename: file.filename, type: file.content_type
    else
      # generated report deleted from the cloud storage
      render json: { message: 'Gone' }, status: :gone
    end
  rescue Aws::S3::Errors::NoSuch<PERSON><PERSON>, Aws::S3::Errors::NotFound
    render json: { message: 'Gone' }, status: :gone
    # TODO: rescue GCS not found errors
  end

  private

  def report_params
    params.permit(
      :channel, :report_type, :time_zone, :campaign_mode,
      time_frame: [:from, :to], data_columns: [], include_files: [],
      filters: [:all_menus, :all_agents, :all_campaigns, { menus: [], agents: [], teams: [], campaigns: [], call_types: [], chat_types: [] }]
    ).tap do |permitted|
      permitted.require(:channel)
      permitted.require(:report_type)
      permitted.require(:time_frame).tap do |time_frame|
        time_frame.require(:from)
        time_frame.require(:to)
      end
    end
  end

  def more_process_allowed?
    limit = FeatureFlag.get("limit-report-process")
    on_going_status_ids = Job.statuses.slice(:pending, :in_progress).values
    current_process_count = Jobs::CreateReport.where("created_at > ?", Time.now - 1.day)
                                              .where(status: on_going_status_ids).count
    current_process_count < limit
  end
end

module Internal
  class SmartActionSessionsController < BaseController
    def update
      smart_action_session = SmartActionSession.find(params[:id])
      omnichannel_session = smart_action_session.session

      update_params = params.require(:smart_action_session).permit(:out_ticket_id, :out_ticket_url)
      out_ticket_id = update_params[:out_ticket_id]
      out_ticket_url = update_params[:out_ticket_url]

      if out_ticket_id.present?
        SmartActionSession.transaction do
          smart_action_session.update_out_ticket_id(out_ticket_id, out_ticket_url)
          smart_action_session.logs.create(text: "Update out_ticket_id #{out_ticket_id}")
        end

        omnichannel_session.update_out_ticket_id(out_ticket_id, out_ticket_url)
      end

      render json: {}
    end
  end
end

module Internal
  class CcaiInsightController < BaseController
    def settings
      render json: CcaiInsightRepresenter.new(Company.current.ccai_insight_settings)
    end

    def ingest
      comm_type, comm_id, gcs_uri = params.permit(:comm_type, :comm_id, :gcs_uri)
                                          .require([:comm_type, :comm_id, :gcs_uri])
      Ccai::CcaiService.ingest_conversation(comm_type, comm_id, gcs_uri)
    end

    def update_ccai_upload_status
      comm_type, comm_id, gcs_uri = params.permit(:comm_type, :comm_id, :gcs_uri)
                                          .require([:comm_type, :comm_id, :gcs_uri])
      Ccai::CcaiService.update_ccai_upload_status(comm_type, comm_id, gcs_uri)
    end
  end
end

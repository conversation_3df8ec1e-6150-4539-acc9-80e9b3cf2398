module Internal
  class VideosController < BaseController
    before_action :find_video

    def show
      url = @video.original_download_url || @video.media_url
      response = {url: url}
      basic_auth_token = TwilioService::Download.basic_auth_token if /api\.twilio\.com/i.match(url)
      response[:basic_auth_token] = basic_auth_token if basic_auth_token.present?
      render json: response
    end

    def update
      video_params = params.require(:video).permit(:url)
      @video.uploaded_to_crm!(video_params.fetch(:url))
      comm = @video.comm

      SessionDataFeed.update_save_videos(comm:, video: @video, url: params.require(:video).permit(:url)[:url])

      # TODO: remove sms_history_log_id later since it exists only for backward compatibility (see CRM-858)
      messaging_history_log_id =  params.require(:video).fetch(:messaging_history_log_id, nil) ||
        params.require(:video).fetch(:sms_history_log_id, nil)

      if messaging_history_log_id.present?
        Messaging::ChatService.inbound_media_upload_callback(comm:, messaging_history_log_id:, media: @video)
      end

      # If the param is_temp exists, do not delete media files.
      # If comm_type is a Chat, do not delete media files.
      DeleteMediaWorker.perform_async(:delete_video!, @video.id) if params.require(:video)[:is_temp].blank? && @video.comm_type != Chat.name

      render json: {}
    end

    private

    def find_video
      @video = Video.find(params[:id])
    end
  end
end

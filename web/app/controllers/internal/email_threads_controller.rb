# frozen_string_literal: true

module Internal
  class EmailThreadsController < BaseController

    def latest_message_id
      message_id = EmailAdapter::EmailThreadService.new.get_latest_message_id(email_account_id: params[:email_account_id],
                                                                              mailbox_name: params[:mailbox_name])
      render json: { message_id: message_id }
    end
  end
end

module Internal
  class CrmCommandsController < BaseController
    def update
      status = params.dig(:command, :status)
      if status.present?
        command = CrmCommand.find(params[:id])

        if status == "finished"
          finish_command(command, params.require(:command))
        elsif status == "delayed"
          command.delayed!
        elsif status == "queued"
          Rails.logger.warn("[CrmCommandsController] Setting the status as queued is not allowed")
          command.queued!
        elsif status == "retry"
          executor_id = params.dig(:command, :executor_id)
          command.executor_id = executor_id unless executor_id.blank? || !command.executor_id.blank?
          delay = params.dig(:command, :delay) || 0
          command.retry! delay.to_i
        elsif status == "running"
          command.running!
        elsif status == "waiting"
          if command.find_or_create_ticket? and command.end_user.present?
            command.with_lock do
              if command.contact_id == command.end_user.out_contact_id
                # Already existing EndUser (contact_id) contacted again but
                # there is something wrong in command.contact_id (Invalid Contact error thrown)
                # waiting until CRM adatper/server find or create a new contact and update contact_id
                command.waiting!
              else
                # We assume that CRM adapter/server ran find_or_create_contact and updated CRM command's contact_id
                # We will retry run find_or_create_ticket command again.
                if command.retriable?
                  command.retry! 5
                else
                  command.finished!(result: :failed, fail_reason: "Wait failure")
                end
              end
            end
          end
        end
      end

      render json: {}
    end


    private

    def finish_command(command, params_command)
      params_command_permitted = params_command.permit(:result, :fail_reason)
      return if command.finished? && params_command_permitted[:result]&.to_sym != :success

      ext = params_command[:ext]
      ext = ext.to_unsafe_h if ext.respond_to?(:to_unsafe_h)

      command.with_lock do
        command.finished!(result: params_command_permitted[:result],
                          fail_reason: params_command_permitted[:fail_reason],
                          ext: ext)
      end

      command.publish_finished(ext: ext)
    end
  end
end

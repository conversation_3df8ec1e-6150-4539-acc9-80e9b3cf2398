module Internal
  class AudiosController < BaseController
    before_action :find_audio

    def show
      url = @audio.original_download_url || @audio.media_url
      response = {url: url}
      basic_auth_token = TwilioService::Download.basic_auth_token if /api\.twilio\.com/i.match(url)
      response[:basic_auth_token] = basic_auth_token if basic_auth_token.present?
      render json: response
    end

    def update
      audio_params = params.require(:audio).permit(:url)
      @audio.uploaded_to_crm!(audio_params.fetch(:url))
      comm = @audio.comm
      messaging_history_log_id = params.require(:audio).fetch(:messaging_history_log_id, nil)
      if messaging_history_log_id.present?
        Messaging::ChatService.inbound_media_upload_callback(comm:, messaging_history_log_id:, media: @audio)
      end

      # If the param is_temp exists, do not delete media files.
      # If comm_type is a Chat, do not delete media files.
      DeleteMediaWorker.perform_async(:delete_audio!, @audio.id) if params.require(:audio)[:is_temp].blank? && @audio.comm_type != Chat.name

      render json: {}
    end

    private

    def find_audio
      @audio = Audio.find(params[:id])
    end
  end
end

module Internal
  class EmailAccountsController < BaseController
    def index
      accounts = assigned_email_accounts
      render json: accounts
    end

    def show
      email_account = EmailAccount.select(:id,
                                          :incoming_server_host,
                                          :incoming_server_port,
                                          :email_id, :password,
                                          :email_service,
                                          :imap_sync_info,
                                          :oauth_linked
                                        )
                                  .find(params[:id])
      email_account[:password] = Ujet.encryption_service.decrypt(email_account[:password])[:password]
      render json: email_account
    end

    def update
      email_account = EmailAccount.find(params[:id])
      email_account.update!(
        email_id: permitted_params[:email_id],
        oauth_linked: permitted_params[:oauth_linked]
      )
      email_account[:password] = Ujet.encryption_service.decrypt(email_account[:password])[:password]

      render json: { success: true, email_account: email_account }
    end

    def auth_token_retrieve
      email_account_auth_token = EmailAccountAuthToken.select(:token_data).find(params[:id])

      render json: { success: true, token_data: JSON.parse(email_account_auth_token.token_data) }
    end

    def auth_token_update
      email_account_auth_token = EmailAccountAuthToken.find_or_initialize_by(email_account_id: params[:id])
      email_account_auth_token.token_data = params.require(:token_data).to_json
      email_account_auth_token.save!

      render json: { success: true, token_data: JSON.parse(email_account_auth_token.token_data) }
    rescue StandardError => e
      Rails.logger.error("Email Account Auth Token: Error updating auth token #{e}, #{e.backtrace}")
      render json: { message: 'Internal Server Error' }, status: :internal_server_error
    end

    private

    def assigned_email_accounts
      return [] unless Company.current.email_settings.enabled
      return [] unless Company.current.use_email

      email_account_ids = ::Menu::EmailChannelSetting.where(enabled: true).where.not(email_account_id: nil).pluck(:email_account_id)
      email_accounts = EmailAccount.valid
                  .where(id: email_account_ids)
                  .select(:id, :incoming_server_host, :incoming_server_port, :email_id, :password, :email_service, :imap_sync_info, :oauth_linked)

      email_accounts.filter_map do |account|
        account[:password] = Ujet.encryption_service.decrypt(account[:password])[:password]
        Rails.logger.info("Email Account ID: #{account.id} was successfully decrypted")
        account
      rescue StandardError => e
        Rails.logger.warn("Email Account ID: #{account.id} cannot be decrypted: #{e}, #{e.backtrace}")
        nil
      end
    end

    def permitted_params
      params.require(:email_account).permit(
        :email_id,
        :oauth_linked
      )
    end
  end
end

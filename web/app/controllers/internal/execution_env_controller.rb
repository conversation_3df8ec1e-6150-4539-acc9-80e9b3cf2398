require 'socket'
module Internal
  # Controller that has to do with anything to do with the execution environment
  # Note: This controller is not authenticating anything like most of the internal calls
  # we want this controller to be able to be hit from any endpoint
  class ExecutionEnvController < ApplicationController
    skip_before_action :authenticate

    def ping
      start_time = Time.now.to_f
      @@host_name ||= Socket.gethostname
      end_time = Time.now.to_f
      render json: {start_time: start_time, host_name: @@host_name, end_time: end_time }
    end
  end
end

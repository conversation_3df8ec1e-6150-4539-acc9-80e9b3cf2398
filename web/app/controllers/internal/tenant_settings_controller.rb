# frozen_string_literal: true

module Internal
  class TenantSettingsController < BaseController
    before_action :require_no_tenant

    # GET /internal/tenant_settings
    def index
      @settings = TenantSettings.all(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/nice
    def nice
      @settings = TenantSettings.nice(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/alvaria
    def alvaria
      @settings = TenantSettings.alvaria(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/email_channel
    def email_channel
      @settings = TenantSettings.email_channel(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/external_session_event
    def external_session_event
      @settings = TenantSettings.external_session_event(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/verint
    def verint
      @settings = TenantSettings.verint(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/looker_exporter
    def looker_exporter
      @settings = TenantSettings.looker_exporter(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/data_exporter
    # Deprecated: use data_export
    def data_exporter
      @settings = TenantSettings.data_exporter(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/data_export
    def data_export
      @settings = TenantSettings.data_export(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/clearview_exporter
    def clearview_exporter
      @settings = TenantSettings.clearview_exporter(permitted_params.to_h)
      render json: @settings
    end

    def wfm
      @settings = TenantSettings.wfm(permitted_params.to_h)
      render json: @settings
    end

    # GET /internal/tenant_settings/customer_logs
    def customer_logs
      @settings = TenantSettings.customer_logs(permitted_params.to_h)
      render json: @settings
    end

    private

    def require_no_tenant
      return if request.env['ujet.tenant'].blank?

      Rails.logger.warn('internal tenant settings requested with specific tenant')
      render json: { message: 'Not Found' }, status: :not_found
    end

    def permitted_params
      params.permit(:enabled)
    end
  end
end

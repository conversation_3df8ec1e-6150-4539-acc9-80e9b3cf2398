module Internal
  class ChatsController < BaseController
    before_action :convert_id

    attr_reader :call, :chat

    def show
      service = Internal::CommRepresenterService.new(Chat.find(params[:id]))
      render json: service.comm_with_notes_codes_and_metadata
    end

    def update
      chat = Chat.find(params[:id])
      session = chat.session

      chat_params = params.require(:chat).permit(
        :out_ticket_id, :out_ticket_url, :is_out_ticket_account,
        :chat_log_id, :out_contact_id, :out_contact_type,
        :need_assignment, :is_new, :identifier
      )
      out_ticket_id = chat_params[:out_ticket_id]
      out_ticket_url = chat_params[:out_ticket_url]
      is_out_ticket_account = chat_params[:is_out_ticket_account]
      out_chat_log_id = chat_params[:chat_log_id]
      out_contact_id = chat_params[:out_contact_id]
      out_contact_type = chat_params[:out_contact_type]
      need_assignment = chat_params[:need_assignment]
      is_new = chat_params[:is_new]
      identifier = chat_params[:identifier]

      chat.update_is_out_ticket_account(is_out_ticket_account) if is_out_ticket_account.present?

      if out_ticket_id.present?
        chat.update_out_ticket_id(out_ticket_id, out_ticket_url)
        session.update_out_ticket_id(out_ticket_id, out_ticket_url)
      elsif out_ticket_url.present? && (Company.current.crm_type == :ujet || Company.current.crm_type == :custom)
        chat.update_out_ticket_id(nil, out_ticket_url)
        session.update_out_ticket_id(nil, out_ticket_url)
      end

      if out_contact_id.present?
        ChatService::Base.update_end_user(
          chat:,
          contact_id: out_contact_id,
          need_assignment:,
          is_new_contact: is_new,
          out_contact_type:,
          identifier:
        )
      end

      chat.update_out_log_id(out_chat_log_id) if out_chat_log_id.present?

      render json: {}
    end

    def history
      chat_history_service = chat_history
      chat_history_service.set_use_unredacted(use_unredacted: params.dig(:use_unredacted) || false)
      raw_history = chat_history_service.message_history(end_user_name: params.dig(:end_user_name) || nil).join("\n")
      history = CustomizableLinkService.reformat_custom_links(content: raw_history).strip
      headers['Content-Disposition'] = "attachment; filename=\"#{filename(format: 'txt')}\""
      render plain: history
    end

    def history_ccai
      history = chat_history.message_history_ccai_format(end_user_name: params.dig(:end_user_name) || nil)
      headers['Content-Disposition'] = "attachment; filename=\"#{filename(format: 'json')}\""
      render json: history.compact
    end

    def history_json
      chat_history_service = chat_history
      chat_history_service.set_use_unredacted(use_unredacted: params.dig(:use_unredacted) || false)
      history = chat_history_service.message_history_json_format(end_user_name: params.dig(:end_user_name) || nil)
      headers['Content-Disposition'] = "attachment; filename=\"#{filename(format: 'json')}\""
      render json: history.compact
    end

    def history_dump
      messages = chat_history.message_dump

      render json: { messages: }
    end

    def display_history_settings
      render json: { display_history: Company.current.chat_settings.display_history }
    end

    def realtime_redaction_settings
      render json: { realtime_redaction: nil } and return unless chat.present?

      finder = RealtimeRedactionSetting::Finder.new(menu_id: chat.menu_id, lang: chat.lang)
      realtime_redaction = finder.find_realtime_redaction_setting&.dig(:setting)
      data = { realtime_redaction: }
      data[:chat_realtime_redaction_setting] = ChatRealtimeRedactionSettingRepresenter.new(@chat.chat_realtime_redaction_setting) if @chat&.chat_realtime_redaction_setting.present?
      render json: data
    end

    private

    def convert_id
      # check to see if it's a Blended SMS chat transcript
      if params[:blended_sms].present?
        @call = Call.includes(participants: [:user]).find(params[:id])
      else
        @chat = Chat.includes(participants: [:user]).find(params[:id])
      end
    end

    def chat_history
      # check to see if it's a Blended SMS chat transcript
      if call.present?
        # Blended SMS
        ChatService::ChatHistory.new(call:)
      else
        ChatService::ChatHistory.new(chat:)
      end
    end

    def filename(format: 'txt')
      call.present? ? "call-sms-#{call.id}.#{format}" : "chat-#{chat.id}.#{format}"
    end
  end
end

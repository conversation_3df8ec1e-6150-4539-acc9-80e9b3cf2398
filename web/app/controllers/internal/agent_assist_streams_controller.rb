# frozen_string_literal: true

# TODO: remove after SKY-2180 deployed (see Internal::AgentAssist::StreamsController)
module Internal
  class AgentAssistStreamsController < BaseController
    before_action :require_call
    before_action :require_participant
    before_action :require_agent_assist_profile

    attr_reader :participant, :profile, :call

    # POST /internal/calls/:id/participants/:id/agent_assist/stream
    def stream
      render json: service.stream
    end

    # POST /internal/calls/:id/participants/:id/agent_assist/stream/analyzed_content
    def analyzed_content
      service.save_analyzed_content
      head :ok
    end

    private

    def service
      @service ||= ::AgentAssist::StreamService.new(participant:, profile:, params:)
    end

    def require_call
      @call = Call.find(params[:call_id])
    end

    def require_participant
      @participant = call.participants.find(params[:id])
    end

    def require_agent_assist_profile
      @profile = ::AgentAssist::ConversationService.get_current_assigned_profile(comm: call)
      head :bad_request unless @profile.present?
    end
  end
end

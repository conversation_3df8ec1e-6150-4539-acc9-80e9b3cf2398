module Internal
  module Wfm
    class ChatsController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)
        chats = ChatsService.query_chats(args)

        render json: Internal::Wfm::ChatRepresenter.for_collection.new(chats)
      end

      private

      def sort_column
        'updated_at'
      end

      def permitted_params
        params.permit(:per, :per_page, updated_at: [:from])
      end
    end
  end
end

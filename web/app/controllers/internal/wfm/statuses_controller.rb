# frozen_string_literal: true

module Internal
  module Wfm
    class StatusesController < Internal::BaseController
      # GET /internal/wfm/status
      def show
        render json: current_company.wfm_settings
      end

      # POST /internal/wfm/status
      def create
        update
      end

      # PATCH|PUT /internal/wfm/status
      def update
        settings_params = params.permit(:activation_status, :activation_status_description)
        return render(json: {}, status: :bad_request) if settings_params.empty?

        feature_params = build_feature_params(settings_params)

        safe_company_update { |company| company.wfm_settings.update(feature_params) }

        render json: current_company.wfm_settings, status: :ok
      end

      private

      def build_feature_params(settings_params)
        {
          enabled: current_company.wfm_settings.enabled && settings_params[:activation_status] != 'Removed',
          deployed: true,
          status: case settings_params[:activation_status]
                  when 'Success'
                    WfmStatus::ACTIVATED
                  when 'Removed'
                    WfmStatus::REMOVED
                  else
                    WfmStatus::ACTIVATE_FAILED
                  end,
          log: settings_params[:activation_status_description]
        }
      end
    end
  end
end

module Internal
  module Wfm
    class RequestDataController < Internal::BaseController
      around_action :use_reports_replica

      include PaginationHeader

      MAX_PER_PAGE = 10_000
      DEFAULT_PER = 5_000.to_s

      private

      def default_query_args
        {
          sort_column:,
          sort_direction:,
          max_per_page:,
          per:,
          page: 1
        }.with_indifferent_access
      end

      def sort_column
        raise NotImplementedError, "#{self.class.name}###{__method__} is not implemented"
      end

      def per
        DEFAULT_PER
      end

      def sort_direction
        'ASC'
      end

      def max_per_page
        MAX_PER_PAGE
      end
    end
  end
end

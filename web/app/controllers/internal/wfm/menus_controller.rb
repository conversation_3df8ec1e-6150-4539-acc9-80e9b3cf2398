module Internal
  module Wfm
    class MenusController < Wfm::RequestDataController
      def index
        scope = ::Menu::Base.with_deleted
                            .in_menu_type(permitted_params.delete(:menu_type))
                            .includes(:direct_access_keys, :assignments)

        args = default_query_args.merge(permitted_params.to_h)

        menus = SimpleQuery.call(
          scoped: scope,
          args:
        )
        add_pagination(menus)

        inject_menu_settings(menus)
        inject_voice_call_settings(menus)
        inject_chat_settings(menus)

        Rails.customer_logger.info "[Internal-WFM] Menu data is synced by WFM."

        render json: ::Internal::Wfm::MenuWithAssignmentsRepresenter.for_collection.new(menus)
      end

      private

      def sort_column
        'updated_at'
      end

      def permitted_params
        @permitted_params ||= params.permit(:per, :page, updated_at: [:from], id: [])
      end

      # inject menu settings including deleted ones. this is required to represent deleted menus correctly.
      def inject_menu_settings(menus)
        inject_menu_associations_including_deleted(menus, :settings, ::Menu::Setting)
      end

      # inject voice call settings including deleted ones. this is required to represent deleted menus correctly.
      def inject_voice_call_settings(menus)
        inject_menu_associations_including_deleted(menus, :voice_call_settings, ::Menu::VoiceCallSetting)
      end

      # inject chat settings including deleted ones. this is required to represent deleted menus correctly.
      # chat settings are for mobile and web menus only.
      def inject_chat_settings(menus)
        inject_menu_associations_including_deleted(menus, :chat_settings, ::Menu::ChatSetting)
      end

      def inject_menu_associations_including_deleted(menus, assoc_name, assoc_class)
        menu_ids = menus.ids
        return unless menu_ids.present?

        assoc_records = assoc_class.with_deleted.current.where(menu_id: menus.ids)
        assoc_records = assoc_records.group_by(&:menu_id)

        menus.each do |menu|
          next unless menu.respond_to?(assoc_name)

          menu.association(assoc_name).target = assoc_records[menu.id] || []
        end
      end
    end
  end
end

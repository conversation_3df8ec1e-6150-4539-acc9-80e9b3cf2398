module Internal
  module Wfm
    class CallsController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)
        calls = CallsService.query_calls(args)

        render json: Internal::Wfm::CallRepresenter.for_collection.new(calls)
      end

      private

      def sort_column
        'updated_at'
      end

      def permitted_params
        params.permit(:per, :per_page, updated_at: [:from])
      end
    end
  end
end

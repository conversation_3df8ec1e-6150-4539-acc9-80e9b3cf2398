module Internal
  module Wfm
    class ChatInteractionsController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)
        args[:ended_at] = args.delete(:updated_at) if args[:updated_at].present?
        chats = ChatsService.query_handle_interactions(args)

        Rails.customer_logger.info "[Internal-WFM] Chat data is synced by WFM."

        render json: Internal::Wfm::PerfChatHandleDurationRepresenter.for_collection.new(chats)
      end

      private

      def sort_column
        'ended_at'
      end

      def permitted_params
        params.permit(:per, :per_page, updated_at: [:from])
      end
    end
  end
end

# frozen_string_literal: true

module Internal
  module Wfm
    class EmailsController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)
        email_supports = EmailSupportsService.query_email_supports(args)

        Rails.customer_logger.info "[Internal-WFM] Email Support data is synced by WFM."

        render json: ::Internal::Wfm::EmailSupportRepresenter.for_collection.new(email_supports)
      end

      private

      def sort_column
        'updated_at'
      end

      def permitted_params
        params.permit(:per, :per_page, updated_at: [:from])
      end
    end
  end
end

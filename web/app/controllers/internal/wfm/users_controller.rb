module Internal
  module Wfm
    class UsersController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)

        agents = SimpleQuery.call(
          scoped: User.with_deleted.includes(:teams, assignments: :menu),
          args:
        )
        add_pagination(agents)

        Rails.customer_logger.info "[Internal-WFM] User data is synced by WFM."

        render json: ::Internal::Wfm::UserRepresenter.for_collection.new(agents).to_json(extended_roles: params['extended_roles'] == 'true')
      end

      private

      def sort_column
        'updated_at'
      end

      def permitted_params
        params.permit(:per, :page, updated_at: [:from], id: [])
      end
    end
  end
end

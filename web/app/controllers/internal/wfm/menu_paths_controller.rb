module Internal
  module Wfm
    class MenuPathsController < Wfm::RequestDataController
      skip_around_action :use_reports_replica, only: [:index_with_ids]

      def index
        materialized_path = ::Menu::Base.with_deleted.active_menu_paths
        @menu_paths = ::Menu::Path.where(materialized_path:).includes(:menu_settings, menus: [:assignments, :settings])

        render_menu_paths
      end

      def index_with_ids
        @menu_paths = ::Wfm::DataProcessingService.process_menu_paths(menu_ids: permitted_params[:id])

        render_menu_paths
      end

      private

      def render_menu_paths
        return render json: [] if @menu_paths.blank?

        results = []

        enabled_langs = CompanyLanguage.where(company_id: Company.current.id, enabled: true).pluck(:lang)

        @menu_paths.each do |menu_path|
          leaf_menu = menu_path.menus.last
          leaf_menu.settings.each do |menu_setting|
            lang = menu_setting.lang
            results << {
              key: "#{menu_path.id}-#{lang}",
              id: menu_path.id,
              materialized_path: menu_path.materialized_path,
              name: menu_path.materialized_path_name(lang:),
              lang:,
              type: leaf_menu.type,
              agent_assignments: agents_by_channel_type(leaf_menu, lang),
              team_assignments: teams_by_channel_type(leaf_menu, lang),
              deleted: leaf_menu.deleted_at.present?,
              enabled: menu_setting.enabled && enabled_langs.include?(lang)
            }
          end
        end

        Rails.customer_logger.info "[Internal-WFM] Menu paths data is synced by WFM."

        render json: results
      end

      def agents_by_channel_type(menu, lang)
        ::Menu.supporting_channel_types(menu.menu_type).each_with_object({}) do |channel_type, hash|
          hash[channel_type] = assignments(menu&.assignments, lang, 'User', channel_type)
        end
      end

      def teams_by_channel_type(menu, lang)
        ::Menu.supporting_channel_types(menu.menu_type).each_with_object({}) do |channel_type, hash|
          hash[channel_type] = assignments(menu&.assignments, lang, 'Team', channel_type)
        end
      end

      def assignments(assignments, lang, assignee_type, channel_type)
        list = assignments&.select do |a|
          a.lang == lang && a.assignee_type == assignee_type && a.channel_type.to_sym == channel_type.to_sym
        end
        list&.pluck(:assignee_id)&.uniq
      end

      def permitted_params
        @permitted_params ||= params.permit(id: [])
      end
    end
  end
end

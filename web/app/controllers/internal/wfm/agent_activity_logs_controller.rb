# frozen_string_literal: true

module Internal
  module Wfm
    class AgentActivityLogsController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)
        args[:started_at] = args.delete(:updated_at) if args[:updated_at].present?

        # FXW-2101 User activity log is required due to performance issue until FXW-1884
        scope = UserActivityLog.includes(:whodunnit)
        scope = scope.where.not(activity: UserActivityLog.activities[:deltacast_call_bounced])

        logs = SimpleQuery.call(
          scoped: scope,
          args:
        )

        Rails.customer_logger.info "[Internal-WFM] Agent activity logs data is synced by WFM."

        render json: Internal::Wfm::AgentActivityLogRepresenter.for_collection.new(logs)
      end

      private

      def sort_column
        'started_at'
      end

      def permitted_params
        params.permit(:per, :per_page, updated_at: [:from])
      end
    end
  end
end

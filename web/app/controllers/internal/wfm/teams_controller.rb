module Internal
  module Wfm
    class TeamsController < Wfm::RequestDataController
      def index
        args = default_query_args.merge(permitted_params.to_h)

        teams = SimpleQuery.call(
          scoped: ::Team.with_deleted,
          args:
        )
        add_pagination(teams)

        Rails.customer_logger.info "[Internal-WFM] Team data is synced by WFM."

        render json: Internal::Wfm::TeamRepresenter.for_collection.new(teams)
      end

      private

      def sort_column
        'updated_at'
      end

      def permitted_params
        params.permit(:per, :page, updated_at: [:from], id: [])
      end
    end
  end
end

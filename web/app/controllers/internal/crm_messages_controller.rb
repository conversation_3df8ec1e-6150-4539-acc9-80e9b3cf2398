module Internal
  class CrmMessagesController < BaseController
    # Receive notification from CRM server
    # Route to Firebase so appropriate agent is notified
    def create
      # extract payload
      message_type = params[:message_type]
      message = params[:message]
      user_id = params[:user_id]
      timeout = params[:timeout]
      timestamp = params[:timestamp]

      # setup data to send to firebase
      message_hex = SecureRandom.hex
      payload = {
        id: message_hex,
        messageType: message_type,
        message: message,
        timeout: timeout,
        timestamp: timestamp
      }

      firebase_realtime_db_path = "notifications/users/#{user_id}/#{message_hex}"
      Firebase::RealtimeDb::DefaultWorker.perform_async(:update, firebase_realtime_db_path, payload)

      fb_key = "users/#{user_id}/crm_messages/#{message_hex}"
      Firebase::CloudFirestore::DefaultWorker.perform_async(:update, fb_key, payload)

      render json: {}
    end
  end
end

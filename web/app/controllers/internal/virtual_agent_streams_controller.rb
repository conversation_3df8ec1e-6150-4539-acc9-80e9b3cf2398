module Internal
  class VirtualAgentStreamsController < BaseController
    before_action :require_call
    before_action :repair_nested_params

    # POST /internal/calls/:id/virtual_agent_stream/stream
    def stream
      render json: streams_service.stream
    end

    # POST /internal/calls/:id/virtual_agent_stream/stream/payload
    def payload
      streams_service.payload
      head :ok
    end

    # POST /internal/calls/:id/virtual_agent_stream/stream/speech
    def speech
      streams_service.speech
      head :ok
    end

    # POST /internal/calls/:id/virtual_agent_stream/stream/dtmf
    def dtmf
      streams_service.dtmf
      head :ok
    end

    # POST /internal/calls/:id/virtual_agent_stream/stream/finish
    def finish
      streams_service.finish
      head :ok
    end

    private

    def streams_service
      @streams_service ||= Chatbot::VirtualAgentStreamService.new(call: @call, params: params)
    end

    def require_call
      @call = Call.find(params[:id])
    end

    # deal with a rails bug which turns nested array into a numerically-keyed hash
    # ie payload.messages param looks like \"messages\"=>{\"0\"=>\"Hello! How can I help you?\"}
    def repair_nested_params(obj = params)
      return unless obj.is_a? Hash

      obj.each do |key, value|
        next unless value.is_a? Hash

        # If any non-integer keys
        if value.keys.find { |k, _| k =~ /\D/ }
          repair_nested_params(value)
        else
          obj[key] = value.values
          value.values.each { |h| repair_nested_params(h) }
        end
      end
    end
  end
end

module Internal
  class CrmServerProxyController < BaseController
    def forward_call_to_crm_server(method, version, api_endpoint, user_id)
      resp = CRM::CrmServer.request(url: api_endpoint,
                                    version:,
                                    method:,
                                    params:,
                                    user_id:,
                                    request_id:,
                                    service_id:)
      render :json => resp
    rescue RestClient::ExceptionWithResponse => e
      render :json => e.response, :status => get_error_status(e)
    end

    def find_one_attachment_info
      user_id = params[:user_id]
      api_url = params[:api_url]
      photo_id = params[:photo_id]
      video_id = params[:video_id]
      media_id = params[:media_id]
      media_type = params[:media_type]
      recording_id = params[:recording_id]

      # Check if the user has participated in the communication.
      service = Internal::CrmServerProxyAuthService.new(user_id:, api_url:, photo_id:, video_id:, recording_id:, media_id:, media_type:)
      has_permission = service.participated_in_api_url

      unless has_permission
        # Check if the user has a custom permission.
        authorize :crm_server_proxy, :find_one_attachment_info_for_call? if service.comm_type == 'Call'
        authorize :crm_server_proxy, :find_one_attachment_info_for_chat? if service.comm_type == 'Chat'
        has_permission = true
      end
      raise ::UnauthorizedException, 'Not allowed to download the resource' unless has_permission

      self.forward_call_to_crm_server('get', 'v1', '/find_one_attachment_info', user_id)
    end

    def pundit_user
      User.find(params[:user_id])
    end

    private

    ################
    # Helpers
    ################
    def request_id
      timestamp = Time.now.utc.strftime('%Y%m%d%H%M%S')
      random_string = SecureRandom.hex(8)
      "#{timestamp}_#{random_string}"
    end

    def service_id
      'internalcrmserver'
    end

    def get_error_status(error)
      if error.try('response').try('code')
        error.response.code
      else
        JSON.parse(error.response)['status'] || 520
      end
    rescue
      return 520
    end
  end
end

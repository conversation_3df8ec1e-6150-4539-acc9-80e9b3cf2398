module Internal
  class CrmCredentialsController < BaseController
    def update_admin_token
      # token data varies by CRM, revisit at a later time explicitly specifying permitted params
      token_data = params.require(:token_data).to_unsafe_h
      CRM::AdminCredential.new(token_data).save
      render json: nil
    rescue StandardError => e
      Rails.logger.error("CRM Credentials: Error updating admin token #{e}, #{e.backtrace}")
      render json: { message: 'Internal Server Error' }, status: :internal_server_error
    end

    def update_auth_token
      # token data varies by CRM, revisit at a later time explicitly specifying permitted params
      token_data = params.require(:token_data).to_unsafe_h
      CRM::Credential.new(token_data).save
      render json: nil
    rescue StandardError => e
      Rails.logger.error("CRM Credentials: Error updating auth token #{e}, #{e.backtrace}")
      render json: { message: 'Internal Server Error' }, status: :internal_server_error
    end
  end
end

module Internal
  class AspectController < BaseController
    include UseDatabaseReplica

    around_action :use_read_replica

    ## Used by Aspect Agent Report
    def agent_report
      render json: Aspect::AgentStats.new(from: params[:from], to: params[:to])
                        .get_stats.values
    end

    ## Used by Aspect Team Report
    def team_report
      render json: Aspect::QueueStats.new(from: params[:from], to: params[:to])
                        .get_stats.values
    end

    ## Used by Aspect RTA Worker
    def user_repr_statuses
      ## to avoid N+1, we need to provide `status_provider` to UserStatusConverter
      ## and `status_provider` need to respond to `get` method
      status_provider = UserStatus.all_statuses.index_by(&:id)
      status_provider.instance_eval do
        def get(id)
          self[id]
        end
      end

      result = User.with_agent_permission.map do |user|
        {
          id: user.id,
          repr_status: user.repr_status(status_provider: status_provider).as_json(methods: :wfm_id)
        }
      end

      render json: result
    end

  end
end

module Internal
  class PhotosController < BaseController
    before_action :find_photo

    def show
      url = @photo.original_download_url || @photo.media_url
      response = { url: }
      basic_auth_token = TwilioService::Download.basic_auth_token if /api\.twilio\.com/i.match(url)
      response[:basic_auth_token] = basic_auth_token if basic_auth_token.present?
      render json: response
    end

    def update
      photo_params = params.require(:photo).permit(:url)
      @photo.uploaded_to_crm!(photo_params.fetch(:url))
      comm = @photo.comm
      SessionDataFeed.update_save_photos(comm:, photo: @photo, url: params.require(:photo).permit(:url)[:url])

      # TODO: remove sms_history_log_id later since it exists only for backward compatibility (see CRM-858)
      messaging_history_log_id = params.require(:photo).fetch(:messaging_history_log_id, nil) ||
                                 params.require(:photo).fetch(:sms_history_log_id, nil)

      if messaging_history_log_id.present?
        Messaging::ChatService.inbound_media_upload_callback(comm:, messaging_history_log_id:, media: @photo)
      end

      # If the param is_temp exists, do not delete media files.
      # If comm_type is a Chat, do not delete media files.
      if params.require(:photo)[:is_temp].blank? && @photo.comm_type != Chat.name
        DeleteMediaWorker.perform_async(:delete_photo!,
                                        @photo.id)
      end

      render json: {}
    end

    private

    def find_photo
      @photo = Photo.find(params[:id])
    end
  end
end

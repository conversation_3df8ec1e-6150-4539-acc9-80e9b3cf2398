# frozen_string_literal: true

module Internal
  module AgentAssist
    class StreamsController < BaseController
      before_action :require_stream
      before_action :require_profile

      # GET /internal/agent_assist/streams/:id
      def show
        render json: service.stream
      end

      # POST /internal/agent_assist/streams/:id/analyzed_content
      def analyzed_content
        service.save_analyzed_content
        head :ok
      end

      private

      attr_reader :profile

      def service
        @service ||= ::AgentAssist::StreamService.new(participant:, profile:, params:)
      end

      def participant
        @stream.call_participant
      end

      def require_stream
        @stream = CallStream.find_by(id: params[:id], stream_type: :agent_assist)
        head :bad_request unless @stream.present?
      end

      def require_profile
        @profile = ::AgentAssist::ConversationService.get_assigned_profile(
          comm: @stream.call,
          profile_id: @stream.agent_assist_profile_id
        )
        head :bad_request unless @profile.present?
      end
    end
  end
end

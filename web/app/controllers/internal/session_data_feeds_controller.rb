# frozen_string_literal: true

module Internal
  class SessionDataFeedsController < BaseController
    before_action :require_communication

    MAX_SESSION_DATA_FEEDS = 1000

    def index
      session_data_feeds = @comm.session_data_feeds
        .order('created_at ASC').limit(MAX_SESSION_DATA_FEEDS).map { |s| representer_class.new(s) }

      render json: session_data_feeds
    end

    def chat_history_result
      result = params.require(:chat_history_result)
        .permit(:used_external_storage, :used_crm, :primary_download_url, :external_download_url, :crm_download_url)

      SessionDataFeed.add_chat_history_result(comm: @comm, params: result)

      head :ok
    end

    def ccai_insight_chat_result
      result = params.require(:ccai_insight_chat_result).permit(:ccai_insight_gcs_uri)

      SessionDataFeed.add_ccai_insight_chat_result(comm: @comm, params: result)

      head :ok
    end

    def cobrowse_history_result
      result = params.require(:cobrowse_history_result)
        .permit(:used_external_storage, :used_crm, :primary_download_url, :external_download_url, :crm_download_url)

      SessionDataFeed.add_cobrowse_history_result(comm: @comm, params: result)

      head :ok
    end

    def call_recording_result
      # note that urls are used instead of url.
      result = params.require(:call_recording_result)
        .permit(:recording_id, :used_external_storage, :used_crm, primary_download_urls: [], external_download_urls: [], crm_download_urls: [])

      SessionDataFeed.add_call_recording_result(comm: @comm, result:)

      head :ok
    end

    def ccai_insight_recording_result
      result = params.require(:ccai_insight_recording_result).permit(:ccai_insight_gcs_uri)

      SessionDataFeed.add_ccai_insight_recording_result(comm: @comm, params: result)

      head :ok
    end

    def text_attachment_result
      result = params.require(:text_attachment_result)
        .permit(:external_download_url, :crm_download_url, :text_source)

      SessionDataFeed.add_text_attachment_result(comm: @comm, params: result)

      head :ok
    end

    def survey_result
      result = params.require(:survey_result)
        .permit(:external_download_url, :crm_download_url)

      SessionDataFeed.add_survey_result(comm: @comm, survey_result: result)

      head :ok
    end

    def session_summary_result
      result = params.require(:session_summary_result).permit(:external_download_url)

      SessionDataFeed.add_session_summary_result(comm: @comm, params: result)

      head :ok
    end

    private

    def require_communication
      if params[:call_id]
        @comm = Call.find(params[:call_id])
      elsif params[:chat_id]
        @comm = Chat.find(params[:chat_id])
      end
    end

    def representer_class
      ::AgentApi::SessionDataFeedRepresenter
    end
  end
end

# frozen_string_literal: true

module Internal
  class CallsController < BaseController

    def show
      call = Call.find(params[:id])
      Stat::CallSmsData.inject_call_sms_data(calls: [call]) if FeatureFlag.on?('sms-smart-action')

      service = Internal::CommRepresenterService.new(call)
      render json: service.comm_with_notes_codes_and_metadata
    end

    def update
      call = Call.find(params[:id])
      session = call.session

      call_params = params.require(:call).permit(
        :out_ticket_id,
        :out_ticket_url,
        :out_event_id,
        :out_contact_id,
        :out_contact_type,
        :is_out_ticket_account,
        :call_log_id,
        :recording_url,
        :insights_recording_uri,
        :need_assignment,
        :recording_id,
        :is_new,
        :stream_sid,
        :identifier
      )

      out_ticket_id = call_params[:out_ticket_id]
      out_ticket_url = call_params[:out_ticket_url]
      out_event_id = call_params[:out_event_id]
      out_contact_id = call_params[:out_contact_id]
      out_contact_type = call_params[:out_contact_type]
      out_call_log_id = call_params[:call_log_id]
      insights_recording_uri = call_params[:insights_recording_uri]
      recording_url = call_params[:recording_url]
      recording_id = call_params[:recording_id]
      stream_sid = call_params[:stream_sid]
      is_out_ticket_account = call_params[:is_out_ticket_account]
      identifier = call_params[:identifier]

      if is_out_ticket_account.present?
        call.logs.create(text: "Update is_out_ticket_account #{is_out_ticket_account}")
        call.update_is_out_ticket_account(is_out_ticket_account)
      end

      if out_call_log_id.present?
        call.logs.create(text: "Update out_log_id #{out_call_log_id}")
        call.update_out_log_id(out_call_log_id)
      end

      if out_ticket_id.present?
        call.logs.create(text: "Update out_ticket_id #{out_ticket_id}")
        call.update_out_ticket_id(out_ticket_id, out_ticket_url)
        session.update_out_ticket_id(out_ticket_id, out_ticket_url)
      elsif out_ticket_url.present? && (Company.current.crm_type == :ujet || Company.current.crm_type == :custom)
        call.logs.create(text: "Update out_ticket_url #{out_ticket_url}")
        call.update_out_ticket_id(nil, out_ticket_url)
        session.update_out_ticket_id(nil, out_ticket_url)
      end

      if out_event_id.present?
        call.logs.create(text: "Update out_event_id #{out_event_id}")
        call.update_out_event_id(out_event_id)
        session.update_out_event_id(out_event_id)
      end

      if recording_url.present?
        recording = Call::Recording.find(recording_id)
        recording.url = recording_url
        recording.save
      end

      if insights_recording_uri.present?
        recording = Call::Recording.find(recording_id)
        recording.insights_uri = insights_recording_uri
        recording.save
        ::Ccai::CcaiService.update_ccai_upload_status('call', call.id, insights_recording_uri)
        ::Ccai::CcaiService.add_ccai_insight_recording_result(call, insights_recording_uri)
      end


      if out_contact_id.present?
        CallService::Base.update_end_user(
          call: call,
          contact_id: out_contact_id,
          need_assignment: call_params[:need_assignment],
          out_contact_type: out_contact_type,
          is_new_contact: ActiveRecord::Type::Boolean.new.cast(call_params[:is_new]),
          identifier:
        )
      end

      if out_contact_id.nil? && out_contact_type == EndUser.out_contact_types[:not_found_skipped]
        CRM::CrmCommandHelper.queue_up_find_or_create_ticket(comm: call)
      end

      if stream_sid.present?
        call.logs.create(text: "Update stream_sid #{stream_sid}")
        call.stream_sid = stream_sid
        call.save!
      end

      render json: {}
    end

    def recording
      recording = Call::Recording.find(params[:recording_id])
      redaction_times = []
      recording.redaction_times.each do |times|
        new_times = {}
        new_times[:start] = (times[:start] - recording.started_at).round(2)
        new_times[:end] = times[:end].present? ? (times[:end] - recording.started_at).round(2) : recording.duration
        redaction_times << new_times
      end

      call = Call.find(params[:id])
      voip_provider = CallService::VoipProviderFactory.create(call: call)
      data = {
        file_name: recording.file_name,
        url: voip_provider.recording_url(recording: recording),
        voip_provider: call.voip_provider.split('_').last,
        redaction_times: redaction_times,
        segments: recording.segments
      }

      if call.voip_provider_nexmo?
        data[:jwt_token] = NexmoService::ApplicationClient.current(call.region).generate_token_for_user(:files, :media)
      elsif call.voip_provider_twilio?
        data[:basic_auth_token] = TwilioService::Download.basic_auth_token(call.region)
      end

      Rails.logger.info(tracker_id: call.tracker_id) {"[Call::Recording] call_id: #{call.id}, segments #{data[:segments]}"}

      render json: data
    end

    def sip_headers
      # Get Inbound SIP data for call
      call_id = params[:id]
      call = Call.find(call_id)

      sip_headers = CustomCrm::TempStore.sip_headers(comm: call)
      sip_headers = nil unless sip_headers.present? # CustomCrm::TempStore.sip_headers returns {} if nothing was stored

      sip_uri = CustomCrm::TempStore.sip_uri(comm: call)

      data = OpenStruct.new(call_id: call_id, sip_uri: sip_uri, sip_headers: sip_headers)

      render json: Internal::SipHeadersRepresenter.new(data).to_hash
    end

    def update_ticket_status
      call_id = params.require(:id)
      update_data = {}

      if params[:crm_error].present?
        update_data[:crm_error] = true
      else
        update_data[:ticket_id] = params[:ticket_id]
        update_data[:is_closed] = params[:is_closed]
      end

      CallService::ScheduledCallService.set_scheduled_ticket_cache(call_id:, **update_data)
      head :ok
    end
  end
end

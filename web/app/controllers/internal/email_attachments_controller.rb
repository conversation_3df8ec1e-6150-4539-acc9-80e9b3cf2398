module Internal
  class EmailAttachmentsController < BaseController
    before_action :validate_create_parameters, only: :create
    before_action :validate_update_parameters, only: :update

    def create
      email_attachment = EmailAttachment.create!(
        comm_id: params[:email_support_id],
        session_id: params[:session_id],
        url: params[:url],
        file_type: params[:file_type]
      )
      render json: email_attachment.id
    end

    def update
      email_attachment = EmailAttachment.find(params[:id])
      email_attachment.update!(url: params[:url])
      render json: {}
    end

    private

    def validate_create_parameters
      params.require([:email_support_id, :session_id, :file_type])
    end

    def validate_update_parameters
      params.require([:url])
    end
  end
end

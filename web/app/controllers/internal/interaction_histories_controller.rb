module Internal
  class InteractionHistoriesController < BaseController
    before_action :find_communication, only: :session_summary

    def session_summary
      crm_note_data_service = ::AgentAssist::Summary::CRMNoteDataService.new(participant: @comm.agent_participants.first)
      render json: crm_note_data_service.session_summary_json(@comm)
    end

    private

    def find_communication
      if params[:call_id].present?
        @comm = Call.find(params[:call_id])
      else
        @comm = Chat.find(params[:chat_id])
      end
    end
  end
end

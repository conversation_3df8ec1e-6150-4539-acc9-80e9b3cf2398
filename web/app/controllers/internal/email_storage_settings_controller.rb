# frozen_string_literal: true

module Internal
  class EmailStorageSettingsController < BaseController
    def show
      email_settings =
        SettingsService::EmailStorageSettingsService.new.decrypt_storage_credentials(
          Company.current.email_settings
        )

      render json: {
        storage_provider: email_settings.storage_provider,
        email_storages: email_settings.email_storages
      }
    end
  end
end

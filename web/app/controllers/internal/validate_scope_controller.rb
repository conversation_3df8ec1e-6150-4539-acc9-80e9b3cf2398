# frozen_string_literal: true

module Internal
  class ValidateScopeController < BaseController
    def transcript
      auth_result = ::AuthToken.authenticate(token: params[:token])
      @current_user = auth_result.user

      comm_id = params.require(:comm_id)
      chat = Chat.find_by_id(comm_id)

      return render json: { error: 'Chat not found' }, status: :not_found unless chat.present?

      authorize(chat, policy_class: ::TranscriptPolicy)

      render json: {}, status: 200
    end
  end
end
module Internal
  module Menu
    class OperationHoursController < BaseController

      def show
        operation_hour = ::Menu::OperationHour.find(params[:id])

        render json: {
          id: operation_hour.try(:id),
          sun_start_time: operation_hour.try(:sun_start_time),
          sun_end_time: operation_hour.try(:sun_end_time),
          mon_start_time: operation_hour.try(:mon_start_time),
          mon_end_time: operation_hour.try(:mon_end_time),
          tue_start_time: operation_hour.try(:tue_start_time),
          tue_end_time: operation_hour.try(:tue_end_time),
          wed_start_time: operation_hour.try(:wed_start_time),
          wed_end_time: operation_hour.try(:wed_end_time),
          thu_start_time: operation_hour.try(:thu_start_time),
          thu_end_time: operation_hour.try(:thu_end_time),
          fri_start_time: operation_hour.try(:fri_start_time),
          fri_end_time: operation_hour.try(:fri_end_time),
          sat_start_time: operation_hour.try(:sat_start_time),
          sat_end_time: operation_hour.try(:sat_end_time)
        }
      end
    end
  end
end

module Internal
  class DocumentsController < BaseController
    before_action :find_document

    def show
      url = @document.original_download_url || @document.media_url
      response = {url: url}
      basic_auth_token = TwilioService::Download.basic_auth_token if /api\.twilio\.com/i.match(url)
      response[:basic_auth_token] = basic_auth_token if basic_auth_token.present?
      render json: response
    end

    def update
      document_params = params.require(:document).permit(:url)
      @document.uploaded_to_crm!(document_params.fetch(:url))
      comm = @document.comm
      messaging_history_log_id = params.require(:document).fetch(:messaging_history_log_id, nil)
      if messaging_history_log_id.present?
        Messaging::ChatService.inbound_media_upload_callback(comm:, messaging_history_log_id:, media: @document)
      end

      # If the param is_temp exists, do not delete media files.
      # If comm_type is a Chat, do not delete media files.
      DeleteMediaWorker.perform_async(:delete_document!, @document.id) if params.require(:document)[:is_temp].blank? && @document.comm_type != Chat.name

      render json: {}
    end

    private

    def find_document
      @document = Document.find(params[:id])
    end
  end
end

module Internal
  class CrmSettingsController < BaseController
    def show
      render json: Company.current.crm_settings
    end

    def media_service
      render json: Company.current.crm_settings.media_service
    end

    def save_external_storage_google
      current_company.external_storage_settings.external_storage_enabled = true
      current_company.external_storage_settings.external_storage_selection = 'google'
      current_company.save
      render json: nil
    end

    def update_google_storage_token
      current_company.external_storage_settings.external_storage_google_token = params[:token]
      current_company.save
      render json: nil
    end
  end
end

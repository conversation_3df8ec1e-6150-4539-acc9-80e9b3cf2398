module Internal
  class UserPresencesController < BaseController
    before_action :require_user
    before_action :require_enabled

    # PUT /internal/user_presences/:user_id
    def update
      online = params.require(:online)
      if online
        UserService.update_online(user: @user, online: true, time: Time.current)
      else
        # don't mark them offline immediately, wait to see if client reconnects to a different server soon
        time = [UserPresence::PresenceTimeoutService.timeout_seconds, 60].min.seconds.since
        UserPresence::UserOnlineStatusUpdateWorker.perform_at(time, @user.id)
      end

      head :ok
    end

    private

    def require_user
      @user = User.select(:id, :online, :onlined_at, :offlined_at).find(params[:user_id])
    end

    def require_enabled
      head(:ok) unless ::PresenceService.enabled?
    end
  end
end

# frozen_string_literal: true

module Internal
  class EmailSupportsController < BaseController
    before_action :validate_parameters, only: :create

    def create
      email_support_params = params.permit(:email_account_id,
                                           :message_id,
                                           :email_uid,
                                           :sender_name,
                                           :sender_email,
                                           :subject,
                                           :in_reply_to,
                                           :mailbox_name,
                                           :uid_validity,
                                           :attachments_count)
      EmailAdapter::EmailSupportService.new.create(email_support_params)
      render json: {}
    end

    def show
      email_support = EmailSupport.find(params[:id])
      service = Internal::CommRepresenterService.new(email_support)
      render json: service.comm_with_notes_codes_and_metadata
    end

    def update
      email_support = EmailSupport.find(params[:id])
      email_support_params = params.require(:email_support)
                                   .permit(:out_ticket_id, :out_ticket_url, :email_support_log_id)
      EmailAdapter::EmailSupportService.new.update(email_support, email_support_params)
      render json: {}
    end

    private

    def validate_parameters
      params.require([:email_account_id, :message_id, :email_uid, :sender_email, :mailbox_name, :uid_validity])
    end
  end
end

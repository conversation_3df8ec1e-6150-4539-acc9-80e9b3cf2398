module Internal
  class UsersController < BaseController

    def index
      users = User.all

      # this looks like it's only used in the mediaServiceErrorReporter, which has been adjusted.
      # users = users.with_roles(*params[:roles]) if params[:roles].present?
      users = users.with_any_license(:admin) if ActiveRecord::Type::Boolean.new.deserialize(params[:admin])

      properties = params[:properties].presence || [:id, :email, :name]

      render json: {
        users: Internal::UserRepresenter.for_collection.prepare(users).to_hash(
          user_options: { properties: properties }
        )
      }
    end

    # GET /internal/users/online
    def online_users
      # Presence worker will fix those invalid user status.
      render json: User.online.ids
    end

    # PUT|PATCH /internal/users/:id
    def update
      user = User.find(params[:id])

      # Requests from CRM adaptor
      crm_auth = ActiveRecord::Type::Boolean.new.cast(params.dig(:user, :crm_auth))
      if !crm_auth.nil? && crm_auth == false
        # We do not clear oauth_token for a user in redis.
        # Because there are no credentials in the database anymore but we need credentials to send CRM commands.
        user.invalidate_crm_authentication
      end

      crm_access_token = params.dig(:user, :access_token)
      if crm_access_token
        crm_credential = CRM::Credential.fetch_for_user_id(user_id: user.id)
        crm_credential.access_token = crm_access_token
        crm_credential.save
      end

      crm_user_id = params.dig(:user, :crm_user_id)
      if crm_user_id
        if Company.current.crm_type == :oracle
          user.crm_user_id = crm_user_id
          user.crm_authenticated = true
          user.save
        else
          user.update_attribute(:crm_user_id, crm_user_id)
        end
        # update CRM credential
        crm_credential = CRM::Credential.fetch_for_user_id(user_id: user.id)
        crm_credential.crm_user_id = crm_user_id
        crm_credential.save
      end

      # Requests from presence worker
      handle_requests_from_presence_worker(user: user)

      render json: {online: user.online}
    end

    private

    def handle_requests_from_presence_worker(user:)
      # If user is signed out, online is always false.
      return unless user.logged_in?

      # Requests from presence_worker
      online = ActiveRecord::Type::Boolean.new.cast(params.dig(:user, :online))
      return if online.nil?

      time =  if params.dig(:user, :time).present?
                Time.at(params.dig(:user, :time).to_i / 1000)
              else
                Time.zone.now
              end

      # TODO: We can remove this since not use anymore.
      UserService.update_online(user: user, online: online, time: time)
    end
  end
end

module Internal
  class EndUsersController < BaseController
    def update
      end_user = EndUser.fetch(params[:id])
      if end_user.nil?
        Rails.logger.warn("EndUser not found, id: #{params[:id]}")
        return render json: { result: false, message: "EndUser not found, id: #{params[:id]}" }
      end
      attrs = params.require(:end_user).permit(:out_contact_id, :need_assignment, :out_contact_type)
      attrs[:out_contact_type] = attrs[:out_contact_type].to_i if attrs[:out_contact_type].is_a?(String)
      end_user.update(attrs)

      # Update end user sync for the calls in progress
      end_user.calls.in_progress.each do |call|
        call.sync_update!(:end_user)
      end

      if end_user.out_contact_id_previously_changed?
        crm_commands = end_user.crm_commands
                               .where('status <> ?', CrmCommand.statuses[:finished])
                               .where.not(contact_id: end_user.out_contact_id)

        crm_commands.find_each do |command|
          CRM::CrmCommandHelper.update_contact_id(command:, contact_id: end_user.out_contact_id)
        end

        # Update end_user.crm_commands again not to lose any crm_commands those were created during the above update
        # Especially there were problems for 'find_or_create_ticket' command that was created in 'on_chat_created'
        CrmCommandUpdateContactWorker.perform_at(3.seconds.since, end_user.id)
      end

      # Enqueue `update_ticket_with_contact` crm_commands those are dependent on overwritten contact_id.
      # A worker is needed due to the condition of another crm_command being existent.
      # At this time, another crm_command may exist before it will be deleted after its success.
      # We will add an additional async task just in case that 5 seconds is not enough for readiness.
      call_id = params[:call_id]
      chat_id = params[:chat_id]
      command_name = CrmCommand.names.key(CrmCommand.names[:update_ticket_with_contact])
      commands = ::CRM::CrmCommandHelper.find_waiting_commands_from_comm(name: command_name, call_id:, chat_id:)
      if commands.present?
        CrmCommandDependencyQueueWorker.perform_in(5.seconds, command_name, call_id, chat_id)
        CrmCommandDependencyQueueWorker.perform_in(30.seconds, command_name, call_id, chat_id)
      end

      render json: {}
    end
  end
end

module CsrfProtection
  extend ActiveSupport::Concern

  included do
    protect_from_forgery with: :exception, prepend: true

    # this will be done conditionally on authentication
    skip_before_action :verify_authenticity_token

    # AngularJS XSRF protection support
    before_action :set_authenticity_token
  end

  # AngularJS XSRF protection support
  def verified_request?
    token = request.headers['X-UJET-XSRF-TOKEN'].presence || request.headers['X-XSRF-TOKEN']
    super || valid_authenticity_token?(session, token)
  end

  def set_authenticity_token
    authenticity_token = form_authenticity_token
    headers['XSRF-TOKEN'] = authenticity_token

    # @deprecated
    cookies['XSRF-TOKEN'] = {value: authenticity_token, secure: PRODLIKE_ENVIRONMENTS.include?(Rails.env)}
  end
end

# frozen_string_literal: true

module ValidateExportImportEnabled
  extend SettingVersionHistoryErrorHandling
  extend ActiveSupport::Concern

  included do
    before_action :validate_export_import_enabled
  end

  private

  def validate_export_import_enabled
    return if current_company.export_import_enabled?

    render_error(
      "Config export and import are disabled for #{current_company.display_name} company", :precondition_failed
    )
  end
end

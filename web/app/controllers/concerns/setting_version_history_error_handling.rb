# frozen_string_literal: true

module SettingVersionHistoryErrorHandling
  extend ActiveSupport::Concern

  included do
    rescue_from StandardError do |error|
      Telemetry::Factory.create.add_error(name: error, attributes: { message: error.message })

      Rails.logger.error(error:) { error.message }
      # Display error details in the console only in the development environment
      if Rails.env.development?
        puts '------------------------'
        puts error.message
        puts error.backtrace
        puts '------------------------'
      end

      render_error('Internal Server Error', :internal_server_error)
    end

    rescue_from Settings::DataTransfer::ImportService::ImportConflictError do |error|
      Rails.logger.warn(error:) { error.message }
      render_error(error.message, :conflict)
    end

    rescue_from Settings::DataTransfer::ExportService::ExportError do |error|
      Rails.logger.warn(error:) { error.message }
      render_error('Export Error', :unprocessable_entity)
    end

    rescue_from ActiveRecord::RecordInvalid do |error|
      Rails.logger.warn(error:) { error.message }
      render_error('Record Invalid', :unprocessable_entity)
    end

    rescue_from IOError do |error|
      Rails.logger.error(error:) { error.message }
      render_error('IO error occurred', :internal_server_error)
    end

    rescue_from Errno::EACCES do |error|
      Rails.logger.error(error:) { error.message }
      render_error('Permission denied', :forbidden)
    end

    rescue_from ServiceException do |error|
      Rails.logger.error(error:) { error.message }
      render_error('Request Invalid', :bad_request)
    end

    rescue_from ActiveRecord::RecordNotFound do |error|
      Rails.logger.warn(error:) { error.message }
      render_error('Not Found', :not_found)
    end

    rescue_from ActionController::ParameterMissing do |error|
      Rails.logger.warn(error:) { error.message }
      render_error('Invalid Parameters', :bad_request)
    end

    rescue_from ::UnauthorizedException do |error|
      Rails.logger.warn(error:) { error.message }
      render_error('Unauthorized', :unauthorized)
    end

    rescue_from ::Settings::DataTransfer::ManagingQueueBackupService::RollbackExpired,
      Settings::DataTransfer::BackupTablesService::BackupError, InvalidParameterException do |error|
      Rails.logger.warn(error:) { error.message }
      render_error(error.message, :forbidden)
    end
  end

  private

  def render_error(message, status)
    render json: { status: :failed, data: { errors: message } }, status:
  end
end

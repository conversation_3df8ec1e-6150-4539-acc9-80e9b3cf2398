# frozen_string_literal: true

module GcpServiceAccountAuthentication
  private

  def gcp_authenticated?
    return false unless gcp?

    auth = Authentication::ServiceAccount.create_role_based_gcp_instance(bearer_token)
    auth.authenticated?
  end

  def bearer_credentials?
    request.authorization.present? && (auth_scheme.downcase == 'bearer')
  end

  def basic_credentials?
    request.authorization.present? && (auth_scheme.downcase == 'basic')
  end

  def bearer_token
    request.authorization.to_s.split(' ', 2).second
  end

  def gcp?
    Rails.configuration.env[:cloud_provider] == 'GCP'
  end

  def auth_scheme
    request.authorization.to_s.split(' ', 2).first
  end
end

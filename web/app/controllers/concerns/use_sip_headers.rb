# frozen_string_literal: true

module UseSipHeaders
  extend ActiveSupport::Concern

  def set_sip_headers
    params[:sip_headers] = find_sip_headers
  end

  def find_sip_headers
    hash = {}
    params.each do |key, value|
      key_without_prefix = key.delete_prefix(sip_header_prefix)
      next if key_without_prefix == key # not a sip header

      hash[key_without_prefix] = decode_www_form_component(value)
    end
    hash
  end

  def decode_www_form_component(value)
    return value unless form_urlencoded?

    URI.decode_www_form_component(value || '')
  end

  def form_urlencoded?
    request.content_type == 'application/x-www-form-urlencoded'
  end

  # can be overridden in controller for any carriers that use a different prefix
  def sip_header_prefix
    'SipHeader_'
  end
end

module ErrorHandling
  extend ActiveSupport::Concern

  included do
    # TODO : find the better way in handling errors
    # order has meanings : first declared, last served.
    rescue_from Exception do |error|
      Telemetry::Factory.create.add_error(name: error, attributes: { message: error.message })

      Rails.logger.error(error: error) { error.message }
      if Rails.env.development?
        puts "------------------------"
        puts error.message
        puts error.backtrace
        puts "------------------------"
      end
      render json: { message: 'Internal Server Error' }, status: :internal_server_error
    end

    rescue_from TooManyRequestsException do |error|
      render json: { message: 'Too Many Requests' }, status: :too_many_requests
    end

    rescue_from CompanyLockedError do
      render json: { message: t('errors.company_locked') }, status: :forbidden
    end

    rescue_from Pundit::NotAuthorizedError do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: 'Not Authorized' }, status: :forbidden
    end

    if Rails.env.development?
      rescue_from Pundit::AuthorizationNotPerformedError do |error|
        Rails.logger.warn(error: error) { "Authorization not performed for #{self.class.name}.#{action_name}" }
        self.response_body = nil
        render json: { message: "No pundit authorization performed for #{self.class.name}.#{action_name}; please file a JIRA ticket" },
               status: :not_implemented
      end
    end

    rescue_from ConflictException do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :conflict
    end

    rescue_from ActionView::MissingTemplate do |_error|
      render body: nil, status: :not_found
    end

    rescue_from ServiceException do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :bad_request
    end

    rescue_from InvalidParameterException do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :bad_request
    end

    rescue_from ActionController::ParameterMissing do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :bad_request
    end

    rescue_from ActionController::InvalidAuthenticityToken do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :unauthorized
    end

    rescue_from UnauthorizedException do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :unauthorized
    end

    rescue_from NoPermissionException do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.message }, status: :forbidden
    end

    rescue_from UserPresence::PresenceService::TooManyConcurrentUsers do |_|
      # Force logout current user
      UserService.logout(user: current_user, force_offline: true) if current_user

      render json: { message: t('limits.too_many_concurrent_users') }, status: :forbidden
    end

    # Error by optimistic locking
    rescue_from ActiveRecord::StaleObjectError do |error|
      Telemetry::Factory.create.add_error(name: error, attributes: { message: error.message })

      Rails.logger.error(error: error) do
        "#{error.message}, id: #{error.record.send( error.record.class.primary_key )}, changes: #{error.record.changes}"
      end

      render json: { message: 'Internal Server Error' }, status: :internal_server_error
    end

    rescue_from ActiveRecord::RecordInvalid do |error|
      Rails.logger.warn(error: error) { error.message }
      json = if error.message.include?('MiniMagick')
               {
                 model_name: error.record.model_name.singular,
                 primary_key: error.record.send(error.record.class.primary_key),
                 message: ['Error processing uploaded image'],
                 errors: ['Error processing uploaded image']
               }
             else
               {
                 model_name: error.record.model_name.singular,
                 primary_key: error.record.send(error.record.class.primary_key),
                 errs: error.record.errors.details,
                 message: error.record.errors.full_messages, # stop using `full_messages` use `details`
                 errors: error.record.errors.to_hash, # stop using `to_hash`, use `details`
               }
             end
      render json: json, status: :bad_request
    end

    rescue_from ActiveRecord::RecordNotFound do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: 'Not Found' }, status: :not_found
    end

    rescue_from TenantSelect::InvalidTenantError do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { message: error.to_s }, status: :not_found
    end

    rescue_from Twilio::REST::RestError do |error|
      Rails.logger.error(error: error) { error.message }
      render json: { message: error.message }, status: :bad_request
    end

    rescue_from ArgumentError do |error|
      Rails.logger.error(error: error) { error.message }
      render json: { error: error.message }, status: :bad_request
    end

    rescue_from ExpiredDeliveryException do |error|
      Rails.logger.warn(error:) { error.message }
      render json: { message: error.message }, status: :gone
    end

    rescue_from Messaging::DuplicateMessageError do
      head :ok
    end

    rescue_from DecryptionException do |error|
      Rails.logger.warn(error:) { "decryption failure: #{error.message}" }
      render json: { message: error.message }, status: :failed_dependency
    end

    rescue_from ClientErrorWithCode do |error|
      Rails.logger.warn(error: error) { error.message }
      render json: { error_code: error.error_code, message: error.message}, status: :bad_request
    end
  end
end

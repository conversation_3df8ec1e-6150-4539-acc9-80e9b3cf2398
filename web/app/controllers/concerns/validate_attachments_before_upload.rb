module ValidateAttachmentsBeforeUpload
  extend ActiveSupport::Concern

  ACCEPTED_EXTENSIONS = {
    image: ['.jpeg', '.jpg', '.png', '.gif', '.tiff', '.raw', '.webp'],
    video: ['.mp4', '.mov', '.avi', '.wmv', '.webm'],
    audio: ['.mp3', '.wav']
  }.freeze

  EXCEPTED_EXTENSIONS = ['.exe', '.dll', '.bat', '.zip', '.rar', '.scr', '.bin'].freeze

  def validate_attachments_ext!(attachments:)
    return if attachments.blank?

    extensions = attachments.map { |attachment| File.extname(attachment.original_filename).downcase }
    raise ServiceException, 'File without extension is invalid' if extensions.any?(&:blank?)

    filtered_attachments = attachments.select { |attachment| valid_file?(attachment) }

    raise ServiceException, 'Invalid file extensions' if filtered_attachments.size < attachments.size
  end

  def validate_attachments_size!(attachments:, body_text:, max_size: 50.megabytes)
    total_attachments_size = attachments.present? ? attachments.sum(&:size) : 0

    total_size = total_attachments_size + total_inline_attachments_size(body_text)

    msg = "Total size of attachments cannot exceed #{max_size / 1.megabyte} MB."

    raise ServiceException, msg if total_size > max_size
  end

  private

  def total_inline_attachments_size(body_text)
    return 0 if body_text.blank?

    base64_image_data_array = body_text.scan(%r{src="data:image/\w+;base64,([^"]*)"}).flatten
    total_size = 0
    base64_image_data_array.each do |base64_data|
      decoded_image_data = Base64.decode64(base64_data)
      total_size += decoded_image_data.size
    end

    total_size
  end

  def file_type(file)
    case file.content_type
    when %r{^image/.*}
      'image'
    when %r{^video/.*}
      'video'
    when %r{^audio/.*}
      'audio'
    else
      'unknown'
    end
  end

  def valid_file?(file)
    type = file_type(file)
    extension = File.extname(file.original_filename).downcase
    case type
    when 'image', 'video', 'audio'
      ACCEPTED_EXTENSIONS[type.to_sym].include?(extension)
    else
      !EXCEPTED_EXTENSIONS.include?(extension)
    end
  end
end

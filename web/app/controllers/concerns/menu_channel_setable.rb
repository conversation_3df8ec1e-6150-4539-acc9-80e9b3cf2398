# frozen_string_literal: true

module MenuChannelSetable
  extend ActiveSupport::Concern

  included do
    before_action :require_channel_setting, except: :index
    before_action :authorize_channel_setting
  end

  def index
    lang = params.require(:lang)
    menu_id = params.require(:menu_id)
    @channel_settings = resource_scope.where(lang: lang, menu_id: menu_id)

    before_render_channel_settings(@channel_settings)
    render json: channel_setting_representer.for_collection.prepare(@channel_settings)
  end

  def show
    render_channel_setting
  end

  def update
    @channel_setting.update!(setting_params)
    if params[:group_type].present?
      service = Menu::QueueGroupService.new(channel_setting: @channel_setting)
      service.switch!(group_type: params[:group_type])
    end
    render_channel_setting
  rescue ActiveRecord::RecordNotFound => rnf
    raise rnf unless handle_record_not_found_on_update(rnf)
  end

  protected

  def require_channel_setting
    @channel_setting = resource_scope.find params[:id]
  end

  def resource_scope
    resource_class.without_deleted.current
  end

  def resource_class
    raise NotImplementedError
  end

  def setting_params
    raise NotImplementedError
  end

  def channel_setting_representer
    raise NotImplementedError
  end

  def authorize_channel_setting
    authorize @channel_setting || Menu::ChannelSetting
  end

  def render_channel_setting
    before_render_channel_setting(@channel_setting)
    render json: channel_setting_representer
      .new(@channel_setting).to_hash
      .merge(
        {
          virtual_agent_disabled: @virtual_agent_disabled || false
        }
      ).to_json(
        user_options: {
          include_queue_path: true
        }
      )
  end

  def before_render_channel_settings(channel_settings)
    # override me
  end

  def before_render_channel_setting(channel_setting)
    # override me
  end

  def validate_virtual_agent_assignment
    return unless setting_params.dig(:virtual_agent, :enabled)

    virtual_agent = @channel_setting.virtual_agent_assignment.virtual_agent
    if virtual_agent.present?
      @virtual_agent_disabled = !virtual_agent.enabled
      return
    end

    message = 'Cannot turn on because virtual agent is not assigned.'
    # render in a before action prevents main action from taking place
    render json: { message: message }, status: :bad_request
  end

  def handle_record_not_found_on_update(_)
    false
  end
end

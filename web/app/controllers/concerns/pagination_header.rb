module PaginationHeader
  def add_pagination(collection)
      # build links
      links = (headers['Link'] || "").split(',').map(&:strip)
      url   = request.original_url.sub(/\?.*$/, '')
      pages = {}
      unless collection.first_page?
        pages[:first] = 1
        pages[:prev]  = collection.current_page - 1 if collection.present?
      end

      unless collection.last_page?
        pages[:last] = collection.total_pages
        pages[:next] = collection.current_page + 1 if collection.present?
      end
      pages.each do |k, v|
        new_params = request.query_parameters.merge(:page => v)
        links << %(<#{url}?#{new_params.to_param}>; rel="#{k}")
      end

      # Add pagination info to headers following [RFC-5988](http://tools.ietf.org/html/rfc5988)
      headers['Link'] = links.join(', ') unless links.empty?
      headers["Total"] = collection.total_count.to_s
      headers["Per-Page"] = per_page(collection)
    rescue ActiveRecord::StatementInvalid
      raise ServiceException, "Sql statement is invalid. Possibly caused by invalid query parameters."
  end

  def per_page(collection)
    return Kaminari.config.default_per_page.to_s if params[:per_page].blank?
    [params[:per_page].to_i, collection.klass.max_per_page].min.to_s
  end
end

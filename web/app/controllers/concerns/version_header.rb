# frozen_string_literal: true

module VersionHeader
  extend ActiveSupport::Concern

  included do
    after_action :set_version_header
  end

  private

  def set_version_header
    return if version.nil?

    headers['UJET_VERSION'] = version
  end

  def version
    return @@version if defined?(@@version)

    @@version = nil
    @@version = Rails.application.config.env[:ujet][:version]
    @@version
  end
end

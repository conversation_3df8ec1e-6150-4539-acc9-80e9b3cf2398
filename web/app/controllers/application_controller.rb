class ApplicationController < ActionController::Base

  include Pundit::Authorization
  include ErrorHandling
  include ThreadUrlOptions::UsingRequestUrlOptions
  include PaginationHeader
  include UseDatabaseReplica
  include TimezoneCallback
  include UseRequestId
  include UseReferer
  include CsrfProtection
  include ErrorCode

  before_action :authenticate, except: ["home"]

  before_action :set_paper_trail_whodunnit
  around_action :use_user_company_timezone

  after_action :allow_iframe, only: ["home"]
  after_action :verify_authorized, if: lambda {
    Rails.env.development? && should_verify_authorized && !Rails.application.config.bypass_auth_checks
  }

  if Rails.application.config.use_debug_controller
    @@referer_map = RefererMap.new # rubocop:disable Style/ClassVars
    before_action -> { @@referer_map.record_action(controller: self) }
  end

  def home
    skip_authorization
    if File.exist?(Rails.root.join("public/client/index.html"))
      render file: Rails.root.join("public/client/index.html")
    else
      render body: nil, status: :not_found
    end
  end

  def unhandled_exception(error, message)
    Telemetry::Factory.create.add_error(name: error, attributes: { message: error.message })

    Rails.logger.error(error: error) { error.message }
    render json: { message: message },
           status: :internal_server_error
  end

  protected

  def current_company
    Company.current
  end

  def reset_password_allowed?(user)
    # Resets are allowed if company setting is false
    # or the user has an email ending in ujet.co or ujet.cx
    # or the user is in the default admin role
    current_company.settings.disable_password_reset == false || user.ujet? || user.default_admin_role?
  end

  def should_verify_authorized
    true
  end

  # Authenticates token, it does not matter that the token is
  # from end_user, user, or company
  def authenticate
    raise ::UnauthorizedException, "Auth is not present" unless auth.present?

    # using auth token in cookie requires CSRF protection
    verify_authenticity_token if auth.authorization_cookie.present?

    auth.authenticate(cookie_enabled: true)

    raise ::UnauthorizedException, "Invalid auth token type" unless auth.user.is_a?(User)
  end

  def current_user
    @current_user ||= auth.try(:user)
  end

  def current_user=(user)
    @current_user = user
    RequestStore.store[:user] ||= @current_user
  end

  def current_device
    auth.try(:device)
  end

  def auth
    request.env['ujet.auth']
  end

  # authenticate by the end_user's token issued for agent call's schedule link.
  def authenticate_token
    token = params.require(:auth_token)
    @payload, header = AuthToken.decode(token)
    @agent = User.with_agent_permission.find_by_id @payload["agent_id"]
    RequestStore.store[:agent] = @agent if @agent
    raise UnauthorizedException, "Cannot find agent" if @agent.nil?
  end

  def require_agent
    raise UnauthorizedException, "only the agent can do this operation." unless current_user.try(:agent_permission?)
    @agent = current_user
    RequestStore.store[:agent] = @agent
  end

  def append_info_to_payload(payload)
    super

    RequestStore.store[:call] = @call if @call
    RequestStore.store[:chat] = @chat if @chat
    RequestStore.store[:smart_action_session] = @smart_action_session if @smart_action_session
    case @comm
    when Call
      RequestStore.store[:call] = @comm
    when Chat
      RequestStore.store[:chat] = @comm
    when SmartActionSession
      RequestStore.store[:smart_action_session] = @comm
    end
  end

  def authorize_stat
    Rails.logger.warn("[DEPRECATION WARNING] #{controller_name} uses the default StatPolicy.  Please use context specific authorization instead.") if Rails.env.development? || Rails.env.test?
    authorize :stat, :show?
  end

  def allow_iframe
    response.headers.except! 'X-Frame-Options'
  end

  def authenticate_user(user)
    UserService.login(user: user, remote_ip: remote_ip)

    token = user.generate_auth_token

    self.current_user = user

    current_user.update_signed_request(salesforce_hash_key: params[:hash_key])

    token
  end

  def remote_ip
    IPAddr.new(request.remote_ip).hton
  rescue IPAddr::InvalidAddressError, IPAddr::AddressFamilyError
    nil
  end

  def retry_on_stale_object(max_attempts: 2)
    raise 'Please provide a block' unless block_given?

    attempts = 0
    loop do
      yield
      break
    rescue ActiveRecord::StaleObjectError => e
      attempts += 1
      raise e if attempts >= max_attempts
    end
  end

  def safe_company_update(max_attempts: 2, perform_save: true)
    raise 'Please provide a block' unless block_given?

    retry_on_stale_object(max_attempts: max_attempts) do
      current_company.reload
      yield current_company
      current_company.save! if perform_save
    end
  end

  class << self
    def require_feature(flag_name)
      before_action do
        render json: { message: 'Not Found.' }, status: :not_found unless FeatureFlag.on?(flag_name.to_s)
      end
    end
  end
end

# frozen_string_literal: true

module AgentAssist
  class AgentAssistListener
    #
    # Nexmo specific
    #

    def on_voip_conference_leave(call, _conference_sid, call_sid)
      return unless call.voip_provider_nexmo?
      return unless AgentAssist::ConversationService.get_all_assigned_profiles(comm: call).any?

      participant = CallParticipant.find_by_tw_sid(call_sid)
      return unless participant.present?

      hangup_websockets(participant.call_streams)
    end

    # we're not seeing on_voip_conference_leave from nexmo when the departing agent hangs up
    # but we do see this when they do. this only updates the database since nexmo is telling us
    # it's already closed the streams
    def on_voip_call_finished(participant)
      return unless participant.call.voip_provider_nexmo?

      participant.call_streams.agent_assist.connected.each(&:finished!)
    end

    #
    # Stream events
    #

    def on_call_stream_created(call_stream)
      return unless call_stream.agent_assist?

      AgentAssist::StartStreamWorker.perform_async(call_stream.id)
    end

    #
    # Call events
    #
    def on_call_created(call)
      outgoing = call.is_a?(DirectCall) || call.outbound_number.present?
      return unless outgoing

      update_agent_assist_status(call)
    end

    def on_voip_conference_join(call, _conference_sid, call_sid, participant = nil)
      return unless call.is_a?(IvrCall) || call.is_a?(DirectCall)

      participant = call.participants.detect { |p| p.tw_sid == call_sid }
      return unless participant.present?

      transcribe_participant_joined(participant) if participant.end_user?

      CallStream.created.where(call_participant: participant).each do |call_stream|
        AgentAssist::StartStreamWorker.perform_async(call_stream.id)
      end
    end

    def on_call_status_changed(call, _prev_status, current_status)
      handle_nexmo_status_change(call, current_status) if call.voip_provider_nexmo?

      if current_status == 'queued'
        update_agent_assist_status_for_queued(call)
      elsif call.ended?
        on_comm_ended(call)
      end
    end

    # for agent participants, we can create their agent assist profile when they accept a call
    def on_call_participant_created(participant_id)
      participant = CallParticipant.find_by_id(participant_id)
      return unless participant.present?

      on_comm_participant_connected(participant)
    end

    def on_call_participant_status_changed(pid, prev_status, curr_status)
      return unless curr_status == 'connected' || prev_status == 'connected'

      participant = CallParticipant.find_by_id(pid)
      return unless participant.present?

      if curr_status == 'connected'
        # create the agent assist participant if we haven't already at this point
        on_comm_participant_connected(participant)

        # for ivr calls, the end user is 'connected' when in a menu instead of a conference
        return if participant.end_user? && participant.call.is_a?(IvrCall)

        transcribe_participant_joined(participant)
      elsif prev_status == 'connected'
        transcribe_participant_left(participant)
        summarize_conversation_by_agent(participant)
        if participant.agent_assist_profile_id
          AgentAssist::StopObsoleteStreamsWorker.perform_async(participant.id)
          complete_obsolete_conversations(participant:)
        end
      end
    end

    def on_cold_transfer_started(_transfer, end_user_participant, _in_queue)
      # we typically don't end an end-user's stream until the end of the call, but
      # in the case of a cold transfer, we need to end it to allow a new one to be created
      # in the new queue
      CallStream.agent_assist.not_ended.where(participant_id: end_user_participant.id).each do |call_stream|
        AgentAssist::StopStreamWorker.perform_async(call_stream.id)
        next unless call_stream.agent_assist_profile_id

        AgentAssist::CompleteConversationProfileWorker.perform_async(
          call_stream.call.class.name,
          call_stream.call_id,
          call_stream.agent_assist_profile_id
        )
      end
    end

    #
    # Chat events
    #
    def on_chat_created(chat)
      # messaging chats are not created with a queue, so we don't know what AA profile to use yet
      return if chat.messaging_chat?

      update_agent_assist_status(chat)
    end

    def on_chat_participant_status_changed(participant_id, prev_status, current_status)
      participant = ChatParticipant.find_by_id(participant_id)
      return unless participant.present?

      if prev_status == 'wrapping_up' && current_status == 'finished'
        complete_conversation_after_wrap_up(participant:)
        chat = participant.chat
        if chat.ended? && !chat.has_agents_in_wrap_up?
          AgentAssist::CompleteConversationWorker.perform_async(Chat.name, chat.id)
        end
      elsif current_status == 'connected'
        on_comm_participant_connected(participant)
      elsif prev_status == 'connected'
        summarize_conversation_by_agent(participant)
        complete_obsolete_conversations(participant:) unless participant.chat.dismissed?

        if participant.wrapping_up?
          # create the new conversation for wrap-up
          create_wrap_up_conversation(participant:)
        end
      end
    end

    def on_chat_status_changed(chat, prev_status, current_status)
      if current_status == 'queued' && chat.messaging_chat? # once we actually queue the messaging chat we can add AA
        update_agent_assist_status_for_queued(chat)
      elsif prev_status == 'dismissed' && current_status == 'queued'
        update_agent_assist_status_for_queued(chat)
      elsif chat.ended? && !chat.has_agents_in_wrap_up?
        on_comm_ended(chat)
      end

      return unless chat.any_agent_assist_enabled? && current_status == 'dismissed'

      agent = chat.current_agent_participant || chat.last_agent_participant
      return unless agent

      summarize_conversation_by_agent(agent, for_wrap_up: false)
    end

    #
    # Communication events
    #
    def on_transfer_status_changed(transfer, _prev_status, current_status)
      return unless transfer.chat? || transfer.call?

      on_comm_transferring(transfer) if current_status == 'transferring'
      on_comm_transferred(transfer.comm.reload) if current_status == 'transferred'
    end

    def on_escalation_status_changed(escalation, _prev_status, current_status)
      return unless current_status == 'escalated'

      on_comm_escalated(escalation.comm.reload)
    end

    private

    def update_agent_assist_status_for_queued(comm)
      update_agent_assist_status(comm)
      create_external_comm_participants(comm, 1.second)
    end

    def handle_nexmo_status_change(call, current_status)
      return unless Call::ENDED_STATUSES.include?(Call.statuses[current_status])
      return unless AgentAssist::ConversationService.get_all_assigned_profiles(comm: call).any?

      hangup_websockets(call.streams)
    end

    def on_comm_participant_connected(participant)
      return unless participant.support_agent_assist?

      comm = participant.try(:call) || participant.try(:chat)
      return unless comm&.any_agent_assist_enabled?

      return unless AgentAssist::AgentAssistStore.external_participant_missing?(comm:, participant:)

      AgentAssist::CreateParticipantWorker.perform_async(participant.class.name, participant.id)
    end

    def on_comm_ended(comm)
      AgentAssist::CompleteConversationWorker.perform_async(comm.class.name, comm.id)

      return unless comm.is_a?(Call)

      transcribe_call_ended(comm) if comm.realtime_transcripts_enabled?

      # ensure we upload any previous messages even if we end on a queue that does not have transcripts enabled
      AgentAssist::TranscriptService.new(call: comm).upload_to_crm
    end

    def on_comm_transferring(transfer)
      transcribe_transfer_started(transfer) if transfer.comm.is_a?(Call)
    end

    def transcribe_call_ended(comm)
      return unless comm.disconnected_by_end_user? # agent disconnect transcription is handled in the progress service

      ended_by = EndUserService.contact_name(comm.end_user)
      message = AgentAssist::TranscriptMessage.call_ended(ended_by:)
      AgentAssist::SaveTranscriptWorker.perform_async(comm.id, message.to_h)
    end

    def transcribe_transfer_started(transfer)
      return unless transfer.comm.realtime_transcripts_enabled?

      from_agent = transfer.from_agent
      message = AgentAssist::TranscriptMessage.transfer_started(
        target: transfer.transfer_target,
        agent: {
          id: from_agent.id,
          email: from_agent.email,
          name: from_agent.message_name
        }
      )
      AgentAssist::SaveTranscriptWorker.perform_async(transfer.comm.id, message.to_h)
    end

    def on_comm_transferred(comm)
      return if comm.va_assigned? || comm.va_connected?

      enabled = update_agent_assist_status(comm)
      return unless enabled

      create_external_comm_participants(comm, 1.second)
    end

    def on_comm_escalated(comm)
      enabled = update_agent_assist_status(comm)
      return unless enabled

      create_external_comm_participants(comm, 1.second)
    end

    def update_agent_assist_status(comm)
      assignable = AgentAssist::Availability.agent_assist_enabled?(comm:)
      existing_profile_id = AgentAssist::AgentAssistStore.get_current_assigned_profile_id(comm:)
      profile_id = comm.agent_assist_profile&.id || 0
      return false if profile_id.zero?

      # If the profile is different, we need to create a new conversation for the new profile
      profile_changed = assignable && existing_profile_id != 0 && existing_profile_id != profile_id
      enabled = profile_changed || assignable

      AgentAssist::AgentAssistStore.set_enabled(comm:, enabled:, profile_id:)

      if enabled && AgentAssist::AgentAssistStore.get_conversation_name(comm:, profile_id:).blank?
        handoff = Chatbot::HandoffService.new(comm:)
        if handoff.can_complete_handoff?
          handoff.enqueue_complete_handoff
        else
          AgentAssist::CreateConversationWorker.perform_async(comm.class.name, comm.id)
        end
      end

      enabled
    end

    def hangup_websockets(streams)
      streams.each do |stream|
        next unless stream.agent_assist?
        next unless stream.connected?

        ::Nexmo::ApplicationClientWorker.perform_async(stream.region, :hangup, stream.provider_id)
      end
    end

    def transcribe_participant_joined(participant)
      return unless participant.present?

      comm = participant.call
      return unless comm.realtime_transcripts_enabled?

      message = AgentAssist::TranscriptMessage.participant_joined(name: participant.name)
      AgentAssist::SaveTranscriptWorker.perform_async(comm.id, message.to_h)
    end

    def transcribe_participant_left(participant)
      return unless participant.present?

      comm = participant.call
      return unless comm.realtime_transcripts_enabled?

      message = AgentAssist::TranscriptMessage.participant_left(name: participant.name)
      AgentAssist::SaveTranscriptWorker.perform_async(comm.id, message.to_h)
    end

    # whenever agent assist is enabled, make sure all connected participants have external participants
    def create_external_comm_participants(comm, delay)
      return unless comm&.any_agent_assist_enabled?

      comm.participants.each do |participant|
        next unless participant.connected?
        next unless participant.support_agent_assist?
        next unless AgentAssist::AgentAssistStore.external_participant_missing?(comm:, participant:)

        AgentAssist::CreateParticipantWorker.perform_in(delay, participant.class.name, participant.id)
      end
    end

    def summarize_conversation_by_agent(participant, for_wrap_up: true)
      return unless participant.agent?

      comm = participant.try(:call) || participant.try(:chat)
      generate_for_wrap_up = for_wrap_up && ::WrapUp::Predicates.summary_enabled?(participant)
      save_to_crm = AgentAssist::ConversationService.save_summary_to_crm?(comm:)

      return unless generate_for_wrap_up || save_to_crm

      AgentAssist::ConversationService.summarize_conversation(participant:)
      AgentAssist::ConversationService.post_summary_to_crm(participant:) if save_to_crm
    end

    def complete_obsolete_conversations(participant:)
      return unless participant.agent_assist_profile_id

      AgentAssist::CompleteObsoleteConversationWorker.perform_async(participant.class.name, participant.id)

      comm = participant.try(:call) || participant.try(:chat)
      return unless comm.va_assigned?

      # when we are transferring from a human agent to a queue with a virtual agent that shares the same profile, we
      # briefly have two conversations going with the same profile, the ongoing conversation from the human agent (that
      # needs to stick around to support generating a summary for wrap-up), and a new session for the va.
      #
      # since we need to generate a summary for the human agent conversation, we wait until here to do this cleanup.

      AgentAssist::CompleteConversationProfileWorker.perform_async(comm.class.name, comm.id, participant.agent_assist_profile_id)
    end

    def create_wrap_up_conversation(participant:)
      AgentAssist::CreateWrapUpConversationWorker.perform_async(participant.class.name, participant.id)

      completion_delay = 3.hours + participant.wrap_up_time_after_chat
      AgentAssist::CompleteWrapUpConversationWorker.perform_in(completion_delay, participant.class.name, participant.id)
    end

    def complete_conversation_after_wrap_up(participant:)
      AgentAssist::CompleteWrapUpConversationWorker.perform_async(participant.class.name, participant.id)
    end
  end
end

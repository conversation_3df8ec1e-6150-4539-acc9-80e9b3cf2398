# frozen_string_literal: true

module AgentAssist
  module Ccai
    class InsightsListener
      def on_call_status_changed(call, _prev_status, _current_status)
        return unless call.ended?
        return unless Company.current.ccai_insight_settings.enabled &&
                      Company.current.ccai_insight_settings.send_call_recordings

        # delay to ensure voip events are handled before this runs
        AgentAssist::Ccai::IngestCallRecordingsWorker.perform_in(1.minute, call.id)
      end
    end
  end
end

# frozen_string_literal: true

class Call
  class PostSessionTransferServiceListener
    def on_call_participant_status_changed(pid, _prev_status, curr_status)
      return unless ['connected', 'finished', 'failed'].include?(curr_status)

      participant = CallParticipant.find_by_id(pid)
      return if participant.blank?
      return unless participant.virtual_agent? && participant.post_session?

      service = CallService::PostSessionTransferService.new(participant.call)

      if curr_status == 'connected'
        service.post_session_connected
      else
        service.post_session_ended
      end
    end
  end
end

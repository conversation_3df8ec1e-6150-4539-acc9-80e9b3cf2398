class Call
  class VoicemailServiceListener
    def on_call_recording_saved(recording, prev_url)
      return unless recording.voicemail?

      # reloads the call not to modify the call from the event parameter
      call = Call.find_by_id(recording.call_id)
      return unless call

      service = CallService::VoicemailService.new(call)
      service.recording_saved
    end

    def on_call_participant_status_changed(pid, prev_status, curr_status)
      return unless ['finished', 'voicemail_finished'].include?(curr_status)

      participant = CallParticipant.find_by_id(pid)
      return unless participant.present?

      service = CallService::VoicemailService.new(participant.call)
      case curr_status.to_sym
      when :finished
        return unless participant.call&.kind_of_voicemail?

        service.participant_finished(participant: participant)
      when :voicemail_finished
        service.participant_voicemail_finished(participant: participant)
      end
    end
  end
end

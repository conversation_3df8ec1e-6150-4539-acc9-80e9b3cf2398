module ErrorCode
  USER_CANNOT_CHANGE_STATUS_IN_CHAT = 1001.freeze
  USER_CANNOT_CHANGE_STATUS_IN_CALL = 1002.freeze
  SMS_OPEN_DOWNLOAD_APP_NOT_ENABLED = 1003.freeze
  EMERGENCY_DIALING_SETTING_DISABLED = 1004.freeze
  CALL_IS_NOT_CONNECTED = 1005.freeze
  NOTE_PARAMETER_MISSING = 1006.freeze
  CALL_TELNYX_ERROR = 1007.freeze
  ADMIN_CANNOT_CHANGE_USER_STATUS_IN_CHAT = 1008.freeze
  ADMIN_CANNOT_CHANGE_USER_STATUS_IN_CALL = 1009.freeze
  USER_CANNOT_CHANGE_STATUS_WRAP_UP_EXCEEDED = 1010.freeze
  ADMIN_CANNOT_CHANGE_USER_STATUS_WRAP_UP_EXCEEDED = 1011.freeze

  DEFAULT_MESSAGES = {
    USER_CANNOT_CHANGE_STATUS_IN_CHAT => "Your status cannot be changed while you are in-chat",
    USER_CANNOT_CHANGE_STATUS_IN_CALL => "Your status cannot be changed while you are in-call, or required to enter disposition notes or codes",
    SMS_OPEN_DOWNLOAD_APP_NOT_ENABLED => "SMS to open/download App is not enabled",
    EMERGENCY_DIALING_SETTING_DISABLED => "Emergency dialing setting is disabled",
    CALL_IS_NOT_CONNECTED => "Call is not connected",
    NOTE_PARAMETER_MISSING => "param is missing or the value is empty: note",
    CALL_TELNYX_ERROR => "Internal Server Error",
    ADMIN_CANNOT_CHANGE_USER_STATUS_IN_CHAT => "User status cannot be changed while user's in-chat",
    ADMIN_CANNOT_CHANGE_USER_STATUS_IN_CALL => "User status cannot be changed while user's in-call, or required to enter disposition notes or codes",
    USER_CANNOT_CHANGE_STATUS_WRAP_UP_EXCEEDED => "Your status cannot be changed while your next status is wrap-up exceeded",
    ADMIN_CANNOT_CHANGE_USER_STATUS_WRAP_UP_EXCEEDED => "User status cannot be changed while user's next status is wrap-up exceeded"
  }.freeze
end

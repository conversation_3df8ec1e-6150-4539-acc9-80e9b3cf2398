JIRA Ticket: https://ujetcs.atlassian.net/browse/

## Summary

## Related Issues

## Final Review Checklist
See [documentation](https://ujetcs.atlassian.net/wiki/spaces/ENG/pages/1112277222/UJET+s+Software+Development+Life+Cycle+SDLC#UJET'sSoftwareDevelopmentLifeCycle(SDLC)-ujet-server)

For final reviews, merges of project branches into master or release branch, add `ujet-team-engineering-leads` to review PR

For final reviewer, check each of the boxes below if meeting expectations.


- [ ] Did not add references to ENV variables in Rails code outside of web/config/ directory or specific to rake tasks
- [ ] ENV vars have sane defaults
- [ ] Sane use of Redis. No excessive looping, bulk updates done via pipeline. No KEYS (redis.keys) commands
- [ ] Code does not move AND remove sidekiq worker in same release
- [ ] New feature flags have sane defaults defined in feature_flag_defaults.json
- [ ] Does not delete feature code AND database schema in the same release
- [ ] Migrations do not require PTOSC or otherwise have necessary CM and UJET tickets documenting PTOSC execution
- [ ] PTOSC migrations do not handle more than one table each
- [ ] PTOSC migrations making more than one change to a table use the `bulk: true` flag
- [ ] Migrations all check if column or index exists before performing actions like adding or removing
- [ ] Migrations do not rename tables
- [ ] No large data migrations in db/data that should be done as background jobs or rake task
- [ ] Migrations are backward compatible
- [ ] Does not remove tests (without ticket and prior approval)
- [ ] No new references to ujet in generated text, files, etc.. All company/tenant references should be using
  white-labeled safe values where appropriate. (brand_name)
- [ ] No PII should be included in application logs, including API parameters and data synced to Firebase
- [ ] No credential information in API responses, application logs, and data synced to Firebase (secrets, tokens, keys, service account json, etc.)
- [ ] All AI bot's comments are addressed (dismissed or replied to)
- [ ] If this is a project merge to master PR, verify that test is added. If the test is not added, verify that follow up JIRA ticket is created

# Intended to be copied to managed repos via .github/workflows/push_workflows.yml

name: Notify Release Management When PR is Ready

on:
  pull_request:
    types: [labeled]
    branches:
      - 'release/*'
      - 'main'
      - 'master'

jobs:
  ujet-release-management-shared-merge-ready-notification:
    if: ${{ github.event.label.name == 'merge-requested' }}
    uses: UJET/ujet-release-management/.github/workflows/shared_merge_ready_notification.yml@main
    secrets: inherit
name: <PERSON>t YAML files

on:
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          always-auth: true
          registry-url: 'https://npm.pkg.github.com'
          scope: '@ujet'
      - name: 'Checkout repository on branch: ${{ github.REF }}'
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.HEAD_REF }}
          clean: true
      - name: Cache node modules
        uses: actions/cache@v4
        id: node-cache
        env:
          cache-name: cache-node-modules
        with:
          path: |
            node_modules
            /home/<USER>/.cache/Cypress
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Install dependencies
        if: steps.node-cache.outputs.cache-hit != 'true'
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Lint YAML files
        run: npm run lint:yaml

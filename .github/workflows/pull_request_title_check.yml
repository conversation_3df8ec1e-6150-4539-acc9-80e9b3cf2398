# Intended to be copied to managed repos via .github/workflows/push_workflows.yml
name: Check PR Title For Jira Ticket

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
on:
  pull_request:
    types: [reopened, opened, edited, synchronize]

jobs:
  ujet-release-management-pull-request-title-check:
    uses: UJET/ujet-release-management/.github/workflows/shared_pull_request_title_check.yml@main
    secrets: inherit

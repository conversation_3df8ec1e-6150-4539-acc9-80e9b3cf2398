name: Sync branch to master
on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Destination Branch'
        default: ''
        required: true

jobs:
  sync_branch_with_master:
    runs-on: ubuntu-latest
    steps:
      - name: Set up vars
        run: |
          echo "merge_branch_name=merge/master-to-${{ github.event.inputs.branch }}-$(date +%F)" >> $GITHUB_ENV

      - name: Checkout branch
        uses: actions/checkout@v2
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Create merge branch
        run: |
          git config user.name github-actions
          git config user.email <EMAIL>
          git fetch --unshallow origin ${{ github.event.inputs.branch }}
          git checkout -b ${{ env.merge_branch_name }}
      
      - name: Attempt to merge master
        run: |
          git pull origin master
    
      - name: Push merge branch
        run: |
          git push origin ${{ env.merge_branch_name }}
      
      - name: Create pull request
        uses: repo-sync/pull-request@v2
        with:
          source_branch: ${{ env.merge_branch_name }}
          destination_branch: ${{ github.event.inputs.branch }}
          pr_title: ${{ env.merge_branch_name }}             
          pr_body: |
                Merge master
                - Auto-generated by github action
          pr_reviewer: ${{ github.actor }}
          github_token: ${{ secrets.GITHUB_TOKEN }}

  create_merge_conflict_pr:
    if: ${{ failure() }}
    needs: sync_branch_with_master
    runs-on: ubuntu-latest
    steps:
    - name: Set up vars
      run: |
        echo "merge_branch_name=merge/master-to-${{ github.event.inputs.branch }}-$(date +%F)" >> $GITHUB_ENV

    # This will check out the master branch
    - name: Check out Branch
      uses: actions/checkout@v2
      with:
        ref: master

    - name: Verify destination branch exists
      run: |
        git ls-remote --exit-code --heads origin ${{ github.event.inputs.branch }}
        exit $?

    - name: Create merge branch
      run: |
        git config user.name github-actions
        git config user.email <EMAIL>
        git checkout -b ${{ env.merge_branch_name }}
        git push origin ${{ env.merge_branch_name }}

    - name: Create pull request
      uses: repo-sync/pull-request@v2
      with:
        source_branch: ${{ env.merge_branch_name }}
        destination_branch: ${{ github.event.inputs.branch }}
        pr_title: ${{ env.merge_branch_name }}             
        pr_body: |
              Merge master
              - Auto-generated by github action
        pr_reviewer: ${{ github.actor }}
        github_token: ${{ secrets.GITHUB_TOKEN }}

name: SonarQube

on:
  pull_request:
    branches: [ master ]

jobs:
  sonarqube_analysis:
    if: github.event.pull_request.closed == false
    runs-on: load
    steps:
      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          always-auth: true
          registry-url: 'https://npm.pkg.github.com'
          scope: '@ujet'
      - name: 'Checkout repository on branch: ${{ github.REF }}'
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          ref: ${{ github.HEAD_REF }}
          clean: true
      - name: Cache node modules
        uses: actions/cache@v2
        id: node-cache
        env:
          cache-name: cache-node-modules
        with:
          path: |
            node_modules
            /home/<USER>/.cache/Cypress
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - name: Install dependencies
        if: steps.node-cache.outputs.cache-hit != 'true'
        run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
      - name: Delete temp project
        run: curl -X POST -u "${{ secrets.SONARQUBE_TOKEN }}:" "https://sonarqube.lod.ujet.xyz/api/projects/delete?project=${{ github.event.repository.name }}-${{ github.event.pull_request.number }}"
      - name: Run an analysis of the PR
        run: /home/<USER>/sonar-scanner/bin/sonar-scanner
          -Dsonar.projectKey=${{ github.event.repository.name }}-${{ github.event.pull_request.number }}
          -Dsonar.host.url=https://sonarqube.lod.ujet.xyz/
          -Dsonar.login=${{ secrets.SONARQUBE_TOKEN }}
          -Dsonar.scm.provider=git
      - name: Run SonarQube analysis script
        env:
          SONAR_TOKEN: "${{ secrets.SONARQUBE_TOKEN }}:"
          DIFF: "diff"
          LIMIT: "10"
        run: sh node_modules/@ujet/sonar-utils/sonar.sh ${{ github.event.pull_request.number }} ${{ github.event.repository.name }} ${{ env.SONAR_TOKEN }} $GITHUB_BASE_REF ${{ env.DIFF }} ${{ env.LIMIT }}
      - name: Post results to PR
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run:
          npx github-commenter comment -n ${{ github.event.pull_request.number }} -r ${{ github.event.repository.name }} -c ./sonar_results.md
      - name: Exit analysis
        if: ${{ env.SUCCESSFUL_ANALYSIS == 'false' }}
        run: exit 1

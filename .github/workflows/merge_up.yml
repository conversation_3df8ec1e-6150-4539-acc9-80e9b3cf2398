# Intended to be copied to managed repos via .github/workflows/push_workflows.yml
name: Merge Up

on:
  workflow_dispatch:
    inputs:
      event_ref:
        description: The latest tag that needs to be merged up. Example v3.10.8'     
        required: true
        default: ''
        type: string
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'

jobs:
  ujet-release-management-shared-merge-up:
    uses: UJET/ujet-release-management/.github/workflows/shared_merge_up.yml@main
    secrets: inherit
    with:
      event_ref: ${{ inputs.event_ref }}
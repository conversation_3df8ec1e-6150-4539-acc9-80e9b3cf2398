# Intended to be copied to managed repos via .github/workflows/push_workflows.yml
name: Check PR approvals by at least one UJET engineer

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
on:
  pull_request_review:
    types: [submitted]

jobs:
  ujet-release-management-pull-request-title-check:
    uses: UJET/ujet-release-management/.github/workflows/shared_ujet_employee_approval.yml@main
    secrets: inherit

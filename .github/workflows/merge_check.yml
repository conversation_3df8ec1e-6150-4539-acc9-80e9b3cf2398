# Intended to be copied to managed repos via .github/workflows/push_workflows.yml

name: PR Merge Check

on: issue_comment

concurrency:
  group: merge-check-${{ github.event.issue.number }}
  cancel-in-progress: true

jobs:
  ujet-release-management-shared-merge-check:
    if: ${{ github.event.issue.pull_request && startsWith(github.event.comment.body, '/merge-check') }}
    uses: UJET/ujet-release-management/.github/workflows/shared_merge_check.yml@main
    secrets: inherit

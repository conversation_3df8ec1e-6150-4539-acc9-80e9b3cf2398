# Intended to be copied to managed repos via .github/workflows/push_workflows.yml

name: Cut Release

on: 
  workflow_dispatch:
    inputs:
      version:
        description: Version to cut; example 4.0'     
        required: true
        default: ''
        type: string

jobs:
  ujet-release-management-shared-release-cut:
    uses: UJET/ujet-release-management/.github/workflows/shared_release_cut.yml@main
    secrets: inherit
    with:
      version: ${{ inputs.version }}